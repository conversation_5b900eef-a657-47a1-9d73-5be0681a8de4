/*********************************************************************************************/
【*】程序简介

-工程名称：DAC904_HighSpeedDAC
-实验平台:  EP4CE15F17C8
-软件版本：Quartus II 64-Bit Version 11.0
	工程文件位于"\DAC904_HighSpeedDAC_Vx.x\prj"目录下，用Quartus II软件打开DAC904_HighSpeedDAC.qpf即可

【 ！】功能简介：
控制DAC902模块输出正弦波

注：也可输出三角波，或者方波

将"rom_14B_Sin.v"中的，这段代码，三选一，选择对应的输出波形，重新编译，下载到FPGA板，用示波器即可观察到DAC模块输出
波形数据也可自定义，\rtl\ROM\ROM_14B_SIN路径下的MIF文件中，存储的就是自定义波形数据，也可根据自己需要更改
//		altsyncram_component.init_file = "../../ROM/ROM_14B_SIN/ROM_14B_SQU.mif",	//调用方波波形
//		altsyncram_component.init_file = "../../ROM/ROM_14B_SIN/ROM_14B_TRIANG.mif",	//调用三角波波形
		altsyncram_component.init_file = "../../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",	//调用正弦波波形


【*】注意事项：



【 ！】实验操作：

1、使用排线连接FPGA主控板，或直接将模块插到FPGA主控板上
2、下载程序到FPGA板中
3、对FPGA主控板从新上电
4、使用SMA屏蔽线连接模块与示波器，调整示波器时间档位到1uS/DIV,即可观察到正弦波输出


/*********************************************************************************************/

【*】 引脚分配
	
DAC904模块	FPGA驱动板(EP4CE15F17C8N)	
5V		5V
GND		GND
CLK		P11
D13		PIN_M10
D12		PIN_M9
D11		PIN_N9
D10		PIN_P9
D9		PIN_L7
D8		PIN_M8
D7		PIN_N8
D6		PIN_P8
D5		PIN_M7
D4		PIN_M6
D3		PIN_P6
D2		PIN_N6
D1		PIN_N5
D0		PIN_K6

/*********************************************************************************************/

【*】 版本

-程序版本：1.6
-更新日期：2023-07-xx



/*********************************************************************************************/

【*】 联系我们

-淘宝店铺    https://kvdz.taobao.com

/*********************************************************************************************/