/**
  ******************************************************************************
  * @file    dds_dac904.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   基于DAC904的高性能DDS信号发生器实现
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "dds_dac904.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/**
  * @brief  DDS DAC904全局句柄
  */
DDS_DAC904_Handle_t g_dds_dac904_handle;

/* Private function prototypes -----------------------------------------------*/
static DDS_DAC904_Status_t DDS_DAC904_UpdateFrequencyWord(void);
static DDS_DAC904_Status_t DDS_DAC904_UpdateAmplitudeScale(void);
static DDS_DAC904_Status_t DDS_DAC904_UpdateWaveTable(void);
static uint16_t DDS_DAC904_ApplyAmplitudeAndOffset(uint16_t raw_data);

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  DDS DAC904初始化
  * @param  config: 配置参数指针
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_Init(DDS_DAC904_Config_t* config)
{
    // 参数检查
    if (config == NULL) {
        return DDS_DAC904_PARAM_ERROR;
    }

    // 检查频率范围
    if (!DDS_DAC904_IS_VALID_FREQ(config->frequency)) {
        return DDS_DAC904_PARAM_ERROR;
    }

    // 检查幅度范围
    if (!DDS_DAC904_IS_VALID_AMPLITUDE(config->amplitude)) {
        return DDS_DAC904_PARAM_ERROR;
    }

    // 清零句柄
    memset(&g_dds_dac904_handle, 0, sizeof(DDS_DAC904_Handle_t));

    // 保存配置
    g_dds_dac904_handle.config = *config;

    // 初始化DAC904
    DAC904_Config_t dac_config = {
        .mode = DAC904_MODE_TIMER,
        .clock_frequency = config->sample_rate,
        .gpio_speed = DAC904_GPIO_SPEED_HIGH,
        .enable_clock_output = true   // 必须启用时钟输出，DAC904需要时钟脉冲锁存数据
    };

    if (DAC904_Init(&dac_config) != DAC904_OK) {
        return DDS_DAC904_ERROR;
    }

    // 初始化DDS参数
    g_dds_dac904_handle.phase_accumulator = 0;
    g_dds_dac904_handle.sample_count = 0;
    g_dds_dac904_handle.error_count = 0;
    g_dds_dac904_handle.is_initialized = false;
    g_dds_dac904_handle.is_running = false;

    // 更新频率控制字
    if (DDS_DAC904_UpdateFrequencyWord() != DDS_DAC904_OK) {
        DAC904_DeInit();
        return DDS_DAC904_ERROR;
    }

    // 更新幅度缩放
    if (DDS_DAC904_UpdateAmplitudeScale() != DDS_DAC904_OK) {
        DAC904_DeInit();
        return DDS_DAC904_ERROR;
    }

    // 更新波形表
    if (DDS_DAC904_UpdateWaveTable() != DDS_DAC904_OK) {
        DAC904_DeInit();
        return DDS_DAC904_ERROR;
    }

    // 初始化完成
    g_dds_dac904_handle.is_initialized = true;

    // 设置初始输出为中间值
    DAC904_WriteData(DAC904_DATA_MID);

    return DDS_DAC904_OK;
}

/**
  * @brief  DDS DAC904反初始化
  * @param  None
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_DeInit(void)
{
    // 停止DDS输出
    if (g_dds_dac904_handle.is_running) {
        DDS_DAC904_Stop();
    }

    // 反初始化DAC904
    DAC904_DeInit();

    // 清除句柄
    memset(&g_dds_dac904_handle, 0, sizeof(DDS_DAC904_Handle_t));

    return DDS_DAC904_OK;
}

/**
  * @brief  设置DDS频率
  * @param  frequency: 目标频率 (Hz)
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_SetFrequency(uint32_t frequency)
{
    // 检查初始化状态
    if (!g_dds_dac904_handle.is_initialized) {
        return DDS_DAC904_NOT_INITIALIZED;
    }

    // 检查频率范围
    if (!DDS_DAC904_IS_VALID_FREQ(frequency)) {
        return DDS_DAC904_PARAM_ERROR;
    }

    // 更新配置
    g_dds_dac904_handle.config.frequency = frequency;

    // 重新计算频率控制字
    return DDS_DAC904_UpdateFrequencyWord();
}

/**
  * @brief  设置DDS幅度
  * @param  amplitude: 目标幅度 (0.0-5.0V)
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_SetAmplitude(float amplitude)
{
    // 检查初始化状态
    if (!g_dds_dac904_handle.is_initialized) {
        return DDS_DAC904_NOT_INITIALIZED;
    }

    // 检查幅度范围
    if (!DDS_DAC904_IS_VALID_AMPLITUDE(amplitude)) {
        return DDS_DAC904_PARAM_ERROR;
    }

    // 更新配置
    g_dds_dac904_handle.config.amplitude = amplitude;

    // 重新计算幅度缩放
    return DDS_DAC904_UpdateAmplitudeScale();
}

/**
  * @brief  设置DDS波形类型
  * @param  wave_type: 波形类型
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_SetWaveType(Wave_Type_14bit_t wave_type)
{
    // 检查初始化状态
    if (!g_dds_dac904_handle.is_initialized) {
        return DDS_DAC904_NOT_INITIALIZED;
    }

    // 检查波形类型
    if (!IS_VALID_WAVE_TYPE(wave_type)) {
        return DDS_DAC904_PARAM_ERROR;
    }

    // 更新配置
    g_dds_dac904_handle.config.wave_type = wave_type;

    // 更新波形表
    return DDS_DAC904_UpdateWaveTable();
}

/**
  * @brief  设置DDS直流偏移
  * @param  offset: 直流偏移 (0.0-5.0V)
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_SetOffset(float offset)
{
    // 检查初始化状态
    if (!g_dds_dac904_handle.is_initialized) {
        return DDS_DAC904_NOT_INITIALIZED;
    }

    // 检查偏移范围
    if (offset < 0.0f || offset > 5.0f) {
        return DDS_DAC904_PARAM_ERROR;
    }

    // 更新配置
    g_dds_dac904_handle.config.offset = offset;

    // 计算直流偏移值
    g_dds_dac904_handle.dc_offset = DDS_DAC904_VOLTAGE_TO_DAC(offset);

    return DDS_DAC904_OK;
}

/**
  * @brief  启动DDS输出
  * @param  None
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_Start(void)
{
    // 检查初始化状态
    if (!g_dds_dac904_handle.is_initialized) {
        return DDS_DAC904_NOT_INITIALIZED;
    }

    // 如果已经运行，直接返回
    if (g_dds_dac904_handle.is_running) {
        return DDS_DAC904_OK;
    }

    // 重置相位累加器
    g_dds_dac904_handle.phase_accumulator = 0;
    g_dds_dac904_handle.sample_count = 0;

    // 启动DAC904定时器
    if (DAC904_StartTimer(g_dds_dac904_handle.config.sample_rate) != DAC904_OK) {
        return DDS_DAC904_ERROR;
    }

    g_dds_dac904_handle.is_running = true;

    return DDS_DAC904_OK;
}

/**
  * @brief  停止DDS输出
  * @param  None
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_Stop(void)
{
    // 检查初始化状态
    if (!g_dds_dac904_handle.is_initialized) {
        return DDS_DAC904_NOT_INITIALIZED;
    }

    // 停止DAC904定时器
    DAC904_StopTimer();

    g_dds_dac904_handle.is_running = false;

    // 输出中间值
    DAC904_WriteData(DAC904_DATA_MID);

    return DDS_DAC904_OK;
}

/**
  * @brief  生成下一个DDS采样点
  * @param  None
  * @retval uint16_t 14位DAC数据
  * @note   此函数在定时器中断中调用
  */
uint16_t DDS_DAC904_GenerateSample(void)
{
    // 检查初始化状态
    if (!g_dds_dac904_handle.is_initialized || !g_dds_dac904_handle.is_running) {
        return DAC904_DATA_MID;
    }

    // 获取波形表索引
    uint16_t table_index = DDS_DAC904_PHASE_TO_INDEX(g_dds_dac904_handle.phase_accumulator);

    // 从波形表获取原始数据
    uint16_t raw_data;
    if (g_dds_dac904_handle.current_table != NULL) {
        raw_data = g_dds_dac904_handle.current_table[table_index];
    } else {
        raw_data = WAVE_14BIT_CENTER;
        g_dds_dac904_handle.error_count++;
    }

    // 应用幅度缩放和偏移
    uint16_t output_data = DDS_DAC904_ApplyAmplitudeAndOffset(raw_data);

    // 更新相位累加器
    g_dds_dac904_handle.phase_accumulator += g_dds_dac904_handle.frequency_word;

    // 更新采样计数
    g_dds_dac904_handle.sample_count++;

    return output_data;
}

/**
  * @brief  DDS定时器中断处理函数
  * @param  None
  * @retval None
  * @note   此函数需要在定时器中断中调用
  */
void DDS_DAC904_TimerHandler(void)
{
    // 检查并清除定时器中断标志
    if (TIM_GetITStatus(TIM2, TIM_IT_Update) != RESET) {
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);

        // 生成新的采样点
        uint16_t dac_data = DDS_DAC904_GenerateSample();

        // 输出到DAC904
        DAC904_WriteData(dac_data);
    }
}

/**
  * @brief  获取DDS状态信息
  * @param  None
  * @retval DDS_DAC904_Handle_t* 句柄指针
  */
DDS_DAC904_Handle_t* DDS_DAC904_GetHandle(void)
{
    return &g_dds_dac904_handle;
}

/**
  * @brief  重置DDS相位累加器
  * @param  None
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_ResetPhase(void)
{
    // 检查初始化状态
    if (!g_dds_dac904_handle.is_initialized) {
        return DDS_DAC904_NOT_INITIALIZED;
    }

    g_dds_dac904_handle.phase_accumulator = 0;

    return DDS_DAC904_OK;
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  更新频率控制字
  * @param  None
  * @retval DDS_DAC904_Status_t 操作状态
  */
static DDS_DAC904_Status_t DDS_DAC904_UpdateFrequencyWord(void)
{
    // 计算32位频率控制字
    g_dds_dac904_handle.frequency_word = DDS_DAC904_CALC_FREQ_WORD(
        g_dds_dac904_handle.config.frequency,
        g_dds_dac904_handle.config.sample_rate
    );

    g_dds_dac904_handle.phase_increment = g_dds_dac904_handle.frequency_word;

    return DDS_DAC904_OK;
}

/**
  * @brief  更新幅度缩放因子
  * @param  None
  * @retval DDS_DAC904_Status_t 操作状态
  */
static DDS_DAC904_Status_t DDS_DAC904_UpdateAmplitudeScale(void)
{
    // 计算幅度缩放因子 (0-16383)
    float amplitude_ratio = g_dds_dac904_handle.config.amplitude / DDS_DAC904_AMPLITUDE_MAX;
    g_dds_dac904_handle.amplitude_scale = (uint16_t)(amplitude_ratio * WAVE_14BIT_AMPLITUDE);

    return DDS_DAC904_OK;
}

/**
  * @brief  更新波形表指针
  * @param  None
  * @retval DDS_DAC904_Status_t 操作状态
  */
static DDS_DAC904_Status_t DDS_DAC904_UpdateWaveTable(void)
{
    // 获取波形表指针
    g_dds_dac904_handle.current_table = WaveTable_GetTable(g_dds_dac904_handle.config.wave_type);

    if (g_dds_dac904_handle.current_table == NULL) {
        return DDS_DAC904_ERROR;
    }

    return DDS_DAC904_OK;
}

/**
  * @brief  应用幅度缩放和偏移
  * @param  raw_data: 原始波形数据
  * @retval 缩放后的数据
  */
static uint16_t DDS_DAC904_ApplyAmplitudeAndOffset(uint16_t raw_data)
{
    // 将原始数据转换为有符号数 (以中心值为0)
    int32_t signed_data = (int32_t)raw_data - WAVE_14BIT_CENTER;

    // 应用幅度缩放
    signed_data = (signed_data * g_dds_dac904_handle.amplitude_scale) / WAVE_14BIT_AMPLITUDE;

    // 应用直流偏移
    int32_t output_data = signed_data + g_dds_dac904_handle.dc_offset;

    // 限制输出范围
    if (output_data < 0) {
        output_data = 0;
    } else if (output_data > DAC904_DATA_MAX) {
        output_data = DAC904_DATA_MAX;
    }

    return (uint16_t)output_data;
}

/************************ (C) COPYRIGHT DAC904移植项目组 *****END OF FILE****/
