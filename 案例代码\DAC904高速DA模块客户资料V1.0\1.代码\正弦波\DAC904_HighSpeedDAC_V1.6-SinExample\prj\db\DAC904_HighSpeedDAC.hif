Quartus II 64-Bit
Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version
15
1197
OFF
OFF
OFF
ON
ON
ON
FV_OFF
Level2
0
0
VRSM_ON
VHSM_ON
0
-- Start Library Paths --
-- End Library Paths --
-- Start VHDL Libraries --
-- End VHDL Libraries --
# entity
altpll
# storage
db|DAC904_HighSpeedDAC.(3).cnf
db|DAC904_HighSpeedDAC.(3).cnf
# case_insensitive
# source_file
altpll.tdf
1432cd46f66d9cd9c995c4e7995ff8
7
# user_parameter {
OPERATION_MODE
NORMAL
PARAMETER_UNKNOWN
USR
PLL_TYPE
AUTO
PARAMETER_UNKNOWN
USR
LPM_HINT
CBX_MODULE_PREFIX=pll200m
PARAMETER_UNKNOWN
USR
QUALIFY_CONF_DONE
OFF
PARAMETER_UNKNOWN
DEF
COMPENSATE_CLOCK
CLK0
PARAMETER_UNKNOWN
USR
SCAN_CHAIN
LONG
PARAMETER_UNKNOWN
DEF
PRIMARY_CLOCK
INCLK0
PARAMETER_UNKNOWN
DEF
INCLK0_INPUT_FREQUENCY
20000
PARAMETER_SIGNED_DEC
USR
INCLK1_INPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
GATE_LOCK_SIGNAL
NO
PARAMETER_UNKNOWN
DEF
GATE_LOCK_COUNTER
0
PARAMETER_UNKNOWN
DEF
LOCK_HIGH
1
PARAMETER_UNKNOWN
DEF
LOCK_LOW
1
PARAMETER_UNKNOWN
DEF
VALID_LOCK_MULTIPLIER
1
PARAMETER_UNKNOWN
DEF
INVALID_LOCK_MULTIPLIER
5
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_ON_LOSSCLK
OFF
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_ON_GATED_LOCK
OFF
PARAMETER_UNKNOWN
DEF
ENABLE_SWITCH_OVER_COUNTER
OFF
PARAMETER_UNKNOWN
DEF
SKIP_VCO
OFF
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_COUNTER
0
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_TYPE
AUTO
PARAMETER_UNKNOWN
DEF
FEEDBACK_SOURCE
EXTCLK0
PARAMETER_UNKNOWN
DEF
BANDWIDTH
0
PARAMETER_UNKNOWN
DEF
BANDWIDTH_TYPE
AUTO
PARAMETER_UNKNOWN
USR
SPREAD_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
DOWN_SPREAD
0
PARAMETER_UNKNOWN
DEF
SELF_RESET_ON_GATED_LOSS_LOCK
OFF
PARAMETER_UNKNOWN
DEF
SELF_RESET_ON_LOSS_LOCK
OFF
PARAMETER_UNKNOWN
USR
CLK9_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK8_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK7_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK6_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK5_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK4_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK3_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK2_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK1_MULTIPLY_BY
4
PARAMETER_SIGNED_DEC
USR
CLK0_MULTIPLY_BY
1
PARAMETER_SIGNED_DEC
USR
CLK9_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK8_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK7_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK6_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK5_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK4_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK3_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK2_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK1_DIVIDE_BY
1
PARAMETER_SIGNED_DEC
USR
CLK0_DIVIDE_BY
1
PARAMETER_SIGNED_DEC
USR
CLK9_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK8_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK7_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK6_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK5_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK4_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK3_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK2_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
USR
CLK0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
USR
CLK5_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK4_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK9_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK8_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK7_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK6_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK5_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK4_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK3_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK2_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK1_DUTY_CYCLE
50
PARAMETER_SIGNED_DEC
USR
CLK0_DUTY_CYCLE
50
PARAMETER_SIGNED_DEC
USR
CLK9_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK8_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK7_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK6_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK5_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK4_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK3_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK2_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK1_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK0_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK9_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK8_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK7_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK6_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK5_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK4_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK3_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK2_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK1_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK0_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
LOCK_WINDOW_UI
 0.05
PARAMETER_UNKNOWN
DEF
LOCK_WINDOW_UI_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
VCO_RANGE_DETECTOR_LOW_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
VCO_RANGE_DETECTOR_HIGH_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
DPA_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
DPA_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
DPA_DIVIDER
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK2_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK1_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK0_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK3_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK2_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK1_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK0_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK3_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK2_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK2_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK1_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK0_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
VCO_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
VCO_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
SCLKOUT0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
SCLKOUT1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
VCO_MIN
0
PARAMETER_UNKNOWN
DEF
VCO_MAX
0
PARAMETER_UNKNOWN
DEF
VCO_CENTER
0
PARAMETER_UNKNOWN
DEF
PFD_MIN
0
PARAMETER_UNKNOWN
DEF
PFD_MAX
0
PARAMETER_UNKNOWN
DEF
M_INITIAL
0
PARAMETER_UNKNOWN
DEF
M
0
PARAMETER_UNKNOWN
DEF
N
1
PARAMETER_UNKNOWN
DEF
M2
1
PARAMETER_UNKNOWN
DEF
N2
1
PARAMETER_UNKNOWN
DEF
SS
1
PARAMETER_UNKNOWN
DEF
C0_HIGH
0
PARAMETER_UNKNOWN
DEF
C1_HIGH
0
PARAMETER_UNKNOWN
DEF
C2_HIGH
0
PARAMETER_UNKNOWN
DEF
C3_HIGH
0
PARAMETER_UNKNOWN
DEF
C4_HIGH
0
PARAMETER_UNKNOWN
DEF
C5_HIGH
0
PARAMETER_UNKNOWN
DEF
C6_HIGH
0
PARAMETER_UNKNOWN
DEF
C7_HIGH
0
PARAMETER_UNKNOWN
DEF
C8_HIGH
0
PARAMETER_UNKNOWN
DEF
C9_HIGH
0
PARAMETER_UNKNOWN
DEF
C0_LOW
0
PARAMETER_UNKNOWN
DEF
C1_LOW
0
PARAMETER_UNKNOWN
DEF
C2_LOW
0
PARAMETER_UNKNOWN
DEF
C3_LOW
0
PARAMETER_UNKNOWN
DEF
C4_LOW
0
PARAMETER_UNKNOWN
DEF
C5_LOW
0
PARAMETER_UNKNOWN
DEF
C6_LOW
0
PARAMETER_UNKNOWN
DEF
C7_LOW
0
PARAMETER_UNKNOWN
DEF
C8_LOW
0
PARAMETER_UNKNOWN
DEF
C9_LOW
0
PARAMETER_UNKNOWN
DEF
C0_INITIAL
0
PARAMETER_UNKNOWN
DEF
C1_INITIAL
0
PARAMETER_UNKNOWN
DEF
C2_INITIAL
0
PARAMETER_UNKNOWN
DEF
C3_INITIAL
0
PARAMETER_UNKNOWN
DEF
C4_INITIAL
0
PARAMETER_UNKNOWN
DEF
C5_INITIAL
0
PARAMETER_UNKNOWN
DEF
C6_INITIAL
0
PARAMETER_UNKNOWN
DEF
C7_INITIAL
0
PARAMETER_UNKNOWN
DEF
C8_INITIAL
0
PARAMETER_UNKNOWN
DEF
C9_INITIAL
0
PARAMETER_UNKNOWN
DEF
C0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C4_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C5_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C6_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C7_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C8_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C9_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C0_PH
0
PARAMETER_UNKNOWN
DEF
C1_PH
0
PARAMETER_UNKNOWN
DEF
C2_PH
0
PARAMETER_UNKNOWN
DEF
C3_PH
0
PARAMETER_UNKNOWN
DEF
C4_PH
0
PARAMETER_UNKNOWN
DEF
C5_PH
0
PARAMETER_UNKNOWN
DEF
C6_PH
0
PARAMETER_UNKNOWN
DEF
C7_PH
0
PARAMETER_UNKNOWN
DEF
C8_PH
0
PARAMETER_UNKNOWN
DEF
C9_PH
0
PARAMETER_UNKNOWN
DEF
L0_HIGH
1
PARAMETER_UNKNOWN
DEF
L1_HIGH
1
PARAMETER_UNKNOWN
DEF
G0_HIGH
1
PARAMETER_UNKNOWN
DEF
G1_HIGH
1
PARAMETER_UNKNOWN
DEF
G2_HIGH
1
PARAMETER_UNKNOWN
DEF
G3_HIGH
1
PARAMETER_UNKNOWN
DEF
E0_HIGH
1
PARAMETER_UNKNOWN
DEF
E1_HIGH
1
PARAMETER_UNKNOWN
DEF
E2_HIGH
1
PARAMETER_UNKNOWN
DEF
E3_HIGH
1
PARAMETER_UNKNOWN
DEF
L0_LOW
1
PARAMETER_UNKNOWN
DEF
L1_LOW
1
PARAMETER_UNKNOWN
DEF
G0_LOW
1
PARAMETER_UNKNOWN
DEF
G1_LOW
1
PARAMETER_UNKNOWN
DEF
G2_LOW
1
PARAMETER_UNKNOWN
DEF
G3_LOW
1
PARAMETER_UNKNOWN
DEF
E0_LOW
1
PARAMETER_UNKNOWN
DEF
E1_LOW
1
PARAMETER_UNKNOWN
DEF
E2_LOW
1
PARAMETER_UNKNOWN
DEF
E3_LOW
1
PARAMETER_UNKNOWN
DEF
L0_INITIAL
1
PARAMETER_UNKNOWN
DEF
L1_INITIAL
1
PARAMETER_UNKNOWN
DEF
G0_INITIAL
1
PARAMETER_UNKNOWN
DEF
G1_INITIAL
1
PARAMETER_UNKNOWN
DEF
G2_INITIAL
1
PARAMETER_UNKNOWN
DEF
G3_INITIAL
1
PARAMETER_UNKNOWN
DEF
E0_INITIAL
1
PARAMETER_UNKNOWN
DEF
E1_INITIAL
1
PARAMETER_UNKNOWN
DEF
E2_INITIAL
1
PARAMETER_UNKNOWN
DEF
E3_INITIAL
1
PARAMETER_UNKNOWN
DEF
L0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
L1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
L0_PH
0
PARAMETER_UNKNOWN
DEF
L1_PH
0
PARAMETER_UNKNOWN
DEF
G0_PH
0
PARAMETER_UNKNOWN
DEF
G1_PH
0
PARAMETER_UNKNOWN
DEF
G2_PH
0
PARAMETER_UNKNOWN
DEF
G3_PH
0
PARAMETER_UNKNOWN
DEF
E0_PH
0
PARAMETER_UNKNOWN
DEF
E1_PH
0
PARAMETER_UNKNOWN
DEF
E2_PH
0
PARAMETER_UNKNOWN
DEF
E3_PH
0
PARAMETER_UNKNOWN
DEF
M_PH
0
PARAMETER_UNKNOWN
DEF
C1_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C2_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C3_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C4_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C5_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C6_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C7_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C8_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C9_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
CLK0_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK1_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK2_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK3_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK4_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK5_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK6_COUNTER
E0
PARAMETER_UNKNOWN
DEF
CLK7_COUNTER
E1
PARAMETER_UNKNOWN
DEF
CLK8_COUNTER
E2
PARAMETER_UNKNOWN
DEF
CLK9_COUNTER
E3
PARAMETER_UNKNOWN
DEF
L0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
L1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
M_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
N_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_COUNTER
E3
PARAMETER_UNKNOWN
DEF
EXTCLK2_COUNTER
E2
PARAMETER_UNKNOWN
DEF
EXTCLK1_COUNTER
E1
PARAMETER_UNKNOWN
DEF
EXTCLK0_COUNTER
E0
PARAMETER_UNKNOWN
DEF
ENABLE0_COUNTER
L0
PARAMETER_UNKNOWN
DEF
ENABLE1_COUNTER
L0
PARAMETER_UNKNOWN
DEF
CHARGE_PUMP_CURRENT
2
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_R
 1.000000
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_C
5
PARAMETER_UNKNOWN
DEF
CHARGE_PUMP_CURRENT_BITS
9999
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_R_BITS
9999
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_C_BITS
9999
PARAMETER_UNKNOWN
DEF
VCO_POST_SCALE
0
PARAMETER_UNKNOWN
DEF
CLK2_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
CLK1_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
CLK0_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
INTENDED_DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
PORT_CLKENA0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA4
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA5
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLKENA0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA2
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA3
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLK0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKBAD0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKBAD1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK0
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CLK1
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CLK2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK4
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK5
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK6
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK7
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK8
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK9
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_SCANDATA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANDATAOUT
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANDONE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCLKOUT1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_SCLKOUT0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_ACTIVECLOCK
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKLOSS
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_INCLK1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_INCLK0
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_FBIN
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PLLENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKSWITCH
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_ARESET
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_PFDENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANCLK
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANACLR
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANREAD
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANWRITE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_ENABLE0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_ENABLE1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_LOCKED
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CONFIGUPDATE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_FBOUT
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_PHASEDONE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASESTEP
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASEUPDOWN
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANCLKENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASECOUNTERSELECT
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_VCOOVERRANGE
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_VCOUNDERRANGE
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
M_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C0_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C1_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C2_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C3_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C4_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C5_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C6_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C7_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C8_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C9_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
CBXI_PARAMETER
pll200m_altpll
PARAMETER_UNKNOWN
USR
VCO_FREQUENCY_CONTROL
AUTO
PARAMETER_UNKNOWN
DEF
VCO_PHASE_SHIFT_STEP
0
PARAMETER_UNKNOWN
DEF
WIDTH_CLOCK
5
PARAMETER_SIGNED_DEC
USR
WIDTH_PHASECOUNTERSELECT
4
PARAMETER_UNKNOWN
DEF
USING_FBMIMICBIDIR_PORT
OFF
PARAMETER_UNKNOWN
DEF
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
SCAN_CHAIN_MIF_FILE
UNUSED
PARAMETER_UNKNOWN
DEF
SIM_GATE_LOCK_DEVICE_BEHAVIOR
OFF
PARAMETER_UNKNOWN
DEF
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
}
# used_port {
locked
-1
3
inclk
-1
3
clk
-1
3
areset
-1
3
scanwrite
-1
1
scanread
-1
1
scandata
-1
1
scanclk
-1
1
scanaclr
-1
1
configupdate
-1
1
clkswitch
-1
1
scanclkena
-1
2
pllena
-1
2
phaseupdown
-1
2
phasestep
-1
2
phasecounterselect
-1
2
pfdena
-1
2
fbin
-1
2
extclkena
-1
2
clkena
-1
2
}
# include_file {
stratixii_pll.inc
6797ab505ed70f1a221e4a213e16a6
cycloneii_pll.inc
c2ee779f89b3bc181df753ea85b3ef
stratix_pll.inc
a9a94c5be18105f7ae8c218a67ec9f7
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
}
# macro_sequence

# end
# entity
pll200m_altpll
# storage
db|DAC904_HighSpeedDAC.(4).cnf
db|DAC904_HighSpeedDAC.(4).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
db|pll200m_altpll.v
ac25544aadc34217b644468fa6ded
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
PhaseAccumulator
# storage
db|DAC904_HighSpeedDAC.(2).cnf
db|DAC904_HighSpeedDAC.(2).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.1_dds|rtl|phaseaccumulator.v
7c3592aa74e34f5228f826d436b27856
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
PhaseAcc_BITS_NUM32Mem_BITS_NUM14PhaseAcc_BITS_NUM32PhaseAcc_BITS_NUM32PhaseAcc_BITS_NUM32Mem_BITS_NUM14
# end
# entity
altsyncram
# storage
db|DAC904_HighSpeedDAC.(6).cnf
db|DAC904_HighSpeedDAC.(6).cnf
# case_insensitive
# source_file
altsyncram.tdf
f8b1b3e6a95fdea61cd86b5880d92761
7
# user_parameter {
BYTE_SIZE_BLOCK
8
PARAMETER_UNKNOWN
DEF
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
WIDTH_BYTEENA
1
PARAMETER_UNKNOWN
DEF
OPERATION_MODE
ROM
PARAMETER_UNKNOWN
USR
WIDTH_A
14
PARAMETER_SIGNED_DEC
USR
WIDTHAD_A
14
PARAMETER_SIGNED_DEC
USR
NUMWORDS_A
16384
PARAMETER_SIGNED_DEC
USR
OUTDATA_REG_A
CLOCK0
PARAMETER_UNKNOWN
USR
ADDRESS_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
OUTDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
WRCONTROL_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_B
1
PARAMETER_UNKNOWN
DEF
WIDTHAD_B
1
PARAMETER_UNKNOWN
DEF
NUMWORDS_B
1
PARAMETER_UNKNOWN
DEF
INDATA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
WRCONTROL_WRADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
RDCONTROL_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
ADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
OUTDATA_REG_B
UNREGISTERED
PARAMETER_UNKNOWN
DEF
BYTEENA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WRCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
ADDRESS_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
OUTDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
RDCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_BYTEENA_A
1
PARAMETER_SIGNED_DEC
USR
WIDTH_BYTEENA_B
1
PARAMETER_UNKNOWN
DEF
RAM_BLOCK_TYPE
AUTO
PARAMETER_UNKNOWN
DEF
BYTE_SIZE
8
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_MIXED_PORTS
DONT_CARE
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_A
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_B
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
INIT_FILE
./ROM/ROM_14B_SIN/ROM_14B_SIN.mif
PARAMETER_UNKNOWN
USR
INIT_FILE_LAYOUT
PORT_A
PARAMETER_UNKNOWN
DEF
MAXIMUM_DEPTH
0
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_INPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_INPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_OUTPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_OUTPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_A
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_B
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
ENABLE_ECC
FALSE
PARAMETER_UNKNOWN
DEF
WIDTH_ECCSTATUS
3
PARAMETER_UNKNOWN
DEF
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
CBXI_PARAMETER
altsyncram_6cb1
PARAMETER_UNKNOWN
USR
}
# used_port {
q_a
-1
3
clock0
-1
3
address_a
-1
3
wren_b
-1
1
wren_a
-1
1
addressstall_b
-1
1
addressstall_a
-1
1
aclr1
-1
1
aclr0
-1
1
rden_b
-1
2
rden_a
-1
2
data_b
-1
2
data_a
-1
2
clocken3
-1
2
clocken2
-1
2
clocken1
-1
2
clocken0
-1
2
clock1
-1
2
byteena_b
-1
2
byteena_a
-1
2
address_b
-1
2
}
# include_file {
altram.inc
ad5518b39ffd3cf1df377e6360d1c9b6
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
lpm_decode.inc
10da69a8bbd590d66779e7a142f73790
stratix_ram_block.inc
e3a03868917fb3dd57b6ed1dd195f22
altdpram.inc
2f9e6727b678ffd76e72bc5a95a2630
a_rdenreg.inc
3fcdce755959d5a8afbe64788d21fb
lpm_mux.inc
dd87bed9959d6126db09970164b7ba6
altrom.inc
192b74eafa8debf2248ea73881e77f91
}
# macro_sequence

# end
# entity
altsyncram
# storage
db|DAC904_HighSpeedDAC.(7).cnf
db|DAC904_HighSpeedDAC.(7).cnf
# case_insensitive
# source_file
altsyncram.tdf
f8b1b3e6a95fdea61cd86b5880d92761
7
# user_parameter {
BYTE_SIZE_BLOCK
8
PARAMETER_UNKNOWN
DEF
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
WIDTH_BYTEENA
1
PARAMETER_UNKNOWN
DEF
OPERATION_MODE
ROM
PARAMETER_UNKNOWN
USR
WIDTH_A
14
PARAMETER_SIGNED_DEC
USR
WIDTHAD_A
14
PARAMETER_SIGNED_DEC
USR
NUMWORDS_A
16384
PARAMETER_SIGNED_DEC
USR
OUTDATA_REG_A
CLOCK0
PARAMETER_UNKNOWN
USR
ADDRESS_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
OUTDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
WRCONTROL_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_B
1
PARAMETER_UNKNOWN
DEF
WIDTHAD_B
1
PARAMETER_UNKNOWN
DEF
NUMWORDS_B
1
PARAMETER_UNKNOWN
DEF
INDATA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
WRCONTROL_WRADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
RDCONTROL_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
ADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
OUTDATA_REG_B
UNREGISTERED
PARAMETER_UNKNOWN
DEF
BYTEENA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WRCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
ADDRESS_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
OUTDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
RDCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_BYTEENA_A
1
PARAMETER_SIGNED_DEC
USR
WIDTH_BYTEENA_B
1
PARAMETER_UNKNOWN
DEF
RAM_BLOCK_TYPE
AUTO
PARAMETER_UNKNOWN
DEF
BYTE_SIZE
8
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_MIXED_PORTS
DONT_CARE
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_A
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_B
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
INIT_FILE
../ROM/ROM_14B_SIN/ROM_14B_SIN.mif
PARAMETER_UNKNOWN
USR
INIT_FILE_LAYOUT
PORT_A
PARAMETER_UNKNOWN
DEF
MAXIMUM_DEPTH
0
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_INPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_INPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_OUTPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_OUTPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_A
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_B
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
ENABLE_ECC
FALSE
PARAMETER_UNKNOWN
DEF
WIDTH_ECCSTATUS
3
PARAMETER_UNKNOWN
DEF
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
CBXI_PARAMETER
altsyncram_kdb1
PARAMETER_UNKNOWN
USR
}
# used_port {
q_a
-1
3
clock0
-1
3
address_a
-1
3
wren_b
-1
1
wren_a
-1
1
addressstall_b
-1
1
addressstall_a
-1
1
aclr1
-1
1
aclr0
-1
1
rden_b
-1
2
rden_a
-1
2
data_b
-1
2
data_a
-1
2
clocken3
-1
2
clocken2
-1
2
clocken1
-1
2
clocken0
-1
2
clock1
-1
2
byteena_b
-1
2
byteena_a
-1
2
address_b
-1
2
}
# include_file {
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
altrom.inc
192b74eafa8debf2248ea73881e77f91
lpm_mux.inc
dd87bed9959d6126db09970164b7ba6
a_rdenreg.inc
3fcdce755959d5a8afbe64788d21fb
altdpram.inc
2f9e6727b678ffd76e72bc5a95a2630
altram.inc
ad5518b39ffd3cf1df377e6360d1c9b6
stratix_ram_block.inc
e3a03868917fb3dd57b6ed1dd195f22
lpm_decode.inc
10da69a8bbd590d66779e7a142f73790
}
# macro_sequence

# end
# entity
rom_14B_Sin
# storage
db|DAC904_HighSpeedDAC.(5).cnf
db|DAC904_HighSpeedDAC.(5).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.1_dds|rtl|rom|rom_14b_sin|rom_14b_sin.v
15c068ac05738b5ca5dbe195910e08c
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
altsyncram
# storage
db|DAC904_HighSpeedDAC.(8).cnf
db|DAC904_HighSpeedDAC.(8).cnf
# case_insensitive
# source_file
altsyncram.tdf
f8b1b3e6a95fdea61cd86b5880d92761
7
# user_parameter {
BYTE_SIZE_BLOCK
8
PARAMETER_UNKNOWN
DEF
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
WIDTH_BYTEENA
1
PARAMETER_UNKNOWN
DEF
OPERATION_MODE
ROM
PARAMETER_UNKNOWN
USR
WIDTH_A
14
PARAMETER_SIGNED_DEC
USR
WIDTHAD_A
14
PARAMETER_SIGNED_DEC
USR
NUMWORDS_A
16384
PARAMETER_SIGNED_DEC
USR
OUTDATA_REG_A
CLOCK0
PARAMETER_UNKNOWN
USR
ADDRESS_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
OUTDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
WRCONTROL_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_B
1
PARAMETER_UNKNOWN
DEF
WIDTHAD_B
1
PARAMETER_UNKNOWN
DEF
NUMWORDS_B
1
PARAMETER_UNKNOWN
DEF
INDATA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
WRCONTROL_WRADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
RDCONTROL_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
ADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
OUTDATA_REG_B
UNREGISTERED
PARAMETER_UNKNOWN
DEF
BYTEENA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WRCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
ADDRESS_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
OUTDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
RDCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_BYTEENA_A
1
PARAMETER_SIGNED_DEC
USR
WIDTH_BYTEENA_B
1
PARAMETER_UNKNOWN
DEF
RAM_BLOCK_TYPE
AUTO
PARAMETER_UNKNOWN
DEF
BYTE_SIZE
8
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_MIXED_PORTS
DONT_CARE
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_A
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_B
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
INIT_FILE
../../ROM/ROM_14B_SIN/ROM_14B_SIN.mif
PARAMETER_UNKNOWN
USR
INIT_FILE_LAYOUT
PORT_A
PARAMETER_UNKNOWN
DEF
MAXIMUM_DEPTH
0
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_INPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_INPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_OUTPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_OUTPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_A
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_B
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
ENABLE_ECC
FALSE
PARAMETER_UNKNOWN
DEF
WIDTH_ECCSTATUS
3
PARAMETER_UNKNOWN
DEF
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
CBXI_PARAMETER
altsyncram_vhb1
PARAMETER_UNKNOWN
USR
}
# used_port {
q_a
-1
3
clock0
-1
3
address_a
-1
3
wren_b
-1
1
wren_a
-1
1
addressstall_b
-1
1
addressstall_a
-1
1
aclr1
-1
1
aclr0
-1
1
rden_b
-1
2
rden_a
-1
2
data_b
-1
2
data_a
-1
2
clocken3
-1
2
clocken2
-1
2
clocken1
-1
2
clocken0
-1
2
clock1
-1
2
byteena_b
-1
2
byteena_a
-1
2
address_b
-1
2
}
# include_file {
lpm_mux.inc
dd87bed9959d6126db09970164b7ba6
a_rdenreg.inc
3fcdce755959d5a8afbe64788d21fb
altdpram.inc
2f9e6727b678ffd76e72bc5a95a2630
altram.inc
ad5518b39ffd3cf1df377e6360d1c9b6
stratix_ram_block.inc
e3a03868917fb3dd57b6ed1dd195f22
lpm_decode.inc
10da69a8bbd590d66779e7a142f73790
altrom.inc
192b74eafa8debf2248ea73881e77f91
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
}
# hierarchies {
rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component
}
# macro_sequence

# end
# entity
altsyncram_vhb1
# storage
db|DAC904_HighSpeedDAC.(9).cnf
db|DAC904_HighSpeedDAC.(9).cnf
# case_insensitive
# source_file
db|altsyncram_vhb1.tdf
7cc1876ced32aff7eb35e16371e220
7
# used_port {
q_a9
-1
3
q_a8
-1
3
q_a7
-1
3
q_a6
-1
3
q_a5
-1
3
q_a4
-1
3
q_a3
-1
3
q_a2
-1
3
q_a13
-1
3
q_a12
-1
3
q_a11
-1
3
q_a10
-1
3
q_a1
-1
3
q_a0
-1
3
clock0
-1
3
address_a9
-1
3
address_a8
-1
3
address_a7
-1
3
address_a6
-1
3
address_a5
-1
3
address_a4
-1
3
address_a3
-1
3
address_a2
-1
3
address_a13
-1
3
address_a12
-1
3
address_a11
-1
3
address_a10
-1
3
address_a1
-1
3
address_a0
-1
3
}
# memory_file {

0
}
# hierarchies {
rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated
}
# macro_sequence

# end
# entity
decode_c8a
# storage
db|DAC904_HighSpeedDAC.(10).cnf
db|DAC904_HighSpeedDAC.(10).cnf
# case_insensitive
# source_file
db|decode_c8a.tdf
2657cb20f272a1ce77f939f373b66e8
7
# used_port {
eq1
-1
3
eq0
-1
3
data0
-1
3
}
# hierarchies {
rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|decode_c8a:rden_decode
}
# macro_sequence

# end
# entity
mux_gob
# storage
db|DAC904_HighSpeedDAC.(11).cnf
db|DAC904_HighSpeedDAC.(11).cnf
# case_insensitive
# source_file
db|mux_gob.tdf
834d39428fc943f3b29498d4abb8c2ea
7
# used_port {
sel0
-1
3
result9
-1
3
result8
-1
3
result7
-1
3
result6
-1
3
result5
-1
3
result4
-1
3
result3
-1
3
result2
-1
3
result13
-1
3
result12
-1
3
result11
-1
3
result10
-1
3
result1
-1
3
result0
-1
3
data9
-1
3
data8
-1
3
data7
-1
3
data6
-1
3
data5
-1
3
data4
-1
3
data3
-1
3
data27
-1
3
data26
-1
3
data25
-1
3
data24
-1
3
data23
-1
3
data22
-1
3
data21
-1
3
data20
-1
3
data2
-1
3
data19
-1
3
data18
-1
3
data17
-1
3
data16
-1
3
data15
-1
3
data14
-1
3
data13
-1
3
data12
-1
3
data11
-1
3
data10
-1
3
data1
-1
3
data0
-1
3
}
# hierarchies {
rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|mux_gob:mux2
}
# macro_sequence

# end
# entity
DAC904_WriteModule
# storage
db|DAC904_HighSpeedDAC.(12).cnf
db|DAC904_HighSpeedDAC.(12).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.1_dds|rtl|dac904_writemodule.v
5856eb437edefda8122c8da7534505a
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence

# end
# entity
pll180m
# storage
db|DAC904_HighSpeedDAC.(13).cnf
db|DAC904_HighSpeedDAC.(13).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.1_dds|rtl|pll|pll180m|pll180m.v
6e215305e9bffc62346129224ae4633
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
altpll
# storage
db|DAC904_HighSpeedDAC.(14).cnf
db|DAC904_HighSpeedDAC.(14).cnf
# case_insensitive
# source_file
altpll.tdf
1432cd46f66d9cd9c995c4e7995ff8
7
# user_parameter {
OPERATION_MODE
NORMAL
PARAMETER_UNKNOWN
USR
PLL_TYPE
AUTO
PARAMETER_UNKNOWN
USR
LPM_HINT
CBX_MODULE_PREFIX=pll180m
PARAMETER_UNKNOWN
USR
QUALIFY_CONF_DONE
OFF
PARAMETER_UNKNOWN
DEF
COMPENSATE_CLOCK
CLK0
PARAMETER_UNKNOWN
USR
SCAN_CHAIN
LONG
PARAMETER_UNKNOWN
DEF
PRIMARY_CLOCK
INCLK0
PARAMETER_UNKNOWN
DEF
INCLK0_INPUT_FREQUENCY
20000
PARAMETER_SIGNED_DEC
USR
INCLK1_INPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
GATE_LOCK_SIGNAL
NO
PARAMETER_UNKNOWN
DEF
GATE_LOCK_COUNTER
0
PARAMETER_UNKNOWN
DEF
LOCK_HIGH
1
PARAMETER_UNKNOWN
DEF
LOCK_LOW
1
PARAMETER_UNKNOWN
DEF
VALID_LOCK_MULTIPLIER
1
PARAMETER_UNKNOWN
DEF
INVALID_LOCK_MULTIPLIER
5
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_ON_LOSSCLK
OFF
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_ON_GATED_LOCK
OFF
PARAMETER_UNKNOWN
DEF
ENABLE_SWITCH_OVER_COUNTER
OFF
PARAMETER_UNKNOWN
DEF
SKIP_VCO
OFF
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_COUNTER
0
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_TYPE
AUTO
PARAMETER_UNKNOWN
DEF
FEEDBACK_SOURCE
EXTCLK0
PARAMETER_UNKNOWN
DEF
BANDWIDTH
0
PARAMETER_UNKNOWN
DEF
BANDWIDTH_TYPE
AUTO
PARAMETER_UNKNOWN
USR
SPREAD_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
DOWN_SPREAD
0
PARAMETER_UNKNOWN
DEF
SELF_RESET_ON_GATED_LOSS_LOCK
OFF
PARAMETER_UNKNOWN
DEF
SELF_RESET_ON_LOSS_LOCK
OFF
PARAMETER_UNKNOWN
USR
CLK9_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK8_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK7_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK6_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK5_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK4_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK3_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK2_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK1_MULTIPLY_BY
18
PARAMETER_SIGNED_DEC
USR
CLK0_MULTIPLY_BY
1
PARAMETER_SIGNED_DEC
USR
CLK9_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK8_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK7_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK6_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK5_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK4_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK3_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK2_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK1_DIVIDE_BY
5
PARAMETER_SIGNED_DEC
USR
CLK0_DIVIDE_BY
1
PARAMETER_SIGNED_DEC
USR
CLK9_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK8_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK7_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK6_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK5_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK4_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK3_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK2_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
USR
CLK0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
USR
CLK5_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK4_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK9_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK8_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK7_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK6_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK5_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK4_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK3_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK2_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK1_DUTY_CYCLE
50
PARAMETER_SIGNED_DEC
USR
CLK0_DUTY_CYCLE
50
PARAMETER_SIGNED_DEC
USR
CLK9_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK8_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK7_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK6_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK5_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK4_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK3_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK2_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK1_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK0_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK9_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK8_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK7_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK6_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK5_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK4_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK3_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK2_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK1_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK0_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
LOCK_WINDOW_UI
 0.05
PARAMETER_UNKNOWN
DEF
LOCK_WINDOW_UI_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
VCO_RANGE_DETECTOR_LOW_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
VCO_RANGE_DETECTOR_HIGH_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
DPA_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
DPA_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
DPA_DIVIDER
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK2_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK1_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK0_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK3_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK2_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK1_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK0_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK3_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK2_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK2_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK1_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK0_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
VCO_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
VCO_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
SCLKOUT0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
SCLKOUT1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
VCO_MIN
0
PARAMETER_UNKNOWN
DEF
VCO_MAX
0
PARAMETER_UNKNOWN
DEF
VCO_CENTER
0
PARAMETER_UNKNOWN
DEF
PFD_MIN
0
PARAMETER_UNKNOWN
DEF
PFD_MAX
0
PARAMETER_UNKNOWN
DEF
M_INITIAL
0
PARAMETER_UNKNOWN
DEF
M
0
PARAMETER_UNKNOWN
DEF
N
1
PARAMETER_UNKNOWN
DEF
M2
1
PARAMETER_UNKNOWN
DEF
N2
1
PARAMETER_UNKNOWN
DEF
SS
1
PARAMETER_UNKNOWN
DEF
C0_HIGH
0
PARAMETER_UNKNOWN
DEF
C1_HIGH
0
PARAMETER_UNKNOWN
DEF
C2_HIGH
0
PARAMETER_UNKNOWN
DEF
C3_HIGH
0
PARAMETER_UNKNOWN
DEF
C4_HIGH
0
PARAMETER_UNKNOWN
DEF
C5_HIGH
0
PARAMETER_UNKNOWN
DEF
C6_HIGH
0
PARAMETER_UNKNOWN
DEF
C7_HIGH
0
PARAMETER_UNKNOWN
DEF
C8_HIGH
0
PARAMETER_UNKNOWN
DEF
C9_HIGH
0
PARAMETER_UNKNOWN
DEF
C0_LOW
0
PARAMETER_UNKNOWN
DEF
C1_LOW
0
PARAMETER_UNKNOWN
DEF
C2_LOW
0
PARAMETER_UNKNOWN
DEF
C3_LOW
0
PARAMETER_UNKNOWN
DEF
C4_LOW
0
PARAMETER_UNKNOWN
DEF
C5_LOW
0
PARAMETER_UNKNOWN
DEF
C6_LOW
0
PARAMETER_UNKNOWN
DEF
C7_LOW
0
PARAMETER_UNKNOWN
DEF
C8_LOW
0
PARAMETER_UNKNOWN
DEF
C9_LOW
0
PARAMETER_UNKNOWN
DEF
C0_INITIAL
0
PARAMETER_UNKNOWN
DEF
C1_INITIAL
0
PARAMETER_UNKNOWN
DEF
C2_INITIAL
0
PARAMETER_UNKNOWN
DEF
C3_INITIAL
0
PARAMETER_UNKNOWN
DEF
C4_INITIAL
0
PARAMETER_UNKNOWN
DEF
C5_INITIAL
0
PARAMETER_UNKNOWN
DEF
C6_INITIAL
0
PARAMETER_UNKNOWN
DEF
C7_INITIAL
0
PARAMETER_UNKNOWN
DEF
C8_INITIAL
0
PARAMETER_UNKNOWN
DEF
C9_INITIAL
0
PARAMETER_UNKNOWN
DEF
C0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C4_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C5_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C6_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C7_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C8_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C9_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C0_PH
0
PARAMETER_UNKNOWN
DEF
C1_PH
0
PARAMETER_UNKNOWN
DEF
C2_PH
0
PARAMETER_UNKNOWN
DEF
C3_PH
0
PARAMETER_UNKNOWN
DEF
C4_PH
0
PARAMETER_UNKNOWN
DEF
C5_PH
0
PARAMETER_UNKNOWN
DEF
C6_PH
0
PARAMETER_UNKNOWN
DEF
C7_PH
0
PARAMETER_UNKNOWN
DEF
C8_PH
0
PARAMETER_UNKNOWN
DEF
C9_PH
0
PARAMETER_UNKNOWN
DEF
L0_HIGH
1
PARAMETER_UNKNOWN
DEF
L1_HIGH
1
PARAMETER_UNKNOWN
DEF
G0_HIGH
1
PARAMETER_UNKNOWN
DEF
G1_HIGH
1
PARAMETER_UNKNOWN
DEF
G2_HIGH
1
PARAMETER_UNKNOWN
DEF
G3_HIGH
1
PARAMETER_UNKNOWN
DEF
E0_HIGH
1
PARAMETER_UNKNOWN
DEF
E1_HIGH
1
PARAMETER_UNKNOWN
DEF
E2_HIGH
1
PARAMETER_UNKNOWN
DEF
E3_HIGH
1
PARAMETER_UNKNOWN
DEF
L0_LOW
1
PARAMETER_UNKNOWN
DEF
L1_LOW
1
PARAMETER_UNKNOWN
DEF
G0_LOW
1
PARAMETER_UNKNOWN
DEF
G1_LOW
1
PARAMETER_UNKNOWN
DEF
G2_LOW
1
PARAMETER_UNKNOWN
DEF
G3_LOW
1
PARAMETER_UNKNOWN
DEF
E0_LOW
1
PARAMETER_UNKNOWN
DEF
E1_LOW
1
PARAMETER_UNKNOWN
DEF
E2_LOW
1
PARAMETER_UNKNOWN
DEF
E3_LOW
1
PARAMETER_UNKNOWN
DEF
L0_INITIAL
1
PARAMETER_UNKNOWN
DEF
L1_INITIAL
1
PARAMETER_UNKNOWN
DEF
G0_INITIAL
1
PARAMETER_UNKNOWN
DEF
G1_INITIAL
1
PARAMETER_UNKNOWN
DEF
G2_INITIAL
1
PARAMETER_UNKNOWN
DEF
G3_INITIAL
1
PARAMETER_UNKNOWN
DEF
E0_INITIAL
1
PARAMETER_UNKNOWN
DEF
E1_INITIAL
1
PARAMETER_UNKNOWN
DEF
E2_INITIAL
1
PARAMETER_UNKNOWN
DEF
E3_INITIAL
1
PARAMETER_UNKNOWN
DEF
L0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
L1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
L0_PH
0
PARAMETER_UNKNOWN
DEF
L1_PH
0
PARAMETER_UNKNOWN
DEF
G0_PH
0
PARAMETER_UNKNOWN
DEF
G1_PH
0
PARAMETER_UNKNOWN
DEF
G2_PH
0
PARAMETER_UNKNOWN
DEF
G3_PH
0
PARAMETER_UNKNOWN
DEF
E0_PH
0
PARAMETER_UNKNOWN
DEF
E1_PH
0
PARAMETER_UNKNOWN
DEF
E2_PH
0
PARAMETER_UNKNOWN
DEF
E3_PH
0
PARAMETER_UNKNOWN
DEF
M_PH
0
PARAMETER_UNKNOWN
DEF
C1_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C2_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C3_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C4_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C5_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C6_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C7_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C8_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C9_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
CLK0_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK1_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK2_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK3_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK4_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK5_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK6_COUNTER
E0
PARAMETER_UNKNOWN
DEF
CLK7_COUNTER
E1
PARAMETER_UNKNOWN
DEF
CLK8_COUNTER
E2
PARAMETER_UNKNOWN
DEF
CLK9_COUNTER
E3
PARAMETER_UNKNOWN
DEF
L0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
L1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
M_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
N_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_COUNTER
E3
PARAMETER_UNKNOWN
DEF
EXTCLK2_COUNTER
E2
PARAMETER_UNKNOWN
DEF
EXTCLK1_COUNTER
E1
PARAMETER_UNKNOWN
DEF
EXTCLK0_COUNTER
E0
PARAMETER_UNKNOWN
DEF
ENABLE0_COUNTER
L0
PARAMETER_UNKNOWN
DEF
ENABLE1_COUNTER
L0
PARAMETER_UNKNOWN
DEF
CHARGE_PUMP_CURRENT
2
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_R
 1.000000
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_C
5
PARAMETER_UNKNOWN
DEF
CHARGE_PUMP_CURRENT_BITS
9999
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_R_BITS
9999
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_C_BITS
9999
PARAMETER_UNKNOWN
DEF
VCO_POST_SCALE
0
PARAMETER_UNKNOWN
DEF
CLK2_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
CLK1_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
CLK0_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
INTENDED_DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
PORT_CLKENA0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA4
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA5
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLKENA0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA2
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA3
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLK0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKBAD0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKBAD1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK0
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CLK1
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CLK2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK4
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK5
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK6
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK7
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK8
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK9
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_SCANDATA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANDATAOUT
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANDONE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCLKOUT1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_SCLKOUT0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_ACTIVECLOCK
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKLOSS
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_INCLK1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_INCLK0
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_FBIN
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PLLENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKSWITCH
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_ARESET
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_PFDENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANCLK
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANACLR
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANREAD
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANWRITE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_ENABLE0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_ENABLE1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_LOCKED
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CONFIGUPDATE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_FBOUT
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_PHASEDONE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASESTEP
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASEUPDOWN
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANCLKENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASECOUNTERSELECT
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_VCOOVERRANGE
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_VCOUNDERRANGE
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
M_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C0_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C1_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C2_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C3_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C4_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C5_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C6_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C7_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C8_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C9_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
CBXI_PARAMETER
pll180m_altpll
PARAMETER_UNKNOWN
USR
VCO_FREQUENCY_CONTROL
AUTO
PARAMETER_UNKNOWN
DEF
VCO_PHASE_SHIFT_STEP
0
PARAMETER_UNKNOWN
DEF
WIDTH_CLOCK
5
PARAMETER_SIGNED_DEC
USR
WIDTH_PHASECOUNTERSELECT
4
PARAMETER_UNKNOWN
DEF
USING_FBMIMICBIDIR_PORT
OFF
PARAMETER_UNKNOWN
DEF
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
SCAN_CHAIN_MIF_FILE
UNUSED
PARAMETER_UNKNOWN
DEF
SIM_GATE_LOCK_DEVICE_BEHAVIOR
OFF
PARAMETER_UNKNOWN
DEF
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
}
# used_port {
locked
-1
3
inclk
-1
3
clk
-1
3
areset
-1
3
scanwrite
-1
1
scanread
-1
1
scandata
-1
1
scanclk
-1
1
scanaclr
-1
1
configupdate
-1
1
clkswitch
-1
1
scanclkena
-1
2
pllena
-1
2
phaseupdown
-1
2
phasestep
-1
2
phasecounterselect
-1
2
pfdena
-1
2
fbin
-1
2
extclkena
-1
2
clkena
-1
2
}
# include_file {
stratixii_pll.inc
6797ab505ed70f1a221e4a213e16a6
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
stratix_pll.inc
a9a94c5be18105f7ae8c218a67ec9f7
cycloneii_pll.inc
c2ee779f89b3bc181df753ea85b3ef
}
# macro_sequence

# end
# entity
pll180m_altpll
# storage
db|DAC904_HighSpeedDAC.(15).cnf
db|DAC904_HighSpeedDAC.(15).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
db|pll180m_altpll.v
8df34c6ab82e378e6df2224be5f2bf
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
DAC904_WriteTEST
# storage
db|DAC904_HighSpeedDAC.(0).cnf
db|DAC904_HighSpeedDAC.(0).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.1_dds|rtl|dac904_writetest.v
547be1b94220da6abfa5b1d99ac7d562
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence

# end
# entity
altsyncram
# storage
db|DAC904_HighSpeedDAC.(21).cnf
db|DAC904_HighSpeedDAC.(21).cnf
# case_insensitive
# source_file
altsyncram.tdf
f8b1b3e6a95fdea61cd86b5880d92761
7
# user_parameter {
BYTE_SIZE_BLOCK
8
PARAMETER_UNKNOWN
DEF
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
WIDTH_BYTEENA
1
PARAMETER_UNKNOWN
DEF
OPERATION_MODE
ROM
PARAMETER_UNKNOWN
USR
WIDTH_A
14
PARAMETER_SIGNED_DEC
USR
WIDTHAD_A
14
PARAMETER_SIGNED_DEC
USR
NUMWORDS_A
16384
PARAMETER_SIGNED_DEC
USR
OUTDATA_REG_A
CLOCK0
PARAMETER_UNKNOWN
USR
ADDRESS_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
OUTDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
WRCONTROL_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_B
1
PARAMETER_UNKNOWN
DEF
WIDTHAD_B
1
PARAMETER_UNKNOWN
DEF
NUMWORDS_B
1
PARAMETER_UNKNOWN
DEF
INDATA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
WRCONTROL_WRADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
RDCONTROL_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
ADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
OUTDATA_REG_B
UNREGISTERED
PARAMETER_UNKNOWN
DEF
BYTEENA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WRCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
ADDRESS_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
OUTDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
RDCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_BYTEENA_A
1
PARAMETER_SIGNED_DEC
USR
WIDTH_BYTEENA_B
1
PARAMETER_UNKNOWN
DEF
RAM_BLOCK_TYPE
AUTO
PARAMETER_UNKNOWN
DEF
BYTE_SIZE
8
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_MIXED_PORTS
DONT_CARE
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_A
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_B
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
INIT_FILE
../../ROM/ROM_14B_SIN/ROM_14B_TRIANG.mif
PARAMETER_UNKNOWN
USR
INIT_FILE_LAYOUT
PORT_A
PARAMETER_UNKNOWN
DEF
MAXIMUM_DEPTH
0
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_INPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_INPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_OUTPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_OUTPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_A
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_B
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
ENABLE_ECC
FALSE
PARAMETER_UNKNOWN
DEF
WIDTH_ECCSTATUS
3
PARAMETER_UNKNOWN
DEF
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
CBXI_PARAMETER
altsyncram_qrb1
PARAMETER_UNKNOWN
USR
}
# used_port {
q_a
-1
3
clock0
-1
3
address_a
-1
3
wren_b
-1
1
wren_a
-1
1
addressstall_b
-1
1
addressstall_a
-1
1
aclr1
-1
1
aclr0
-1
1
rden_b
-1
2
rden_a
-1
2
data_b
-1
2
data_a
-1
2
clocken3
-1
2
clocken2
-1
2
clocken1
-1
2
clocken0
-1
2
clock1
-1
2
byteena_b
-1
2
byteena_a
-1
2
address_b
-1
2
}
# include_file {
altrom.inc
192b74eafa8debf2248ea73881e77f91
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
lpm_mux.inc
dd87bed9959d6126db09970164b7ba6
a_rdenreg.inc
3fcdce755959d5a8afbe64788d21fb
altdpram.inc
2f9e6727b678ffd76e72bc5a95a2630
altram.inc
ad5518b39ffd3cf1df377e6360d1c9b6
stratix_ram_block.inc
e3a03868917fb3dd57b6ed1dd195f22
lpm_decode.inc
10da69a8bbd590d66779e7a142f73790
}
# macro_sequence

# end
# entity
altsyncram_qrb1
# storage
db|DAC904_HighSpeedDAC.(22).cnf
db|DAC904_HighSpeedDAC.(22).cnf
# case_insensitive
# source_file
db|altsyncram_qrb1.tdf
3bdfab82932a78f66ef8607271a04caa
7
# used_port {
q_a9
-1
3
q_a8
-1
3
q_a7
-1
3
q_a6
-1
3
q_a5
-1
3
q_a4
-1
3
q_a3
-1
3
q_a2
-1
3
q_a13
-1
3
q_a12
-1
3
q_a11
-1
3
q_a10
-1
3
q_a1
-1
3
q_a0
-1
3
clock0
-1
3
address_a9
-1
3
address_a8
-1
3
address_a7
-1
3
address_a6
-1
3
address_a5
-1
3
address_a4
-1
3
address_a3
-1
3
address_a2
-1
3
address_a13
-1
3
address_a12
-1
3
address_a11
-1
3
address_a10
-1
3
address_a1
-1
3
address_a0
-1
3
}
# memory_file {

0
}
# macro_sequence

# end
# entity
lpm_mult
# storage
db|DAC904_HighSpeedDAC.(23).cnf
db|DAC904_HighSpeedDAC.(23).cnf
# case_insensitive
# source_file
lpm_mult.tdf
22eb094901b73f6a2c1bae1edc91b
7
# user_parameter {
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
LPM_WIDTHA
28
PARAMETER_UNKNOWN
USR
LPM_WIDTHB
14
PARAMETER_UNKNOWN
USR
LPM_WIDTHP
42
PARAMETER_UNKNOWN
USR
LPM_WIDTHR
42
PARAMETER_UNKNOWN
USR
LPM_WIDTHS
1
PARAMETER_UNKNOWN
USR
LPM_REPRESENTATION
UNSIGNED
PARAMETER_UNKNOWN
USR
LPM_PIPELINE
0
PARAMETER_UNKNOWN
DEF
LATENCY
0
PARAMETER_UNKNOWN
DEF
INPUT_A_IS_CONSTANT
NO
PARAMETER_UNKNOWN
USR
INPUT_B_IS_CONSTANT
NO
PARAMETER_UNKNOWN
USR
USE_EAB
OFF
PARAMETER_UNKNOWN
DEF
MAXIMIZE_SPEED
5
PARAMETER_UNKNOWN
USR
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
CARRY_CHAIN
MANUAL
PARAMETER_UNKNOWN
USR
APEX20K_TECHNOLOGY_MAPPER
LUT
TECH_MAPPER_APEX20K
USR
DEDICATED_MULTIPLIER_CIRCUITRY
AUTO
PARAMETER_UNKNOWN
DEF
DEDICATED_MULTIPLIER_MIN_INPUT_WIDTH_FOR_AUTO
0
PARAMETER_UNKNOWN
DEF
DEDICATED_MULTIPLIER_MIN_OUTPUT_WIDTH_FOR_AUTO
0
PARAMETER_UNKNOWN
DEF
CBXI_PARAMETER
mult_8dt
PARAMETER_UNKNOWN
USR
INPUT_A_FIXED_VALUE
Bx
PARAMETER_UNKNOWN
DEF
INPUT_B_FIXED_VALUE
Bx
PARAMETER_UNKNOWN
DEF
USE_AHDL_IMPLEMENTATION
OFF
PARAMETER_UNKNOWN
DEF
}
# used_port {
result27
-1
3
result26
-1
3
result25
-1
3
result24
-1
3
result23
-1
3
result22
-1
3
result21
-1
3
result20
-1
3
result19
-1
3
result18
-1
3
result17
-1
3
result16
-1
3
result15
-1
3
result14
-1
3
datab9
-1
3
datab8
-1
3
datab7
-1
3
datab6
-1
3
datab5
-1
3
datab4
-1
3
datab3
-1
3
datab2
-1
3
datab13
-1
3
datab12
-1
3
datab11
-1
3
datab10
-1
3
datab1
-1
3
datab0
-1
3
dataa9
-1
3
dataa8
-1
3
dataa7
-1
3
dataa6
-1
3
dataa5
-1
3
dataa4
-1
3
dataa3
-1
3
dataa27
-1
3
dataa26
-1
3
dataa25
-1
3
dataa24
-1
3
dataa23
-1
3
dataa22
-1
3
dataa21
-1
3
dataa20
-1
3
dataa2
-1
3
dataa19
-1
3
dataa18
-1
3
dataa17
-1
3
dataa16
-1
3
dataa15
-1
3
dataa14
-1
3
dataa13
-1
3
dataa12
-1
3
dataa11
-1
3
dataa10
-1
3
dataa1
-1
3
dataa0
-1
3
}
# include_file {
altshift.inc
5c767a29f11db3f131fc886ea42a52bd
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
lpm_add_sub.inc
144a73b61081a2a03554ff5acc5e8842
multcore.inc
ee598ea39a3d6bdc35b167eefc3ee3da
bypassff.inc
42d08f243d3471f724fd61ea21a0eb9f
}
# macro_sequence

# end
# entity
mult_8dt
# storage
db|DAC904_HighSpeedDAC.(24).cnf
db|DAC904_HighSpeedDAC.(24).cnf
# case_insensitive
# source_file
db|mult_8dt.tdf
57abdaa5a9755194e3d7a33d7834b5
7
# used_port {
result9
-1
3
result8
-1
3
result7
-1
3
result6
-1
3
result5
-1
3
result41
-1
3
result40
-1
3
result4
-1
3
result39
-1
3
result38
-1
3
result37
-1
3
result36
-1
3
result35
-1
3
result34
-1
3
result33
-1
3
result32
-1
3
result31
-1
3
result30
-1
3
result3
-1
3
result29
-1
3
result28
-1
3
result27
-1
3
result26
-1
3
result25
-1
3
result24
-1
3
result23
-1
3
result22
-1
3
result21
-1
3
result20
-1
3
result2
-1
3
result19
-1
3
result18
-1
3
result17
-1
3
result16
-1
3
result15
-1
3
result14
-1
3
result13
-1
3
result12
-1
3
result11
-1
3
result10
-1
3
result1
-1
3
result0
-1
3
datab9
-1
3
datab8
-1
3
datab7
-1
3
datab6
-1
3
datab5
-1
3
datab4
-1
3
datab3
-1
3
datab2
-1
3
datab13
-1
3
datab12
-1
3
datab11
-1
3
datab10
-1
3
datab1
-1
3
datab0
-1
3
dataa9
-1
3
dataa8
-1
3
dataa7
-1
3
dataa6
-1
3
dataa5
-1
3
dataa4
-1
3
dataa3
-1
3
dataa27
-1
3
dataa26
-1
3
dataa25
-1
3
dataa24
-1
3
dataa23
-1
3
dataa22
-1
3
dataa21
-1
3
dataa20
-1
3
dataa2
-1
3
dataa19
-1
3
dataa18
-1
3
dataa17
-1
3
dataa16
-1
3
dataa15
-1
3
dataa14
-1
3
dataa13
-1
3
dataa12
-1
3
dataa11
-1
3
dataa10
-1
3
dataa1
-1
3
dataa0
-1
3
}
# macro_sequence

# end
# entity
lpm_mult
# storage
db|DAC904_HighSpeedDAC.(25).cnf
db|DAC904_HighSpeedDAC.(25).cnf
# case_insensitive
# source_file
lpm_mult.tdf
22eb094901b73f6a2c1bae1edc91b
7
# user_parameter {
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
LPM_WIDTHA
28
PARAMETER_UNKNOWN
USR
LPM_WIDTHB
14
PARAMETER_UNKNOWN
USR
LPM_WIDTHP
42
PARAMETER_UNKNOWN
USR
LPM_WIDTHR
42
PARAMETER_UNKNOWN
USR
LPM_WIDTHS
1
PARAMETER_UNKNOWN
USR
LPM_REPRESENTATION
UNSIGNED
PARAMETER_UNKNOWN
USR
LPM_PIPELINE
0
PARAMETER_UNKNOWN
DEF
LATENCY
0
PARAMETER_UNKNOWN
DEF
INPUT_A_IS_CONSTANT
NO
PARAMETER_UNKNOWN
USR
INPUT_B_IS_CONSTANT
NO
PARAMETER_UNKNOWN
USR
USE_EAB
OFF
PARAMETER_UNKNOWN
DEF
MAXIMIZE_SPEED
5
PARAMETER_UNKNOWN
USR
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
CARRY_CHAIN
MANUAL
PARAMETER_UNKNOWN
USR
APEX20K_TECHNOLOGY_MAPPER
LUT
TECH_MAPPER_APEX20K
USR
DEDICATED_MULTIPLIER_CIRCUITRY
AUTO
PARAMETER_UNKNOWN
DEF
DEDICATED_MULTIPLIER_MIN_INPUT_WIDTH_FOR_AUTO
0
PARAMETER_UNKNOWN
DEF
DEDICATED_MULTIPLIER_MIN_OUTPUT_WIDTH_FOR_AUTO
0
PARAMETER_UNKNOWN
DEF
CBXI_PARAMETER
mult_8dt
PARAMETER_UNKNOWN
USR
INPUT_A_FIXED_VALUE
Bx
PARAMETER_UNKNOWN
DEF
INPUT_B_FIXED_VALUE
Bx
PARAMETER_UNKNOWN
DEF
USE_AHDL_IMPLEMENTATION
OFF
PARAMETER_UNKNOWN
DEF
}
# used_port {
result9
-1
3
result8
-1
3
result7
-1
3
result6
-1
3
result5
-1
3
result4
-1
3
result3
-1
3
result27
-1
3
result26
-1
3
result25
-1
3
result24
-1
3
result23
-1
3
result22
-1
3
result21
-1
3
result20
-1
3
result2
-1
3
result19
-1
3
result18
-1
3
result17
-1
3
result16
-1
3
result15
-1
3
result14
-1
3
result13
-1
3
result12
-1
3
result11
-1
3
result10
-1
3
result1
-1
3
result0
-1
3
datab9
-1
3
datab8
-1
3
datab7
-1
3
datab6
-1
3
datab5
-1
3
datab4
-1
3
datab3
-1
3
datab2
-1
3
datab13
-1
3
datab12
-1
3
datab11
-1
3
datab10
-1
3
datab1
-1
3
datab0
-1
3
dataa9
-1
3
dataa8
-1
3
dataa7
-1
3
dataa6
-1
3
dataa5
-1
3
dataa4
-1
3
dataa3
-1
3
dataa27
-1
3
dataa26
-1
3
dataa25
-1
3
dataa24
-1
3
dataa23
-1
3
dataa22
-1
3
dataa21
-1
3
dataa20
-1
3
dataa2
-1
3
dataa19
-1
3
dataa18
-1
3
dataa17
-1
3
dataa16
-1
3
dataa15
-1
3
dataa14
-1
3
dataa13
-1
3
dataa12
-1
3
dataa11
-1
3
dataa10
-1
3
dataa1
-1
3
dataa0
-1
3
}
# include_file {
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
bypassff.inc
42d08f243d3471f724fd61ea21a0eb9f
altshift.inc
5c767a29f11db3f131fc886ea42a52bd
multcore.inc
ee598ea39a3d6bdc35b167eefc3ee3da
lpm_add_sub.inc
144a73b61081a2a03554ff5acc5e8842
}
# macro_sequence

# end
# entity
altpll
# storage
db|DAC904_HighSpeedDAC.(27).cnf
db|DAC904_HighSpeedDAC.(27).cnf
# case_insensitive
# source_file
altpll.tdf
1432cd46f66d9cd9c995c4e7995ff8
7
# user_parameter {
OPERATION_MODE
NORMAL
PARAMETER_UNKNOWN
USR
PLL_TYPE
AUTO
PARAMETER_UNKNOWN
USR
LPM_HINT
CBX_MODULE_PREFIX=pll165m
PARAMETER_UNKNOWN
USR
QUALIFY_CONF_DONE
OFF
PARAMETER_UNKNOWN
DEF
COMPENSATE_CLOCK
CLK0
PARAMETER_UNKNOWN
USR
SCAN_CHAIN
LONG
PARAMETER_UNKNOWN
DEF
PRIMARY_CLOCK
INCLK0
PARAMETER_UNKNOWN
DEF
INCLK0_INPUT_FREQUENCY
20000
PARAMETER_SIGNED_DEC
USR
INCLK1_INPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
GATE_LOCK_SIGNAL
NO
PARAMETER_UNKNOWN
DEF
GATE_LOCK_COUNTER
0
PARAMETER_UNKNOWN
DEF
LOCK_HIGH
1
PARAMETER_UNKNOWN
DEF
LOCK_LOW
1
PARAMETER_UNKNOWN
DEF
VALID_LOCK_MULTIPLIER
1
PARAMETER_UNKNOWN
DEF
INVALID_LOCK_MULTIPLIER
5
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_ON_LOSSCLK
OFF
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_ON_GATED_LOCK
OFF
PARAMETER_UNKNOWN
DEF
ENABLE_SWITCH_OVER_COUNTER
OFF
PARAMETER_UNKNOWN
DEF
SKIP_VCO
OFF
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_COUNTER
0
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_TYPE
AUTO
PARAMETER_UNKNOWN
DEF
FEEDBACK_SOURCE
EXTCLK0
PARAMETER_UNKNOWN
DEF
BANDWIDTH
0
PARAMETER_UNKNOWN
DEF
BANDWIDTH_TYPE
AUTO
PARAMETER_UNKNOWN
USR
SPREAD_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
DOWN_SPREAD
0
PARAMETER_UNKNOWN
DEF
SELF_RESET_ON_GATED_LOSS_LOCK
OFF
PARAMETER_UNKNOWN
DEF
SELF_RESET_ON_LOSS_LOCK
OFF
PARAMETER_UNKNOWN
USR
CLK9_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK8_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK7_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK6_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK5_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK4_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK3_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK2_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK1_MULTIPLY_BY
33
PARAMETER_SIGNED_DEC
USR
CLK0_MULTIPLY_BY
33
PARAMETER_SIGNED_DEC
USR
CLK9_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK8_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK7_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK6_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK5_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK4_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK3_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK2_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK1_DIVIDE_BY
10
PARAMETER_SIGNED_DEC
USR
CLK0_DIVIDE_BY
25
PARAMETER_SIGNED_DEC
USR
CLK9_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK8_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK7_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK6_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK5_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK4_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK3_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK2_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
USR
CLK0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
USR
CLK5_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK4_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK9_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK8_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK7_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK6_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK5_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK4_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK3_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK2_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK1_DUTY_CYCLE
50
PARAMETER_SIGNED_DEC
USR
CLK0_DUTY_CYCLE
50
PARAMETER_SIGNED_DEC
USR
CLK9_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK8_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK7_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK6_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK5_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK4_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK3_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK2_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK1_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK0_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK9_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK8_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK7_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK6_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK5_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK4_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK3_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK2_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK1_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK0_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
LOCK_WINDOW_UI
 0.05
PARAMETER_UNKNOWN
DEF
LOCK_WINDOW_UI_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
VCO_RANGE_DETECTOR_LOW_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
VCO_RANGE_DETECTOR_HIGH_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
DPA_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
DPA_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
DPA_DIVIDER
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK2_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK1_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK0_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK3_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK2_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK1_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK0_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK3_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK2_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK2_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK1_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK0_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
VCO_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
VCO_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
SCLKOUT0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
SCLKOUT1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
VCO_MIN
0
PARAMETER_UNKNOWN
DEF
VCO_MAX
0
PARAMETER_UNKNOWN
DEF
VCO_CENTER
0
PARAMETER_UNKNOWN
DEF
PFD_MIN
0
PARAMETER_UNKNOWN
DEF
PFD_MAX
0
PARAMETER_UNKNOWN
DEF
M_INITIAL
0
PARAMETER_UNKNOWN
DEF
M
0
PARAMETER_UNKNOWN
DEF
N
1
PARAMETER_UNKNOWN
DEF
M2
1
PARAMETER_UNKNOWN
DEF
N2
1
PARAMETER_UNKNOWN
DEF
SS
1
PARAMETER_UNKNOWN
DEF
C0_HIGH
0
PARAMETER_UNKNOWN
DEF
C1_HIGH
0
PARAMETER_UNKNOWN
DEF
C2_HIGH
0
PARAMETER_UNKNOWN
DEF
C3_HIGH
0
PARAMETER_UNKNOWN
DEF
C4_HIGH
0
PARAMETER_UNKNOWN
DEF
C5_HIGH
0
PARAMETER_UNKNOWN
DEF
C6_HIGH
0
PARAMETER_UNKNOWN
DEF
C7_HIGH
0
PARAMETER_UNKNOWN
DEF
C8_HIGH
0
PARAMETER_UNKNOWN
DEF
C9_HIGH
0
PARAMETER_UNKNOWN
DEF
C0_LOW
0
PARAMETER_UNKNOWN
DEF
C1_LOW
0
PARAMETER_UNKNOWN
DEF
C2_LOW
0
PARAMETER_UNKNOWN
DEF
C3_LOW
0
PARAMETER_UNKNOWN
DEF
C4_LOW
0
PARAMETER_UNKNOWN
DEF
C5_LOW
0
PARAMETER_UNKNOWN
DEF
C6_LOW
0
PARAMETER_UNKNOWN
DEF
C7_LOW
0
PARAMETER_UNKNOWN
DEF
C8_LOW
0
PARAMETER_UNKNOWN
DEF
C9_LOW
0
PARAMETER_UNKNOWN
DEF
C0_INITIAL
0
PARAMETER_UNKNOWN
DEF
C1_INITIAL
0
PARAMETER_UNKNOWN
DEF
C2_INITIAL
0
PARAMETER_UNKNOWN
DEF
C3_INITIAL
0
PARAMETER_UNKNOWN
DEF
C4_INITIAL
0
PARAMETER_UNKNOWN
DEF
C5_INITIAL
0
PARAMETER_UNKNOWN
DEF
C6_INITIAL
0
PARAMETER_UNKNOWN
DEF
C7_INITIAL
0
PARAMETER_UNKNOWN
DEF
C8_INITIAL
0
PARAMETER_UNKNOWN
DEF
C9_INITIAL
0
PARAMETER_UNKNOWN
DEF
C0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C4_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C5_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C6_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C7_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C8_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C9_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C0_PH
0
PARAMETER_UNKNOWN
DEF
C1_PH
0
PARAMETER_UNKNOWN
DEF
C2_PH
0
PARAMETER_UNKNOWN
DEF
C3_PH
0
PARAMETER_UNKNOWN
DEF
C4_PH
0
PARAMETER_UNKNOWN
DEF
C5_PH
0
PARAMETER_UNKNOWN
DEF
C6_PH
0
PARAMETER_UNKNOWN
DEF
C7_PH
0
PARAMETER_UNKNOWN
DEF
C8_PH
0
PARAMETER_UNKNOWN
DEF
C9_PH
0
PARAMETER_UNKNOWN
DEF
L0_HIGH
1
PARAMETER_UNKNOWN
DEF
L1_HIGH
1
PARAMETER_UNKNOWN
DEF
G0_HIGH
1
PARAMETER_UNKNOWN
DEF
G1_HIGH
1
PARAMETER_UNKNOWN
DEF
G2_HIGH
1
PARAMETER_UNKNOWN
DEF
G3_HIGH
1
PARAMETER_UNKNOWN
DEF
E0_HIGH
1
PARAMETER_UNKNOWN
DEF
E1_HIGH
1
PARAMETER_UNKNOWN
DEF
E2_HIGH
1
PARAMETER_UNKNOWN
DEF
E3_HIGH
1
PARAMETER_UNKNOWN
DEF
L0_LOW
1
PARAMETER_UNKNOWN
DEF
L1_LOW
1
PARAMETER_UNKNOWN
DEF
G0_LOW
1
PARAMETER_UNKNOWN
DEF
G1_LOW
1
PARAMETER_UNKNOWN
DEF
G2_LOW
1
PARAMETER_UNKNOWN
DEF
G3_LOW
1
PARAMETER_UNKNOWN
DEF
E0_LOW
1
PARAMETER_UNKNOWN
DEF
E1_LOW
1
PARAMETER_UNKNOWN
DEF
E2_LOW
1
PARAMETER_UNKNOWN
DEF
E3_LOW
1
PARAMETER_UNKNOWN
DEF
L0_INITIAL
1
PARAMETER_UNKNOWN
DEF
L1_INITIAL
1
PARAMETER_UNKNOWN
DEF
G0_INITIAL
1
PARAMETER_UNKNOWN
DEF
G1_INITIAL
1
PARAMETER_UNKNOWN
DEF
G2_INITIAL
1
PARAMETER_UNKNOWN
DEF
G3_INITIAL
1
PARAMETER_UNKNOWN
DEF
E0_INITIAL
1
PARAMETER_UNKNOWN
DEF
E1_INITIAL
1
PARAMETER_UNKNOWN
DEF
E2_INITIAL
1
PARAMETER_UNKNOWN
DEF
E3_INITIAL
1
PARAMETER_UNKNOWN
DEF
L0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
L1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
L0_PH
0
PARAMETER_UNKNOWN
DEF
L1_PH
0
PARAMETER_UNKNOWN
DEF
G0_PH
0
PARAMETER_UNKNOWN
DEF
G1_PH
0
PARAMETER_UNKNOWN
DEF
G2_PH
0
PARAMETER_UNKNOWN
DEF
G3_PH
0
PARAMETER_UNKNOWN
DEF
E0_PH
0
PARAMETER_UNKNOWN
DEF
E1_PH
0
PARAMETER_UNKNOWN
DEF
E2_PH
0
PARAMETER_UNKNOWN
DEF
E3_PH
0
PARAMETER_UNKNOWN
DEF
M_PH
0
PARAMETER_UNKNOWN
DEF
C1_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C2_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C3_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C4_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C5_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C6_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C7_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C8_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C9_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
CLK0_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK1_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK2_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK3_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK4_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK5_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK6_COUNTER
E0
PARAMETER_UNKNOWN
DEF
CLK7_COUNTER
E1
PARAMETER_UNKNOWN
DEF
CLK8_COUNTER
E2
PARAMETER_UNKNOWN
DEF
CLK9_COUNTER
E3
PARAMETER_UNKNOWN
DEF
L0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
L1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
M_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
N_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_COUNTER
E3
PARAMETER_UNKNOWN
DEF
EXTCLK2_COUNTER
E2
PARAMETER_UNKNOWN
DEF
EXTCLK1_COUNTER
E1
PARAMETER_UNKNOWN
DEF
EXTCLK0_COUNTER
E0
PARAMETER_UNKNOWN
DEF
ENABLE0_COUNTER
L0
PARAMETER_UNKNOWN
DEF
ENABLE1_COUNTER
L0
PARAMETER_UNKNOWN
DEF
CHARGE_PUMP_CURRENT
2
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_R
 1.000000
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_C
5
PARAMETER_UNKNOWN
DEF
CHARGE_PUMP_CURRENT_BITS
9999
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_R_BITS
9999
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_C_BITS
9999
PARAMETER_UNKNOWN
DEF
VCO_POST_SCALE
0
PARAMETER_UNKNOWN
DEF
CLK2_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
CLK1_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
CLK0_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
INTENDED_DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
PORT_CLKENA0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA4
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA5
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLKENA0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA2
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA3
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLK0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKBAD0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKBAD1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK0
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CLK1
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CLK2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK4
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK5
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK6
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK7
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK8
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK9
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_SCANDATA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANDATAOUT
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANDONE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCLKOUT1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_SCLKOUT0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_ACTIVECLOCK
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKLOSS
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_INCLK1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_INCLK0
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_FBIN
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PLLENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKSWITCH
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_ARESET
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_PFDENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANCLK
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANACLR
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANREAD
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANWRITE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_ENABLE0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_ENABLE1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_LOCKED
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CONFIGUPDATE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_FBOUT
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_PHASEDONE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASESTEP
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASEUPDOWN
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANCLKENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASECOUNTERSELECT
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_VCOOVERRANGE
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_VCOUNDERRANGE
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
M_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C0_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C1_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C2_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C3_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C4_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C5_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C6_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C7_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C8_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C9_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
CBXI_PARAMETER
pll165m_altpll
PARAMETER_UNKNOWN
USR
VCO_FREQUENCY_CONTROL
AUTO
PARAMETER_UNKNOWN
DEF
VCO_PHASE_SHIFT_STEP
0
PARAMETER_UNKNOWN
DEF
WIDTH_CLOCK
5
PARAMETER_SIGNED_DEC
USR
WIDTH_PHASECOUNTERSELECT
4
PARAMETER_UNKNOWN
DEF
USING_FBMIMICBIDIR_PORT
OFF
PARAMETER_UNKNOWN
DEF
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
SCAN_CHAIN_MIF_FILE
UNUSED
PARAMETER_UNKNOWN
DEF
SIM_GATE_LOCK_DEVICE_BEHAVIOR
OFF
PARAMETER_UNKNOWN
DEF
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
}
# used_port {
locked
-1
3
inclk
-1
3
clk
-1
3
areset
-1
3
scanwrite
-1
1
scanread
-1
1
scandata
-1
1
scanclk
-1
1
scanaclr
-1
1
configupdate
-1
1
clkswitch
-1
1
scanclkena
-1
2
pllena
-1
2
phaseupdown
-1
2
phasestep
-1
2
phasecounterselect
-1
2
pfdena
-1
2
fbin
-1
2
extclkena
-1
2
clkena
-1
2
}
# include_file {
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
stratix_pll.inc
a9a94c5be18105f7ae8c218a67ec9f7
cycloneii_pll.inc
c2ee779f89b3bc181df753ea85b3ef
stratixii_pll.inc
6797ab505ed70f1a221e4a213e16a6
}
# hierarchies {
pll165m:U0_pll165m_inst|altpll:altpll_component
}
# macro_sequence

# end
# entity
altsyncram
# storage
db|DAC904_HighSpeedDAC.(29).cnf
db|DAC904_HighSpeedDAC.(29).cnf
# case_insensitive
# source_file
altsyncram.tdf
f8b1b3e6a95fdea61cd86b5880d92761
7
# user_parameter {
BYTE_SIZE_BLOCK
8
PARAMETER_UNKNOWN
DEF
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
WIDTH_BYTEENA
1
PARAMETER_UNKNOWN
DEF
OPERATION_MODE
ROM
PARAMETER_UNKNOWN
USR
WIDTH_A
14
PARAMETER_SIGNED_DEC
USR
WIDTHAD_A
14
PARAMETER_SIGNED_DEC
USR
NUMWORDS_A
16384
PARAMETER_SIGNED_DEC
USR
OUTDATA_REG_A
CLOCK0
PARAMETER_UNKNOWN
USR
ADDRESS_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
OUTDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
WRCONTROL_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_B
1
PARAMETER_UNKNOWN
DEF
WIDTHAD_B
1
PARAMETER_UNKNOWN
DEF
NUMWORDS_B
1
PARAMETER_UNKNOWN
DEF
INDATA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
WRCONTROL_WRADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
RDCONTROL_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
ADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
OUTDATA_REG_B
UNREGISTERED
PARAMETER_UNKNOWN
DEF
BYTEENA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WRCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
ADDRESS_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
OUTDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
RDCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_BYTEENA_A
1
PARAMETER_SIGNED_DEC
USR
WIDTH_BYTEENA_B
1
PARAMETER_UNKNOWN
DEF
RAM_BLOCK_TYPE
AUTO
PARAMETER_UNKNOWN
DEF
BYTE_SIZE
8
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_MIXED_PORTS
DONT_CARE
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_A
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_B
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
INIT_FILE
../../ROM/ROM_14B_SIN/ROM_14B_SQUARE.mif
PARAMETER_UNKNOWN
USR
INIT_FILE_LAYOUT
PORT_A
PARAMETER_UNKNOWN
DEF
MAXIMUM_DEPTH
0
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_INPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_INPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_OUTPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_OUTPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_A
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_B
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
ENABLE_ECC
FALSE
PARAMETER_UNKNOWN
DEF
WIDTH_ECCSTATUS
3
PARAMETER_UNKNOWN
DEF
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
CBXI_PARAMETER
altsyncram_6sb1
PARAMETER_UNKNOWN
USR
}
# used_port {
q_a
-1
3
clock0
-1
3
address_a
-1
3
wren_b
-1
1
wren_a
-1
1
addressstall_b
-1
1
addressstall_a
-1
1
aclr1
-1
1
aclr0
-1
1
rden_b
-1
2
rden_a
-1
2
data_b
-1
2
data_a
-1
2
clocken3
-1
2
clocken2
-1
2
clocken1
-1
2
clocken0
-1
2
clock1
-1
2
byteena_b
-1
2
byteena_a
-1
2
address_b
-1
2
}
# include_file {
altram.inc
ad5518b39ffd3cf1df377e6360d1c9b6
stratix_ram_block.inc
e3a03868917fb3dd57b6ed1dd195f22
lpm_decode.inc
10da69a8bbd590d66779e7a142f73790
altrom.inc
192b74eafa8debf2248ea73881e77f91
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
lpm_mux.inc
dd87bed9959d6126db09970164b7ba6
a_rdenreg.inc
3fcdce755959d5a8afbe64788d21fb
altdpram.inc
2f9e6727b678ffd76e72bc5a95a2630
}
# macro_sequence

# end
# entity
altsyncram_6sb1
# storage
db|DAC904_HighSpeedDAC.(30).cnf
db|DAC904_HighSpeedDAC.(30).cnf
# case_insensitive
# source_file
db|altsyncram_6sb1.tdf
6787a650516777fa416549d5ce38f8a
7
# used_port {
q_a9
-1
3
q_a8
-1
3
q_a7
-1
3
q_a6
-1
3
q_a5
-1
3
q_a4
-1
3
q_a3
-1
3
q_a2
-1
3
q_a13
-1
3
q_a12
-1
3
q_a11
-1
3
q_a10
-1
3
q_a1
-1
3
q_a0
-1
3
clock0
-1
3
address_a9
-1
3
address_a8
-1
3
address_a7
-1
3
address_a6
-1
3
address_a5
-1
3
address_a4
-1
3
address_a3
-1
3
address_a2
-1
3
address_a13
-1
3
address_a12
-1
3
address_a11
-1
3
address_a10
-1
3
address_a1
-1
3
address_a0
-1
3
}
# memory_file {

0
}
# macro_sequence

# end
# entity
altpll
# storage
db|DAC904_HighSpeedDAC.(31).cnf
db|DAC904_HighSpeedDAC.(31).cnf
# case_insensitive
# source_file
altpll.tdf
1432cd46f66d9cd9c995c4e7995ff8
7
# user_parameter {
OPERATION_MODE
NORMAL
PARAMETER_UNKNOWN
USR
PLL_TYPE
AUTO
PARAMETER_UNKNOWN
USR
LPM_HINT
CBX_MODULE_PREFIX=pll165m
PARAMETER_UNKNOWN
USR
QUALIFY_CONF_DONE
OFF
PARAMETER_UNKNOWN
DEF
COMPENSATE_CLOCK
CLK0
PARAMETER_UNKNOWN
USR
SCAN_CHAIN
LONG
PARAMETER_UNKNOWN
DEF
PRIMARY_CLOCK
INCLK0
PARAMETER_UNKNOWN
DEF
INCLK0_INPUT_FREQUENCY
20000
PARAMETER_SIGNED_DEC
USR
INCLK1_INPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
GATE_LOCK_SIGNAL
NO
PARAMETER_UNKNOWN
DEF
GATE_LOCK_COUNTER
0
PARAMETER_UNKNOWN
DEF
LOCK_HIGH
1
PARAMETER_UNKNOWN
DEF
LOCK_LOW
1
PARAMETER_UNKNOWN
DEF
VALID_LOCK_MULTIPLIER
1
PARAMETER_UNKNOWN
DEF
INVALID_LOCK_MULTIPLIER
5
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_ON_LOSSCLK
OFF
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_ON_GATED_LOCK
OFF
PARAMETER_UNKNOWN
DEF
ENABLE_SWITCH_OVER_COUNTER
OFF
PARAMETER_UNKNOWN
DEF
SKIP_VCO
OFF
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_COUNTER
0
PARAMETER_UNKNOWN
DEF
SWITCH_OVER_TYPE
AUTO
PARAMETER_UNKNOWN
DEF
FEEDBACK_SOURCE
EXTCLK0
PARAMETER_UNKNOWN
DEF
BANDWIDTH
0
PARAMETER_UNKNOWN
DEF
BANDWIDTH_TYPE
AUTO
PARAMETER_UNKNOWN
USR
SPREAD_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
DOWN_SPREAD
0
PARAMETER_UNKNOWN
DEF
SELF_RESET_ON_GATED_LOSS_LOCK
OFF
PARAMETER_UNKNOWN
DEF
SELF_RESET_ON_LOSS_LOCK
OFF
PARAMETER_UNKNOWN
USR
CLK9_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK8_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK7_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK6_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
CLK5_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK4_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK3_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK2_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
CLK1_MULTIPLY_BY
33
PARAMETER_SIGNED_DEC
USR
CLK0_MULTIPLY_BY
33
PARAMETER_SIGNED_DEC
USR
CLK9_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK8_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK7_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK6_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
CLK5_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK4_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK3_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK2_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
CLK1_DIVIDE_BY
20
PARAMETER_SIGNED_DEC
USR
CLK0_DIVIDE_BY
25
PARAMETER_SIGNED_DEC
USR
CLK9_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK8_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK7_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK6_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK5_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK4_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK3_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK2_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
CLK1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
USR
CLK0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
USR
CLK5_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK4_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
CLK9_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK8_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK7_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK6_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK5_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK4_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK3_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK2_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
CLK1_DUTY_CYCLE
50
PARAMETER_SIGNED_DEC
USR
CLK0_DUTY_CYCLE
50
PARAMETER_SIGNED_DEC
USR
CLK9_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK8_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK7_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK6_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK5_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK4_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK3_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK2_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK1_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK0_USE_EVEN_COUNTER_MODE
OFF
PARAMETER_UNKNOWN
DEF
CLK9_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK8_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK7_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK6_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK5_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK4_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK3_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK2_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK1_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
CLK0_USE_EVEN_COUNTER_VALUE
OFF
PARAMETER_UNKNOWN
DEF
LOCK_WINDOW_UI
 0.05
PARAMETER_UNKNOWN
DEF
LOCK_WINDOW_UI_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
VCO_RANGE_DETECTOR_LOW_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
VCO_RANGE_DETECTOR_HIGH_BITS
UNUSED
PARAMETER_UNKNOWN
DEF
DPA_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
DPA_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
DPA_DIVIDER
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK2_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK1_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK0_MULTIPLY_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK3_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK2_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK1_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK0_DIVIDE_BY
1
PARAMETER_UNKNOWN
DEF
EXTCLK3_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK2_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK2_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK1_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
EXTCLK0_DUTY_CYCLE
50
PARAMETER_UNKNOWN
DEF
VCO_MULTIPLY_BY
0
PARAMETER_UNKNOWN
DEF
VCO_DIVIDE_BY
0
PARAMETER_UNKNOWN
DEF
SCLKOUT0_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
SCLKOUT1_PHASE_SHIFT
0
PARAMETER_UNKNOWN
DEF
VCO_MIN
0
PARAMETER_UNKNOWN
DEF
VCO_MAX
0
PARAMETER_UNKNOWN
DEF
VCO_CENTER
0
PARAMETER_UNKNOWN
DEF
PFD_MIN
0
PARAMETER_UNKNOWN
DEF
PFD_MAX
0
PARAMETER_UNKNOWN
DEF
M_INITIAL
0
PARAMETER_UNKNOWN
DEF
M
0
PARAMETER_UNKNOWN
DEF
N
1
PARAMETER_UNKNOWN
DEF
M2
1
PARAMETER_UNKNOWN
DEF
N2
1
PARAMETER_UNKNOWN
DEF
SS
1
PARAMETER_UNKNOWN
DEF
C0_HIGH
0
PARAMETER_UNKNOWN
DEF
C1_HIGH
0
PARAMETER_UNKNOWN
DEF
C2_HIGH
0
PARAMETER_UNKNOWN
DEF
C3_HIGH
0
PARAMETER_UNKNOWN
DEF
C4_HIGH
0
PARAMETER_UNKNOWN
DEF
C5_HIGH
0
PARAMETER_UNKNOWN
DEF
C6_HIGH
0
PARAMETER_UNKNOWN
DEF
C7_HIGH
0
PARAMETER_UNKNOWN
DEF
C8_HIGH
0
PARAMETER_UNKNOWN
DEF
C9_HIGH
0
PARAMETER_UNKNOWN
DEF
C0_LOW
0
PARAMETER_UNKNOWN
DEF
C1_LOW
0
PARAMETER_UNKNOWN
DEF
C2_LOW
0
PARAMETER_UNKNOWN
DEF
C3_LOW
0
PARAMETER_UNKNOWN
DEF
C4_LOW
0
PARAMETER_UNKNOWN
DEF
C5_LOW
0
PARAMETER_UNKNOWN
DEF
C6_LOW
0
PARAMETER_UNKNOWN
DEF
C7_LOW
0
PARAMETER_UNKNOWN
DEF
C8_LOW
0
PARAMETER_UNKNOWN
DEF
C9_LOW
0
PARAMETER_UNKNOWN
DEF
C0_INITIAL
0
PARAMETER_UNKNOWN
DEF
C1_INITIAL
0
PARAMETER_UNKNOWN
DEF
C2_INITIAL
0
PARAMETER_UNKNOWN
DEF
C3_INITIAL
0
PARAMETER_UNKNOWN
DEF
C4_INITIAL
0
PARAMETER_UNKNOWN
DEF
C5_INITIAL
0
PARAMETER_UNKNOWN
DEF
C6_INITIAL
0
PARAMETER_UNKNOWN
DEF
C7_INITIAL
0
PARAMETER_UNKNOWN
DEF
C8_INITIAL
0
PARAMETER_UNKNOWN
DEF
C9_INITIAL
0
PARAMETER_UNKNOWN
DEF
C0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C4_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C5_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C6_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C7_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C8_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C9_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
C0_PH
0
PARAMETER_UNKNOWN
DEF
C1_PH
0
PARAMETER_UNKNOWN
DEF
C2_PH
0
PARAMETER_UNKNOWN
DEF
C3_PH
0
PARAMETER_UNKNOWN
DEF
C4_PH
0
PARAMETER_UNKNOWN
DEF
C5_PH
0
PARAMETER_UNKNOWN
DEF
C6_PH
0
PARAMETER_UNKNOWN
DEF
C7_PH
0
PARAMETER_UNKNOWN
DEF
C8_PH
0
PARAMETER_UNKNOWN
DEF
C9_PH
0
PARAMETER_UNKNOWN
DEF
L0_HIGH
1
PARAMETER_UNKNOWN
DEF
L1_HIGH
1
PARAMETER_UNKNOWN
DEF
G0_HIGH
1
PARAMETER_UNKNOWN
DEF
G1_HIGH
1
PARAMETER_UNKNOWN
DEF
G2_HIGH
1
PARAMETER_UNKNOWN
DEF
G3_HIGH
1
PARAMETER_UNKNOWN
DEF
E0_HIGH
1
PARAMETER_UNKNOWN
DEF
E1_HIGH
1
PARAMETER_UNKNOWN
DEF
E2_HIGH
1
PARAMETER_UNKNOWN
DEF
E3_HIGH
1
PARAMETER_UNKNOWN
DEF
L0_LOW
1
PARAMETER_UNKNOWN
DEF
L1_LOW
1
PARAMETER_UNKNOWN
DEF
G0_LOW
1
PARAMETER_UNKNOWN
DEF
G1_LOW
1
PARAMETER_UNKNOWN
DEF
G2_LOW
1
PARAMETER_UNKNOWN
DEF
G3_LOW
1
PARAMETER_UNKNOWN
DEF
E0_LOW
1
PARAMETER_UNKNOWN
DEF
E1_LOW
1
PARAMETER_UNKNOWN
DEF
E2_LOW
1
PARAMETER_UNKNOWN
DEF
E3_LOW
1
PARAMETER_UNKNOWN
DEF
L0_INITIAL
1
PARAMETER_UNKNOWN
DEF
L1_INITIAL
1
PARAMETER_UNKNOWN
DEF
G0_INITIAL
1
PARAMETER_UNKNOWN
DEF
G1_INITIAL
1
PARAMETER_UNKNOWN
DEF
G2_INITIAL
1
PARAMETER_UNKNOWN
DEF
G3_INITIAL
1
PARAMETER_UNKNOWN
DEF
E0_INITIAL
1
PARAMETER_UNKNOWN
DEF
E1_INITIAL
1
PARAMETER_UNKNOWN
DEF
E2_INITIAL
1
PARAMETER_UNKNOWN
DEF
E3_INITIAL
1
PARAMETER_UNKNOWN
DEF
L0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
L1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
G3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E0_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E1_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E2_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
E3_MODE
BYPASS
PARAMETER_UNKNOWN
DEF
L0_PH
0
PARAMETER_UNKNOWN
DEF
L1_PH
0
PARAMETER_UNKNOWN
DEF
G0_PH
0
PARAMETER_UNKNOWN
DEF
G1_PH
0
PARAMETER_UNKNOWN
DEF
G2_PH
0
PARAMETER_UNKNOWN
DEF
G3_PH
0
PARAMETER_UNKNOWN
DEF
E0_PH
0
PARAMETER_UNKNOWN
DEF
E1_PH
0
PARAMETER_UNKNOWN
DEF
E2_PH
0
PARAMETER_UNKNOWN
DEF
E3_PH
0
PARAMETER_UNKNOWN
DEF
M_PH
0
PARAMETER_UNKNOWN
DEF
C1_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C2_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C3_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C4_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C5_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C6_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C7_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C8_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
C9_USE_CASC_IN
OFF
PARAMETER_UNKNOWN
DEF
CLK0_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK1_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK2_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK3_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK4_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK5_COUNTER
G0
PARAMETER_UNKNOWN
DEF
CLK6_COUNTER
E0
PARAMETER_UNKNOWN
DEF
CLK7_COUNTER
E1
PARAMETER_UNKNOWN
DEF
CLK8_COUNTER
E2
PARAMETER_UNKNOWN
DEF
CLK9_COUNTER
E3
PARAMETER_UNKNOWN
DEF
L0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
L1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
G3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E0_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E1_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E2_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
E3_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
M_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
N_TIME_DELAY
0
PARAMETER_UNKNOWN
DEF
EXTCLK3_COUNTER
E3
PARAMETER_UNKNOWN
DEF
EXTCLK2_COUNTER
E2
PARAMETER_UNKNOWN
DEF
EXTCLK1_COUNTER
E1
PARAMETER_UNKNOWN
DEF
EXTCLK0_COUNTER
E0
PARAMETER_UNKNOWN
DEF
ENABLE0_COUNTER
L0
PARAMETER_UNKNOWN
DEF
ENABLE1_COUNTER
L0
PARAMETER_UNKNOWN
DEF
CHARGE_PUMP_CURRENT
2
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_R
 1.000000
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_C
5
PARAMETER_UNKNOWN
DEF
CHARGE_PUMP_CURRENT_BITS
9999
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_R_BITS
9999
PARAMETER_UNKNOWN
DEF
LOOP_FILTER_C_BITS
9999
PARAMETER_UNKNOWN
DEF
VCO_POST_SCALE
0
PARAMETER_UNKNOWN
DEF
CLK2_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
CLK1_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
CLK0_OUTPUT_FREQUENCY
0
PARAMETER_UNKNOWN
DEF
INTENDED_DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
PORT_CLKENA0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA4
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKENA5
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLKENA0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA2
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLKENA3
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_EXTCLK0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_EXTCLK3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKBAD0
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKBAD1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK0
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CLK1
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CLK2
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK3
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK4
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK5
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLK6
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK7
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK8
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_CLK9
PORT_UNUSED
PARAMETER_UNKNOWN
DEF
PORT_SCANDATA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANDATAOUT
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANDONE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCLKOUT1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_SCLKOUT0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_ACTIVECLOCK
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKLOSS
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_INCLK1
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_INCLK0
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_FBIN
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PLLENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_CLKSWITCH
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_ARESET
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_PFDENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANCLK
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANACLR
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANREAD
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANWRITE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_ENABLE0
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_ENABLE1
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_LOCKED
PORT_USED
PARAMETER_UNKNOWN
USR
PORT_CONFIGUPDATE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_FBOUT
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_PHASEDONE
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASESTEP
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASEUPDOWN
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_SCANCLKENA
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_PHASECOUNTERSELECT
PORT_UNUSED
PARAMETER_UNKNOWN
USR
PORT_VCOOVERRANGE
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
PORT_VCOUNDERRANGE
PORT_CONNECTIVITY
PARAMETER_UNKNOWN
DEF
M_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C0_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C1_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C2_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C3_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C4_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C5_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C6_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C7_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C8_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
C9_TEST_SOURCE
5
PARAMETER_UNKNOWN
DEF
CBXI_PARAMETER
pll165m_altpll
PARAMETER_UNKNOWN
USR
VCO_FREQUENCY_CONTROL
AUTO
PARAMETER_UNKNOWN
DEF
VCO_PHASE_SHIFT_STEP
0
PARAMETER_UNKNOWN
DEF
WIDTH_CLOCK
5
PARAMETER_SIGNED_DEC
USR
WIDTH_PHASECOUNTERSELECT
4
PARAMETER_UNKNOWN
DEF
USING_FBMIMICBIDIR_PORT
OFF
PARAMETER_UNKNOWN
DEF
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
SCAN_CHAIN_MIF_FILE
UNUSED
PARAMETER_UNKNOWN
DEF
SIM_GATE_LOCK_DEVICE_BEHAVIOR
OFF
PARAMETER_UNKNOWN
DEF
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
}
# used_port {
locked
-1
3
inclk
-1
3
clk
-1
3
areset
-1
3
scanwrite
-1
1
scanread
-1
1
scandata
-1
1
scanclk
-1
1
scanaclr
-1
1
configupdate
-1
1
clkswitch
-1
1
scanclkena
-1
2
pllena
-1
2
phaseupdown
-1
2
phasestep
-1
2
phasecounterselect
-1
2
pfdena
-1
2
fbin
-1
2
extclkena
-1
2
clkena
-1
2
}
# include_file {
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
stratix_pll.inc
a9a94c5be18105f7ae8c218a67ec9f7
cycloneii_pll.inc
c2ee779f89b3bc181df753ea85b3ef
stratixii_pll.inc
6797ab505ed70f1a221e4a213e16a6
}
# macro_sequence

# end
# entity
pll165m_altpll
# storage
db|DAC904_HighSpeedDAC.(28).cnf
db|DAC904_HighSpeedDAC.(28).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
db|pll165m_altpll.v
eb7bf71443edaebf6caa4145d234507b
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# hierarchies {
pll165m:U0_pll165m_inst|altpll:altpll_component|pll165m_altpll:auto_generated
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
altsyncram
# storage
db|DAC904_HighSpeedDAC.(32).cnf
db|DAC904_HighSpeedDAC.(32).cnf
# case_insensitive
# source_file
altsyncram.tdf
f8b1b3e6a95fdea61cd86b5880d92761
7
# user_parameter {
BYTE_SIZE_BLOCK
8
PARAMETER_UNKNOWN
DEF
AUTO_CARRY_CHAINS
ON
AUTO_CARRY
USR
IGNORE_CARRY_BUFFERS
OFF
IGNORE_CARRY
USR
AUTO_CASCADE_CHAINS
ON
AUTO_CASCADE
USR
IGNORE_CASCADE_BUFFERS
OFF
IGNORE_CASCADE
USR
WIDTH_BYTEENA
1
PARAMETER_UNKNOWN
DEF
OPERATION_MODE
ROM
PARAMETER_UNKNOWN
USR
WIDTH_A
14
PARAMETER_SIGNED_DEC
USR
WIDTHAD_A
14
PARAMETER_SIGNED_DEC
USR
NUMWORDS_A
16384
PARAMETER_SIGNED_DEC
USR
OUTDATA_REG_A
CLOCK0
PARAMETER_UNKNOWN
USR
ADDRESS_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
OUTDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
USR
WRCONTROL_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_A
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_B
1
PARAMETER_UNKNOWN
DEF
WIDTHAD_B
1
PARAMETER_UNKNOWN
DEF
NUMWORDS_B
1
PARAMETER_UNKNOWN
DEF
INDATA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
WRCONTROL_WRADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
RDCONTROL_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
ADDRESS_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
OUTDATA_REG_B
UNREGISTERED
PARAMETER_UNKNOWN
DEF
BYTEENA_REG_B
CLOCK1
PARAMETER_UNKNOWN
DEF
INDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WRCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
ADDRESS_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
OUTDATA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
RDCONTROL_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
BYTEENA_ACLR_B
NONE
PARAMETER_UNKNOWN
DEF
WIDTH_BYTEENA_A
1
PARAMETER_SIGNED_DEC
USR
WIDTH_BYTEENA_B
1
PARAMETER_UNKNOWN
DEF
RAM_BLOCK_TYPE
AUTO
PARAMETER_UNKNOWN
DEF
BYTE_SIZE
8
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_MIXED_PORTS
DONT_CARE
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_A
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
READ_DURING_WRITE_MODE_PORT_B
NEW_DATA_NO_NBE_READ
PARAMETER_UNKNOWN
DEF
INIT_FILE
../../ROM/ROM_14B_SIN/ROM_14B_SQU.mif
PARAMETER_UNKNOWN
USR
INIT_FILE_LAYOUT
PORT_A
PARAMETER_UNKNOWN
DEF
MAXIMUM_DEPTH
0
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_INPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_INPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_OUTPUT_A
BYPASS
PARAMETER_UNKNOWN
USR
CLOCK_ENABLE_OUTPUT_B
NORMAL
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_A
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
CLOCK_ENABLE_CORE_B
USE_INPUT_CLKEN
PARAMETER_UNKNOWN
DEF
ENABLE_ECC
FALSE
PARAMETER_UNKNOWN
DEF
WIDTH_ECCSTATUS
3
PARAMETER_UNKNOWN
DEF
DEVICE_FAMILY
Cyclone IV E
PARAMETER_UNKNOWN
USR
CBXI_PARAMETER
altsyncram_eib1
PARAMETER_UNKNOWN
USR
}
# used_port {
q_a
-1
3
clock0
-1
3
address_a
-1
3
wren_b
-1
1
wren_a
-1
1
addressstall_b
-1
1
addressstall_a
-1
1
aclr1
-1
1
aclr0
-1
1
rden_b
-1
2
rden_a
-1
2
data_b
-1
2
data_a
-1
2
clocken3
-1
2
clocken2
-1
2
clocken1
-1
2
clocken0
-1
2
clock1
-1
2
byteena_b
-1
2
byteena_a
-1
2
address_b
-1
2
}
# include_file {
altrom.inc
192b74eafa8debf2248ea73881e77f91
aglobal110.inc
f560f9a992734abc3e37886b3957a26a
lpm_mux.inc
dd87bed9959d6126db09970164b7ba6
a_rdenreg.inc
3fcdce755959d5a8afbe64788d21fb
altdpram.inc
2f9e6727b678ffd76e72bc5a95a2630
altram.inc
ad5518b39ffd3cf1df377e6360d1c9b6
stratix_ram_block.inc
e3a03868917fb3dd57b6ed1dd195f22
lpm_decode.inc
10da69a8bbd590d66779e7a142f73790
}
# macro_sequence

# end
# entity
altsyncram_eib1
# storage
db|DAC904_HighSpeedDAC.(33).cnf
db|DAC904_HighSpeedDAC.(33).cnf
# case_insensitive
# source_file
db|altsyncram_eib1.tdf
5bc8175f4f3fb4a466a8a31d66c02f27
7
# used_port {
q_a9
-1
3
q_a8
-1
3
q_a7
-1
3
q_a6
-1
3
q_a5
-1
3
q_a4
-1
3
q_a3
-1
3
q_a2
-1
3
q_a13
-1
3
q_a12
-1
3
q_a11
-1
3
q_a10
-1
3
q_a1
-1
3
q_a0
-1
3
clock0
-1
3
address_a9
-1
3
address_a8
-1
3
address_a7
-1
3
address_a6
-1
3
address_a5
-1
3
address_a4
-1
3
address_a3
-1
3
address_a2
-1
3
address_a13
-1
3
address_a12
-1
3
address_a11
-1
3
address_a10
-1
3
address_a1
-1
3
address_a0
-1
3
}
# memory_file {

0
}
# macro_sequence

# end
# entity
pll165m
# storage
db|DAC904_HighSpeedDAC.(16).cnf
db|DAC904_HighSpeedDAC.(16).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.3-test_kcamp|rtl|pll|pll165m|pll165m.v
bc248bfd14db39c52986e8132abb4
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
PhaseAccumulator
# storage
db|DAC904_HighSpeedDAC.(17).cnf
db|DAC904_HighSpeedDAC.(17).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.3-test_kcamp|rtl|phaseaccumulator.v
7c3592aa74e34f5228f826d436b27856
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
PhaseAcc_BITS_NUM32Mem_BITS_NUM14PhaseAcc_BITS_NUM32PhaseAcc_BITS_NUM32PhaseAcc_BITS_NUM32Mem_BITS_NUM14
# end
# entity
rom_14B_Sin
# storage
db|DAC904_HighSpeedDAC.(18).cnf
db|DAC904_HighSpeedDAC.(18).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.3-test_kcamp|rtl|rom|rom_14b_sin|rom_14b_sin.v
5e63ee5171b7e962238bbab1739dda2
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
DAC904_WriteModule
# storage
db|DAC904_HighSpeedDAC.(19).cnf
db|DAC904_HighSpeedDAC.(19).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.3-test_kcamp|rtl|dac904_writemodule.v
5856eb437edefda8122c8da7534505a
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence

# end
# entity
key_down_scan
# storage
db|DAC904_HighSpeedDAC.(20).cnf
db|DAC904_HighSpeedDAC.(20).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.3-test_kcamp|rtl|key_down_scan.v
51e1562d8c56446291ce55b44ac4c3
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# user_parameter {
KEY_WIDTH
5
PARAMETER_SIGNED_DEC
DEF
}
# macro_sequence

# end
# entity
DAC904_WriteTEST
# storage
db|DAC904_HighSpeedDAC.(1).cnf
db|DAC904_HighSpeedDAC.(1).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.3-test_kcamp|rtl|dac904_writetest.v
2f6afbad2c142f559965435eeca16c8
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence

# end
# entity
DAC904_WriteTEST
# storage
db|DAC904_HighSpeedDAC.(26).cnf
db|DAC904_HighSpeedDAC.(26).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.4-testamp+fre|rtl|dac904_writetest.v
e0dd3ad4755921395380229b3817932c
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence

# end
# entity
pll165m
# storage
db|DAC904_HighSpeedDAC.(34).cnf
db|DAC904_HighSpeedDAC.(34).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.4-testamp+fre|rtl|pll|pll165m|pll165m.v
bc248bfd14db39c52986e8132abb4
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
PhaseAccumulator
# storage
db|DAC904_HighSpeedDAC.(35).cnf
db|DAC904_HighSpeedDAC.(35).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.4-testamp+fre|rtl|phaseaccumulator.v
7c3592aa74e34f5228f826d436b27856
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
PhaseAcc_BITS_NUM32Mem_BITS_NUM14PhaseAcc_BITS_NUM32PhaseAcc_BITS_NUM32PhaseAcc_BITS_NUM32Mem_BITS_NUM14
# end
# entity
rom_14B_Sin
# storage
db|DAC904_HighSpeedDAC.(36).cnf
db|DAC904_HighSpeedDAC.(36).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.4-testamp+fre|rtl|rom|rom_14b_sin|rom_14b_sin.v
5e63ee5171b7e962238bbab1739dda2
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
DAC904_WriteModule
# storage
db|DAC904_HighSpeedDAC.(37).cnf
db|DAC904_HighSpeedDAC.(37).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.4-testamp+fre|rtl|dac904_writemodule.v
5856eb437edefda8122c8da7534505a
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence

# end
# entity
key_down_scan
# storage
db|DAC904_HighSpeedDAC.(38).cnf
db|DAC904_HighSpeedDAC.(38).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.4-testamp+fre|rtl|key_down_scan.v
51e1562d8c56446291ce55b44ac4c3
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# user_parameter {
KEY_WIDTH
5
PARAMETER_SIGNED_DEC
DEF
}
# macro_sequence

# end
# entity
pll165m
# storage
db|DAC904_HighSpeedDAC.(40).cnf
db|DAC904_HighSpeedDAC.(40).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.5-simplepnt|rtl|pll|pll165m|pll165m.v
bc248bfd14db39c52986e8132abb4
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
PhaseAccumulator
# storage
db|DAC904_HighSpeedDAC.(41).cnf
db|DAC904_HighSpeedDAC.(41).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.5-simplepnt|rtl|phaseaccumulator.v
7c3592aa74e34f5228f826d436b27856
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
PhaseAcc_BITS_NUM32Mem_BITS_NUM14PhaseAcc_BITS_NUM32PhaseAcc_BITS_NUM32PhaseAcc_BITS_NUM32Mem_BITS_NUM14
# end
# entity
DAC904_WriteModule
# storage
db|DAC904_HighSpeedDAC.(43).cnf
db|DAC904_HighSpeedDAC.(43).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.5-simplepnt|rtl|dac904_writemodule.v
5856eb437edefda8122c8da7534505a
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence

# end
# entity
key_down_scan
# storage
db|DAC904_HighSpeedDAC.(44).cnf
db|DAC904_HighSpeedDAC.(44).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.5-simplepnt|rtl|key_down_scan.v
51e1562d8c56446291ce55b44ac4c3
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# user_parameter {
KEY_WIDTH
5
PARAMETER_SIGNED_DEC
DEF
}
# macro_sequence

# end
# entity
DAC904_WriteTEST
# storage
db|DAC904_HighSpeedDAC.(39).cnf
db|DAC904_HighSpeedDAC.(39).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.5-simplepnt|rtl|dac904_writetest.v
4cf84953c336bba1ea196ad62fb6178e
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# user_parameter {
freCtrlWord_1k
00000000000000000110010110101110
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_100k
00000000001001111011100000000010
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_1M
00000001100011010011000000011000
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_10M
00001111100000111110000011111000
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_20M
00011111000001111100000111110000
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_30M
00101110100010111010001011101000
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_40M
00111110000011111000001111100000
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_45M
01000101110100010111010001011101
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_50M
01001101100100110110010011011001
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_55M
01010101010101010101010101010101
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_60M
01011101000101110100010111010001
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_65M
01100100110110010011011001001101
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_70M
01101100100110110010011011001001
PARAMETER_UNSIGNED_BIN
DEF
}
# macro_sequence

# end
# entity
rom_14B_Sin
# storage
db|DAC904_HighSpeedDAC.(42).cnf
db|DAC904_HighSpeedDAC.(42).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.5-simplepnt|rtl|rom|rom_14b_sin|rom_14b_sin.v
5e63ee5171b7e962238bbab1739dda2
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
pll165m
# storage
db|DAC904_HighSpeedDAC.(46).cnf
db|DAC904_HighSpeedDAC.(46).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.6-sinexample|rtl|pll|pll165m|pll165m.v
bc248bfd14db39c52986e8132abb4
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# hierarchies {
pll165m:U0_pll165m_inst
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
PhaseAccumulator
# storage
db|DAC904_HighSpeedDAC.(47).cnf
db|DAC904_HighSpeedDAC.(47).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.6-sinexample|rtl|phaseaccumulator.v
7c3592aa74e34f5228f826d436b27856
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# hierarchies {
PhaseAccumulator:U1_PhaseAccumulator
}
# macro_sequence
PhaseAcc_BITS_NUM32Mem_BITS_NUM14PhaseAcc_BITS_NUM32PhaseAcc_BITS_NUM32PhaseAcc_BITS_NUM32Mem_BITS_NUM14
# end
# entity
rom_14B_Sin
# storage
db|DAC904_HighSpeedDAC.(48).cnf
db|DAC904_HighSpeedDAC.(48).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.6-sinexample|rtl|rom|rom_14b_sin|rom_14b_sin.v
5e63ee5171b7e962238bbab1739dda2
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# hierarchies {
rom_14B_Sin:U2_rom_14B_Sin
}
# macro_sequence
ALTERA_RESERVED_QISALTERA_RESERVED_QIS
# end
# entity
DAC904_WriteModule
# storage
db|DAC904_HighSpeedDAC.(49).cnf
db|DAC904_HighSpeedDAC.(49).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.6-sinexample|rtl|dac904_writemodule.v
1d30339106c4a30a37a77f43ab56c6
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# hierarchies {
DAC904_WriteModule:U3_DAC904_WriteModule
}
# macro_sequence

# end
# entity
DAC904_WriteTEST
# storage
db|DAC904_HighSpeedDAC.(45).cnf
db|DAC904_HighSpeedDAC.(45).cnf
# logic_option {
AUTO_RAM_RECOGNITION
ON
}
# case_sensitive
# source_file
|ddisk|fpga|quartus|kv|dac904_highspeeddac|dac904_highspeeddac_v1.6-sinexample|rtl|dac904_writetest.v
b596d33b22763e419570d3fbff1b1dd9
8
# internal_option {
HDL_INITIAL_FANOUT_LIMIT
OFF
AUTO_RESOURCE_SHARING
OFF
AUTO_RAM_RECOGNITION
ON
AUTO_ROM_RECOGNITION
ON
IGNORE_VERILOG_INITIAL_CONSTRUCTS
OFF
VERILOG_CONSTANT_LOOP_LIMIT
5000
VERILOG_NON_CONSTANT_LOOP_LIMIT
250
}
# user_parameter {
freCtrlWord_1k
00000000000000000110010110101110
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_100k
00000000001001111011100000000010
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_1M
00000001100011010011000000011000
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_10M
00001111100000111110000011111000
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_20M
00011111000001111100000111110000
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_30M
00101110100010111010001011101000
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_40M
00111110000011111000001111100000
PARAMETER_UNSIGNED_BIN
DEF
freCtrlWord_50M
01001101100100110110010011011001
PARAMETER_UNSIGNED_BIN
DEF
}
# hierarchies {
|
}
# macro_sequence

# end
# complete

