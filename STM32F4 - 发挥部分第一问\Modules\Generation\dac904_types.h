/**
  ******************************************************************************
  * @file    dac904_types.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   DAC904通用类型定义
  ******************************************************************************
  */

#ifndef __DAC904_TYPES_H
#define __DAC904_TYPES_H

#ifdef __cplusplus
extern "C" {
#endif

/* Type definitions ----------------------------------------------------------*/
#ifndef __cplusplus
#ifndef _BOOL_DEFINED
#define _BOOL_DEFINED
typedef enum {
    DAC904_FALSE = 0,
    DAC904_TRUE = 1
} dac904_bool_t;

#define false DAC904_FALSE
#define true DAC904_TRUE
#define bool dac904_bool_t
#endif
#endif

#ifndef NULL
#define NULL ((void*)0)
#endif

#ifdef __cplusplus
}
#endif

#endif /* __DAC904_TYPES_H */

/************************ (C) COPYRIGHT DAC904移植项目组 *****END OF FILE****/
