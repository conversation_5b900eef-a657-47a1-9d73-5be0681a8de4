--altsyncram ADDRESS_ACLR_A="NONE" CBX_DECLARE_ALL_CONNECTED_PORTS="OFF" CLOCK_ENABLE_INPUT_A="BYPASS" CLOCK_ENABLE_OUTPUT_A="BYPASS" CYCLONEII_M4K_COMPATIBILITY="ON" DEVICE_FAMILY="Cyclone IV E" ENABLE_RUNTIME_MOD="NO" INIT_FILE="../ROM/ROM_14B_SIN/ROM_14B_SIN.mif" LOW_POWER_MODE="AUTO" NUMWORDS_A=16384 OPERATION_MODE="ROM" OUTDATA_ACLR_A="NONE" OUTDATA_REG_A="CLOCK0" WIDTH_A=14 WIDTH_BYTEENA_A=1 WIDTHAD_A=14 address_a clock0 q_a CARRY_CHAIN="MANUAL" CARRY_CHAIN_LENGTH=48
--VERSION_BEGIN 11.0SP1 cbx_altsyncram 2011:07:03:21:05:55:SJ cbx_cycloneii 2011:07:03:21:05:55:SJ cbx_lpm_add_sub 2011:07:03:21:05:55:SJ cbx_lpm_compare 2011:07:03:21:05:55:SJ cbx_lpm_decode 2011:07:03:21:05:55:SJ cbx_lpm_mux 2011:07:03:21:05:55:SJ cbx_mgl 2011:07:03:21:07:56:SJ cbx_stratix 2011:07:03:21:05:55:SJ cbx_stratixii 2011:07:03:21:05:55:SJ cbx_stratixiii 2011:07:03:21:05:55:SJ cbx_stratixv 2011:07:03:21:05:55:SJ cbx_util_mgl 2011:07:03:21:05:55:SJ  VERSION_END


-- Copyright (C) 1991-2011 Altera Corporation
--  Your use of Altera Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Altera Program License 
--  Subscription Agreement, Altera MegaCore Function License 
--  Agreement, or other applicable license agreement, including, 
--  without limitation, that your use is for the sole purpose of 
--  programming logic devices manufactured by Altera and sold by 
--  Altera or its authorized distributors.  Please refer to the 
--  applicable agreement for further details.


FUNCTION decode_c8a (data[0..0])
RETURNS ( eq[1..0]);
FUNCTION mux_gob (data[27..0], sel[0..0])
RETURNS ( result[13..0]);
FUNCTION cycloneive_ram_block (clk0, clk1, clr0, clr1, ena0, ena1, ena2, ena3, portaaddr[PORT_A_ADDRESS_WIDTH-1..0], portaaddrstall, portabyteenamasks[PORT_A_BYTE_ENABLE_MASK_WIDTH-1..0], portadatain[PORT_A_DATA_WIDTH-1..0], portare, portawe, portbaddr[PORT_B_ADDRESS_WIDTH-1..0], portbaddrstall, portbbyteenamasks[PORT_B_BYTE_ENABLE_MASK_WIDTH-1..0], portbdatain[PORT_B_DATA_WIDTH-1..0], portbre, portbwe)
WITH ( CLK0_CORE_CLOCK_ENABLE, CLK0_INPUT_CLOCK_ENABLE, CLK0_OUTPUT_CLOCK_ENABLE, CLK1_CORE_CLOCK_ENABLE, CLK1_INPUT_CLOCK_ENABLE, CLK1_OUTPUT_CLOCK_ENABLE, CONNECTIVITY_CHECKING, DATA_INTERLEAVE_OFFSET_IN_BITS, DATA_INTERLEAVE_WIDTH_IN_BITS, DONT_POWER_OPTIMIZE, INIT_FILE, INIT_FILE_LAYOUT, init_file_restructured, LOGICAL_RAM_NAME, mem_init0, mem_init1, mem_init2, mem_init3, mem_init4, MIXED_PORT_FEED_THROUGH_MODE, OPERATION_MODE, PORT_A_ADDRESS_CLEAR, PORT_A_ADDRESS_WIDTH = 1, PORT_A_BYTE_ENABLE_MASK_WIDTH = 1, PORT_A_BYTE_SIZE, PORT_A_DATA_OUT_CLEAR, PORT_A_DATA_OUT_CLOCK, PORT_A_DATA_WIDTH = 1, PORT_A_FIRST_ADDRESS, PORT_A_FIRST_BIT_NUMBER, PORT_A_LAST_ADDRESS, PORT_A_LOGICAL_RAM_DEPTH, PORT_A_LOGICAL_RAM_WIDTH, PORT_A_READ_DURING_WRITE_MODE, PORT_B_ADDRESS_CLEAR, PORT_B_ADDRESS_CLOCK, PORT_B_ADDRESS_WIDTH = 1, PORT_B_BYTE_ENABLE_CLOCK, PORT_B_BYTE_ENABLE_MASK_WIDTH = 1, PORT_B_BYTE_SIZE, PORT_B_DATA_IN_CLOCK, PORT_B_DATA_OUT_CLEAR, PORT_B_DATA_OUT_CLOCK, PORT_B_DATA_WIDTH = 1, PORT_B_FIRST_ADDRESS, PORT_B_FIRST_BIT_NUMBER, PORT_B_LAST_ADDRESS, PORT_B_LOGICAL_RAM_DEPTH, PORT_B_LOGICAL_RAM_WIDTH, PORT_B_READ_DURING_WRITE_MODE, PORT_B_READ_ENABLE_CLOCK, PORT_B_WRITE_ENABLE_CLOCK, POWER_UP_UNINITIALIZED, RAM_BLOCK_TYPE, SAFE_WRITE, WIDTH_ECCSTATUS)
RETURNS ( portadataout[PORT_A_DATA_WIDTH-1..0], portbdataout[PORT_B_DATA_WIDTH-1..0]);

--synthesis_resources = M9K 28 reg 2 
OPTIONS ALTERA_INTERNAL_OPTION = "OPTIMIZE_POWER_DURING_SYNTHESIS=NORMAL_COMPILATION";

SUBDESIGN altsyncram_kdb1
( 
	address_a[13..0]	:	input;
	clock0	:	input;
	q_a[13..0]	:	output;
) 
VARIABLE 
	address_reg_a[0..0] : dffe;
	out_address_reg_a[0..0] : dffe;
	rden_decode : decode_c8a;
	mux2 : mux_gob;
	ram_block1a0 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 0,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a1 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 1,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a2 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 2,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a3 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 3,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a4 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 4,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a5 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 5,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a6 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 6,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a7 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 7,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a8 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 8,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a9 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 9,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a10 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 10,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a11 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 11,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a12 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 12,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a13 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 0,
			PORT_A_FIRST_BIT_NUMBER = 13,
			PORT_A_LAST_ADDRESS = 8191,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a14 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 0,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a15 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 1,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a16 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 2,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a17 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 3,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a18 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 4,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a19 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 5,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a20 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 6,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a21 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 7,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a22 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 8,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a23 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 9,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a24 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 10,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a25 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 11,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a26 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 12,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	ram_block1a27 : cycloneive_ram_block
		WITH (
			CLK0_CORE_CLOCK_ENABLE = "ena0",
			CLK0_INPUT_CLOCK_ENABLE = "none",
			CLK0_OUTPUT_CLOCK_ENABLE = "none",
			CONNECTIVITY_CHECKING = "OFF",
			INIT_FILE = "../ROM/ROM_14B_SIN/ROM_14B_SIN.mif",
			INIT_FILE_LAYOUT = "port_a",
			LOGICAL_RAM_NAME = "ALTSYNCRAM",
			OPERATION_MODE = "rom",
			PORT_A_ADDRESS_CLEAR = "none",
			PORT_A_ADDRESS_WIDTH = 13,
			PORT_A_DATA_OUT_CLEAR = "none",
			PORT_A_DATA_OUT_CLOCK = "clock0",
			PORT_A_DATA_WIDTH = 1,
			PORT_A_FIRST_ADDRESS = 8192,
			PORT_A_FIRST_BIT_NUMBER = 13,
			PORT_A_LAST_ADDRESS = 16383,
			PORT_A_LOGICAL_RAM_DEPTH = 16384,
			PORT_A_LOGICAL_RAM_WIDTH = 14,
			RAM_BLOCK_TYPE = "AUTO"
		);
	address_a_sel[0..0]	: WIRE;
	address_a_wire[13..0]	: WIRE;
	rden_decode_addr_sel_a[0..0]	: WIRE;
	w_addr_val_a3w[0..0]	: WIRE;

BEGIN 
	address_reg_a[].clk = clock0;
	address_reg_a[].d = address_a_sel[];
	out_address_reg_a[].clk = clock0;
	out_address_reg_a[].d = address_reg_a[].q;
	rden_decode.data[] = w_addr_val_a3w[];
	mux2.data[] = ( ram_block1a[27..0].portadataout[0..0]);
	mux2.sel[] = out_address_reg_a[].q;
	ram_block1a[27..0].clk0 = clock0;
	ram_block1a[27..0].ena0 = ( rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..1], rden_decode.eq[1..0], rden_decode.eq[0..0], rden_decode.eq[0..0], rden_decode.eq[0..0], rden_decode.eq[0..0], rden_decode.eq[0..0], rden_decode.eq[0..0], rden_decode.eq[0..0], rden_decode.eq[0..0], rden_decode.eq[0..0], rden_decode.eq[0..0], rden_decode.eq[0..0], rden_decode.eq[0..0], rden_decode.eq[0..0]);
	ram_block1a[27..0].portaaddr[] = ( address_a_wire[12..0]);
	ram_block1a[27..0].portare = B"1111111111111111111111111111";
	address_a_sel[0..0] = address_a[13..13];
	address_a_wire[] = address_a[];
	q_a[] = mux2.result[];
	rden_decode_addr_sel_a[0..0] = address_a_wire[13..13];
	w_addr_val_a3w[] = rden_decode_addr_sel_a[];
END;
--VALID FILE
