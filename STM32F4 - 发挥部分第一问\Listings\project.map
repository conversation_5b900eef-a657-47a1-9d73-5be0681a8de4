Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    main.o(i.DAC904_DDS_Demo) refers to dds_dac904.o(i.DDS_DAC904_Start) for DDS_DAC904_Start
    main.o(i.DAC904_DDS_Demo) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.DAC904_DDS_Demo) refers to dds_dac904.o(i.DDS_DAC904_SetFrequency) for DDS_DAC904_SetFrequency
    main.o(i.DAC904_DDS_Demo) refers to dds_dac904.o(i.DDS_DAC904_SetAmplitude) for DDS_DAC904_SetAmplitude
    main.o(i.DAC904_DDS_Init) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main.o(i.DAC904_DDS_Init) refers to dds_dac904.o(i.DDS_DAC904_Init) for DDS_DAC904_Init
    main.o(i.DAC904_DDS_Init) refers to dds_dac904.o(i.DDS_DAC904_Start) for DDS_DAC904_Start
    main.o(i.DAC904_DDS_Init) refers to main.o(.constdata) for .constdata
    main.o(i.DAC904_DDS_Init) refers to main.o(.data) for current_frequency
    main.o(i.DAC904_Test_Amplitudes) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main.o(i.DAC904_Test_Amplitudes) refers to dds_dac904.o(i.DDS_DAC904_SetAmplitude) for DDS_DAC904_SetAmplitude
    main.o(i.DAC904_Test_Amplitudes) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.DAC904_Test_Amplitudes) refers to main.o(.constdata) for .constdata
    main.o(i.DAC904_Test_Amplitudes) refers to main.o(.data) for current_amplitude
    main.o(i.DAC904_Test_Frequencies) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main.o(i.DAC904_Test_Frequencies) refers to dds_dac904.o(i.DDS_DAC904_SetFrequency) for DDS_DAC904_SetFrequency
    main.o(i.DAC904_Test_Frequencies) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.DAC904_Test_Frequencies) refers to main.o(.constdata) for .constdata
    main.o(i.DAC904_Test_Frequencies) refers to main.o(.data) for current_frequency
    main.o(i.DAC904_Test_Waveforms) refers to dds_dac904.o(i.DDS_DAC904_SetWaveType) for DDS_DAC904_SetWaveType
    main.o(i.DAC904_Test_Waveforms) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.DAC904_Test_Waveforms) refers to main.o(.data) for current_wave_type
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_DeInit) for RCC_DeInit
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_HSEConfig) for RCC_HSEConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) for RCC_WaitForHSEStartUp
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PLLConfig) for RCC_PLLConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PLLCmd) for RCC_PLLCmd
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    main.o(i.SystemClock_Config) refers to stm32f4xx_flash.o(i.FLASH_SetLatency) for FLASH_SetLatency
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_HCLKConfig) for RCC_HCLKConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PCLK1Config) for RCC_PCLK1Config
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PCLK2Config) for RCC_PCLK2Config
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_SYSCLKConfig) for RCC_SYSCLKConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource) for RCC_GetSYSCLKSource
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to systick.o(i.SysTick_Init) for SysTick_Init
    main.o(i.main) refers to bsp.o(i.BSP_Init) for BSP_Init
    main.o(i.main) refers to main.o(i.DAC904_DDS_Init) for DAC904_DDS_Init
    main.o(i.main) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to dds_dac904.o(i.DDS_DAC904_Stop) for DDS_DAC904_Stop
    main.o(i.main) refers to dac904.o(i.DAC904_WriteData) for DAC904_WriteData
    main.o(i.main) refers to main.o(i.DAC904_DDS_Demo) for DAC904_DDS_Demo
    main.o(i.main) refers to main.o(i.DAC904_Test_Frequencies) for DAC904_Test_Frequencies
    main.o(i.main) refers to main.o(i.DAC904_Test_Amplitudes) for DAC904_Test_Amplitudes
    main.o(i.main) refers to main.o(i.DAC904_Test_Waveforms) for DAC904_Test_Waveforms
    main.o(i.main) refers to main.o(.data) for demo_mode
    stm32f4xx_it.o(i.EXTI0_IRQHandler) refers to main.o(i.EXTI0_IRQHandler_Internal) for EXTI0_IRQHandler_Internal
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(i.TimingDelay_Decrement) for TimingDelay_Decrement
    stm32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.SysTick_Handler_Internal) for SysTick_Handler_Internal
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(.data) for uwTick
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to dds_dac904.o(i.DDS_DAC904_TimerHandler) for DDS_DAC904_TimerHandler
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    misc.o(i.NVIC_Init) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_PriorityGroupConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SetVectorTable) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SystemLPConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.SysTick_CLKSourceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_CommonInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ContinuousModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DiscModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetConversionValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_RegularChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SetInjectedOffset) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SoftwareStartConv) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_VBATCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_CancelTransmit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DBGFreeze) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(i.CAN_FIFORelease) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_FilterInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to stm32f4xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetLastErrorCode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_MessagePending) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_OperatingModeRequest) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Receive) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_SlaveStartBank) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Sleep) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_TTComModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Transmit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_TransmitStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_WakeUp) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_PhaseConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_dac.o(i.DAC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetDataOutputValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetChannel1Data) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetChannel2Data) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetDualChannelData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_WaveGenerationCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_APB1PeriphConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_APB2PeriphConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_CROPCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_CaptureCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_JPEGCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_FlowControllerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCurrDataCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetFIFOStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_MemoryTargetConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_BGConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_BGStart) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_FGConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_FGStart) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_Suspend) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_DataCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BORConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BootConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_Launch) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_SetLatency) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f4xx_fsmc.o(i.FSMC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDDeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_PCCARDInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_gpio.o(i.GPIO_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinAFConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinLockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ResetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_SetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ToggleBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_Write) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_WriteBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_AutoStartDigest) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash.o(i.HASH_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_i2c.o(i.I2C_ARPCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_CalculatePEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_CheckEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DualAddressCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GeneralCallCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GenerateSTART) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GenerateSTOP) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetLastEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetPEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_i2c.o(i.I2C_NACKPositionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_OwnAddress2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_PECPositionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ReadRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Send7bitAddress) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_StretchClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_TransmitPEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_SetReload) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_CLUTCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_CLUTInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ColorKeyingConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_ltdc.o(i.LTDC_DitherCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetCDStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LIPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LayerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LayerInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ReloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_BackupAccessCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_pwr.o(i.PWR_EnterSTOPMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_OverDriveCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_PVDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_PVDLevelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_UnderDriveCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_BackupResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_I2SCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAIConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SYSCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_rng.o(i.RNG_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_rng.o(i.RNG_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_AlarmCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_BypassShadowCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CalibOutputCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CalibOutputConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_OutputConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_OutputTypeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ReadBackupRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_TamperCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperFilterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPinSelection) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WakeUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WriteBackupRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_CompandingModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_sai.o(i.SAI_FlushFIFO) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_FrameInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetFIFOStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MonoModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteFrameCounterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteValueConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_SlotInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_TRIStateConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_CEATAITCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_CommandCompletionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DataConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(i.SDIO_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_GetResponse) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendCEATACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendCommand) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendSDIOSuspendCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetPowerState) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetSDIOOperation) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetSDIOReadWaitMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_StartSDIOReadWait) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_StopSDIOReadWait) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_FullDuplexConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_CalculateCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_DataSizeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_GetCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_GetCRCPolynomial) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_SSOutputCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_TIModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_TransmitCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_BDTRConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCPreloadControl) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCxCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCxNCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC1Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC2Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC3Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC4Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CounterModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DMAConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC3Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC4Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GenerateEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture1) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture2) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture3) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture4) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ICInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_InternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_PrescalerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_RemapConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectCCDMA) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectCOM) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectHallSensor) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectInputTrigger) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOCxM) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOnePulseMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectSlaveMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetAutoreload) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetClockDivision) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare1) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare2) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare3) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare4) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_TimeBaseInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_UpdateDisableConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_UpdateRequestConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClockInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_HalfDuplexCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_usart.o(i.USART_IrDACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_IrDAConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_LINCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_OneBitMethodCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_OverSampling8Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SendBreak) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetAddress) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetGuardTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SmartCardCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SmartCardNACKCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_WakeUpConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_wwdg.o(i.WWDG_Enable) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetWindowValue) refers to main.o(i.assert_failed) for assert_failed
    systick.o(i.DWT_Init) refers to systick.o(.data) for s_dwt_initialized
    systick.o(i.Delay_ms) refers to systick.o(i.SysTick_UpdateStats) for SysTick_UpdateStats
    systick.o(i.Delay_ms) refers to systick.o(.data) for s_delay_counter
    systick.o(i.Delay_s) refers to systick.o(i.Delay_ms) for Delay_ms
    systick.o(i.Delay_us) refers to systick.o(i.SysTick_GetCalibratedDelay) for SysTick_GetCalibratedDelay
    systick.o(i.Delay_us) refers to systick.o(i.SysTick_UpdateStats) for SysTick_UpdateStats
    systick.o(i.Delay_us) refers to systick.o(.data) for s_dwt_initialized
    systick.o(i.SysTick_Calibrate) refers to systick.o(i.Delay_us) for Delay_us
    systick.o(i.SysTick_Calibrate) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_GetCalibratedDelay) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_GetStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_GetTick) refers to systick.o(.data) for g_systick_counter
    systick.o(i.SysTick_GetTimestamp_us) refers to systick.o(.data) for g_system_uptime_ms
    systick.o(i.SysTick_GetUptime_ms) refers to systick.o(.data) for g_system_uptime_ms
    systick.o(i.SysTick_Handler_Internal) refers to systick.o(.data) for g_systick_counter
    systick.o(i.SysTick_Init) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.SysTick_Init) refers to systick.o(i.DWT_Init) for DWT_Init
    systick.o(i.SysTick_Init) refers to systick.o(i.SysTick_ResetStats) for SysTick_ResetStats
    systick.o(i.SysTick_Init) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    systick.o(i.SysTick_Init) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_ResetStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_SetTemperatureCompensation) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_UpdateStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_UpdateStats) refers to systick.o(.data) for g_system_uptime_ms
    dac904.o(i.DAC904_DeInit) refers to dac904.o(i.DAC904_StopTimer) for DAC904_StopTimer
    dac904.o(i.DAC904_DeInit) refers to dac904.o(i.DAC904_Timer_DeConfig) for DAC904_Timer_DeConfig
    dac904.o(i.DAC904_DeInit) refers to dac904.o(i.DAC904_GPIO_DeConfig) for DAC904_GPIO_DeConfig
    dac904.o(i.DAC904_DeInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    dac904.o(i.DAC904_DeInit) refers to dac904.o(.bss) for g_dac904_handle
    dac904.o(i.DAC904_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    dac904.o(i.DAC904_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    dac904.o(i.DAC904_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    dac904.o(i.DAC904_GPIO_Config) refers to dac904.o(.bss) for g_dac904_handle
    dac904.o(i.DAC904_GPIO_DeConfig) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    dac904.o(i.DAC904_GetHandle) refers to dac904.o(.bss) for g_dac904_handle
    dac904.o(i.DAC904_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    dac904.o(i.DAC904_Init) refers to dac904.o(i.DAC904_GPIO_Config) for DAC904_GPIO_Config
    dac904.o(i.DAC904_Init) refers to dac904.o(i.DAC904_Timer_Config) for DAC904_Timer_Config
    dac904.o(i.DAC904_Init) refers to dac904.o(i.DAC904_GPIO_DeConfig) for DAC904_GPIO_DeConfig
    dac904.o(i.DAC904_Init) refers to dac904.o(i.DAC904_WriteData) for DAC904_WriteData
    dac904.o(i.DAC904_Init) refers to dac904.o(.bss) for g_dac904_handle
    dac904.o(i.DAC904_SetVoltage) refers to dac904.o(i.DAC904_WriteData) for DAC904_WriteData
    dac904.o(i.DAC904_StartTimer) refers to dac904.o(i.DAC904_StopTimer) for DAC904_StopTimer
    dac904.o(i.DAC904_StartTimer) refers to dac904.o(i.DAC904_Timer_Config) for DAC904_Timer_Config
    dac904.o(i.DAC904_StartTimer) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    dac904.o(i.DAC904_StartTimer) refers to dac904.o(.bss) for g_dac904_handle
    dac904.o(i.DAC904_StopTimer) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    dac904.o(i.DAC904_StopTimer) refers to dac904.o(.bss) for g_dac904_handle
    dac904.o(i.DAC904_TimerCallback) refers to stm32f4xx_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    dac904.o(i.DAC904_TimerCallback) refers to stm32f4xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    dac904.o(i.DAC904_TimerCallback) refers to dac904.o(.bss) for g_dac904_handle
    dac904.o(i.DAC904_Timer_Config) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    dac904.o(i.DAC904_Timer_Config) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    dac904.o(i.DAC904_Timer_Config) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    dac904.o(i.DAC904_Timer_Config) refers to misc.o(i.NVIC_Init) for NVIC_Init
    dac904.o(i.DAC904_Timer_Config) refers to dac904.o(.bss) for g_dac904_handle
    dac904.o(i.DAC904_Timer_DeConfig) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    dac904.o(i.DAC904_Timer_DeConfig) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    dac904.o(i.DAC904_Timer_DeConfig) refers to misc.o(i.NVIC_Init) for NVIC_Init
    dac904.o(i.DAC904_Timer_DeConfig) refers to dac904.o(.bss) for g_dac904_handle
    dac904.o(i.DAC904_WriteData) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    dac904.o(i.DAC904_WriteData) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    dac904.o(i.DAC904_WriteData) refers to dac904.o(.bss) for g_dac904_handle
    wave_tables_14bit.o(i.GenerateSawtoothTable) refers to wave_tables_14bit.o(.bss) for g_sawtooth_table_14bit_ram
    wave_tables_14bit.o(i.GenerateSineTable) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    wave_tables_14bit.o(i.GenerateSineTable) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    wave_tables_14bit.o(i.GenerateSineTable) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    wave_tables_14bit.o(i.GenerateSineTable) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    wave_tables_14bit.o(i.GenerateSineTable) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    wave_tables_14bit.o(i.GenerateSineTable) refers to wave_tables_14bit.o(.bss) for g_sine_table_14bit_ram
    wave_tables_14bit.o(i.GenerateSquareTable) refers to wave_tables_14bit.o(.bss) for g_square_table_14bit_ram
    wave_tables_14bit.o(i.GenerateTriangleTable) refers to wave_tables_14bit.o(.bss) for g_triangle_table_14bit_ram
    wave_tables_14bit.o(i.WaveTable_GetStats) refers to wave_tables_14bit.o(i.WaveTable_GetTable) for WaveTable_GetTable
    wave_tables_14bit.o(i.WaveTable_GetTable) refers to wave_tables_14bit.o(i.WaveTable_InitializeTables) for WaveTable_InitializeTables
    wave_tables_14bit.o(i.WaveTable_GetTable) refers to wave_tables_14bit.o(.data) for g_tables_initialized
    wave_tables_14bit.o(i.WaveTable_GetValue) refers to wave_tables_14bit.o(i.WaveTable_GetTable) for WaveTable_GetTable
    wave_tables_14bit.o(i.WaveTable_InitializeTables) refers to wave_tables_14bit.o(i.GenerateSineTable) for GenerateSineTable
    wave_tables_14bit.o(i.WaveTable_InitializeTables) refers to wave_tables_14bit.o(i.GenerateSquareTable) for GenerateSquareTable
    wave_tables_14bit.o(i.WaveTable_InitializeTables) refers to wave_tables_14bit.o(i.GenerateTriangleTable) for GenerateTriangleTable
    wave_tables_14bit.o(i.WaveTable_InitializeTables) refers to wave_tables_14bit.o(i.GenerateSawtoothTable) for GenerateSawtoothTable
    wave_tables_14bit.o(i.WaveTable_InitializeTables) refers to wave_tables_14bit.o(.data) for g_tables_initialized
    wave_tables_14bit.o(i.WaveTable_Verify) refers to wave_tables_14bit.o(i.WaveTable_GetTable) for WaveTable_GetTable
    wave_tables_14bit.o(.data) refers to wave_tables_14bit.o(.bss) for g_sine_table_14bit_ram
    dds_dac904.o(i.DDS_DAC904_ApplyAmplitudeAndOffset) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_DeInit) refers to dds_dac904.o(i.DDS_DAC904_Stop) for DDS_DAC904_Stop
    dds_dac904.o(i.DDS_DAC904_DeInit) refers to dac904.o(i.DAC904_DeInit) for DAC904_DeInit
    dds_dac904.o(i.DDS_DAC904_DeInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    dds_dac904.o(i.DDS_DAC904_DeInit) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_GenerateSample) refers to dds_dac904.o(i.DDS_DAC904_ApplyAmplitudeAndOffset) for DDS_DAC904_ApplyAmplitudeAndOffset
    dds_dac904.o(i.DDS_DAC904_GenerateSample) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_GetHandle) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    dds_dac904.o(i.DDS_DAC904_Init) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    dds_dac904.o(i.DDS_DAC904_Init) refers to dac904.o(i.DAC904_Init) for DAC904_Init
    dds_dac904.o(i.DDS_DAC904_Init) refers to dds_dac904.o(i.DDS_DAC904_UpdateFrequencyWord) for DDS_DAC904_UpdateFrequencyWord
    dds_dac904.o(i.DDS_DAC904_Init) refers to dac904.o(i.DAC904_DeInit) for DAC904_DeInit
    dds_dac904.o(i.DDS_DAC904_Init) refers to dds_dac904.o(i.DDS_DAC904_UpdateAmplitudeScale) for DDS_DAC904_UpdateAmplitudeScale
    dds_dac904.o(i.DDS_DAC904_Init) refers to dds_dac904.o(i.DDS_DAC904_UpdateWaveTable) for DDS_DAC904_UpdateWaveTable
    dds_dac904.o(i.DDS_DAC904_Init) refers to dac904.o(i.DAC904_WriteData) for DAC904_WriteData
    dds_dac904.o(i.DDS_DAC904_Init) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_Init) refers to dds_dac904.o(.constdata) for .constdata
    dds_dac904.o(i.DDS_DAC904_ResetPhase) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_SetAmplitude) refers to dds_dac904.o(i.DDS_DAC904_UpdateAmplitudeScale) for DDS_DAC904_UpdateAmplitudeScale
    dds_dac904.o(i.DDS_DAC904_SetAmplitude) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_SetFrequency) refers to dds_dac904.o(i.DDS_DAC904_UpdateFrequencyWord) for DDS_DAC904_UpdateFrequencyWord
    dds_dac904.o(i.DDS_DAC904_SetFrequency) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_SetOffset) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_SetWaveType) refers to dds_dac904.o(i.DDS_DAC904_UpdateWaveTable) for DDS_DAC904_UpdateWaveTable
    dds_dac904.o(i.DDS_DAC904_SetWaveType) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_Start) refers to dac904.o(i.DAC904_StartTimer) for DAC904_StartTimer
    dds_dac904.o(i.DDS_DAC904_Start) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_Stop) refers to dac904.o(i.DAC904_StopTimer) for DAC904_StopTimer
    dds_dac904.o(i.DDS_DAC904_Stop) refers to dac904.o(i.DAC904_WriteData) for DAC904_WriteData
    dds_dac904.o(i.DDS_DAC904_Stop) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_TimerHandler) refers to stm32f4xx_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    dds_dac904.o(i.DDS_DAC904_TimerHandler) refers to stm32f4xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    dds_dac904.o(i.DDS_DAC904_TimerHandler) refers to dds_dac904.o(i.DDS_DAC904_GenerateSample) for DDS_DAC904_GenerateSample
    dds_dac904.o(i.DDS_DAC904_TimerHandler) refers to dac904.o(i.DAC904_WriteData) for DAC904_WriteData
    dds_dac904.o(i.DDS_DAC904_UpdateAmplitudeScale) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_UpdateFrequencyWord) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    dds_dac904.o(i.DDS_DAC904_UpdateFrequencyWord) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    dds_dac904.o(i.DDS_DAC904_UpdateWaveTable) refers to wave_tables_14bit.o(i.WaveTable_GetTable) for WaveTable_GetTable
    dds_dac904.o(i.DDS_DAC904_UpdateWaveTable) refers to dds_dac904.o(.bss) for g_dds_dac904_handle
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing bsp.o(.rev16_text), (4 bytes).
    Removing bsp.o(.revsh_text), (4 bytes).
    Removing bsp.o(.rrx_text), (6 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (80 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (88 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (92 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (80 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (152 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (172 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (100 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearITPendingBit), (124 bytes).
    Removing stm32f4xx_adc.o(i.ADC_Cmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonInit), (380 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMACmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (192 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (120 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetConversionValue), (76 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetFlagStatus), (128 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetITStatus), (160 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (124 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (92 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (92 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (168 bytes).
    Removing stm32f4xx_adc.o(i.ADC_Init), (448 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (368 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (88 bytes).
    Removing stm32f4xx_adc.o(i.ADC_RegularChannelConfig), (444 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (140 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartConv), (80 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (80 bytes).
    Removing stm32f4xx_adc.o(i.ADC_StructInit), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (88 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (88 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_CancelTransmit), (120 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearFlag), (220 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearITPendingBit), (304 bytes).
    Removing stm32f4xx_can.o(i.CAN_DBGFreeze), (104 bytes).
    Removing stm32f4xx_can.o(i.CAN_DeInit), (104 bytes).
    Removing stm32f4xx_can.o(i.CAN_FIFORelease), (96 bytes).
    Removing stm32f4xx_can.o(i.CAN_FilterInit), (396 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetFlagStatus), (328 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetITStatus), (428 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLastErrorCode), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_ITConfig), (184 bytes).
    Removing stm32f4xx_can.o(i.CAN_Init), (580 bytes).
    Removing stm32f4xx_can.o(i.CAN_MessagePending), (112 bytes).
    Removing stm32f4xx_can.o(i.CAN_OperatingModeRequest), (248 bytes).
    Removing stm32f4xx_can.o(i.CAN_Receive), (312 bytes).
    Removing stm32f4xx_can.o(i.CAN_SlaveStartBank), (100 bytes).
    Removing stm32f4xx_can.o(i.CAN_Sleep), (92 bytes).
    Removing stm32f4xx_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_TTComModeCmd), (184 bytes).
    Removing stm32f4xx_can.o(i.CAN_Transmit), (464 bytes).
    Removing stm32f4xx_can.o(i.CAN_TransmitStatus), (232 bytes).
    Removing stm32f4xx_can.o(i.CAN_WakeUp), (108 bytes).
    Removing stm32f4xx_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f4xx_cec.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cec.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cec.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Cmd), (84 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DMACmd), (108 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataIn), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataOut), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DeInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_FIFOFlush), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetCmdStatus), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetFlagStatus), (112 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetITStatus), (76 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_ITConfig), (108 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVInit), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVStructInit), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Init), (312 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyInit), (40 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyStructInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_PhaseConfig), (84 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_RestoreContext), (148 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_SaveContext), (264 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_StructInit), (12 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC), (540 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM), (1778 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR), (466 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB), (494 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM), (1308 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_CBC), (250 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_ECB), (226 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC), (282 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB), (258 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearFlag), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearITPendingBit), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Cmd), (104 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DMACmd), (108 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetDataOutputValue), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetFlagStatus), (96 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetITStatus), (116 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ITConfig), (124 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Init), (388 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel1Data), (100 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel2Data), (100 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetDualChannelData), (136 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd), (108 bytes).
    Removing stm32f4xx_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_WaveGenerationCmd), (128 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB1PeriphConfig), (104 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB2PeriphConfig), (104 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_Config), (100 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPConfig), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CaptureCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearFlag), (64 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Cmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_DeInit), (28 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetFlagStatus), (172 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetITStatus), (92 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ITConfig), (108 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Init), (264 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_JPEGCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ReadData), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_SetEmbeddedSynchroCodes), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_StructInit), (18 bytes).
    Removing stm32f4xx_dfsdm.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dfsdm.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dfsdm.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearFlag), (256 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearITPendingBit), (256 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Cmd), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DeInit), (508 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig), (220 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCmdStatus), (192 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrDataCounter), (180 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (192 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (184 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (580 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetITStatus), (684 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ITConfig), (272 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Init), (696 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (208 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_SetCurrDataCounter), (180 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_AbortTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGConfig), (424 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGStart), (84 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearFlag), (80 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit), (92 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeInit), (22 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig), (120 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGConfig), (424 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGStart), (84 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus), (92 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetITStatus), (124 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ITConfig), (132 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Init), (452 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig), (60 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StartTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StructInit), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Suspend), (84 bytes).
    Removing stm32f4xx_dsi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dsi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dsi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearFlag), (60 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt), (68 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetFlagStatus), (196 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetITStatus), (196 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_Init), (268 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ClearFlag), (68 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllSectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseSector), (300 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetFlagStatus), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetStatus), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ITConfig), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BORConfig), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BootConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Launch), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_RDPConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_UserConfig), (140 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramByte), (148 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord), (160 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramHalfWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashInterfaceCmd), (36 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashSleepModeCmd), (36 bytes).
    Removing stm32f4xx_fmpi2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearFlag), (148 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit), (152 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus), (148 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetITStatus), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ITConfig), (228 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDCmd), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDDeInit), (120 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDInit), (592 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd), (128 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit), (112 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit), (904 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit), (52 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd), (96 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDInit), (532 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (416 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinAFConfig), (424 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (204 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit), (268 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (268 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (164 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (280 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash.o(i.HASH_AutoStartDigest), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearFlag), (64 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DMACmd), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DataIn), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DeInit), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetDigest), (72 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetFlagStatus), (108 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetITStatus), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetInFIFOWordsNbr), (16 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ITConfig), (104 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Init), (232 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Reset), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_RestoreContext), (68 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SaveContext), (60 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr), (76 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StartDigest), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StructInit), (12 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_md5.o(i.HASH_MD5), (180 bytes).
    Removing stm32f4xx_hash_md5.o(i.HMAC_MD5), (354 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HASH_SHA1), (186 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HMAC_SHA1), (362 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ARPCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CalculatePEC), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CheckEvent), (316 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearFlag), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearITPendingBit), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Cmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMACmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DeInit), (148 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DualAddressCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GeneralCallCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTART), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTOP), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetFlagStatus), (300 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetITStatus), (244 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetLastEvent), (100 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetPEC), (76 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ITConfig), (132 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Init), (440 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_NACKPositionConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_OwnAddress2Config), (92 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_PECPositionConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReadRegister), (136 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReceiveData), (76 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Send7bitAddress), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SendData), (76 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd), (112 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StretchClockCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_TransmitPEC), (116 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus), (76 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetPrescaler), (80 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetReload), (60 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd), (64 bytes).
    Removing stm32f4xx_lptim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_lptim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_lptim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTCmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTInit), (144 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTStructInit), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearFlag), (72 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Cmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingConfig), (180 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DeInit), (22 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DitherCmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetCDStatus), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetFlagStatus), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetITStatus), (96 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetPosStatus), (44 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetRGBWidth), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ITConfig), (104 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Init), (572 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LIPConfig), (60 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAddress), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAlpha), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerCmd), (76 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerInit), (540 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPixelFormat), (106 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPosition), (160 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerSize), (116 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerStructInit), (48 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_PosStructInit), (8 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_RGBStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ReloadConfig), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_StructInit), (34 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupAccessCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_ClearFlag), (72 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTOPMode), (132 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode), (140 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_GetFlagStatus), (104 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig), (80 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDLevelConfig), (88 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_UnderDriveCmd), (80 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd), (56 bytes).
    Removing stm32f4xx_qspi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (68 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (52 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetClocksFreq), (232 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (84 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (80 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (88 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (116 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (412 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (72 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (56 bytes).
    Removing stm32f4xx_rcc.o(.data), (16 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearFlag), (64 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_rng.o(i.RNG_Cmd), (80 bytes).
    Removing stm32f4xx_rng.o(i.RNG_DeInit), (20 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetFlagStatus), (76 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetITStatus), (72 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetRandomNumber), (12 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ITConfig), (80 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmCmd), (180 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmStructInit), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig), (232 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Bcd2ToByte), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_BypassShadowCmd), (104 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ByteToBcd2), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputCmd), (104 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputConfig), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearFlag), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearITPendingBit), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig), (116 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DateStructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig), (128 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DeInit), (212 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_EnterInitMode), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ExitInitMode), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarm), (184 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarmSubSecond), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetDate), (120 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetFlagStatus), (152 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetITStatus), (148 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetStoreOperation), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetSubSecond), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTime), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStamp), (192 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStampSubSecond), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetWakeUpCounter), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ITConfig), (172 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Init), (184 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputConfig), (136 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputTypeConfig), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ReadBackupRegister), (144 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_RefClockCmd), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetAlarm), (684 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetDate), (420 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetTime), (452 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter), (72 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig), (200 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_StructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig), (188 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperCmd), (100 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperFilterConfig), (88 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinSelection), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration), (88 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig), (112 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig), (108 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampCmd), (120 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStructInit), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WaitForSynchro), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig), (108 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpCmd), (164 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteBackupRegister), (148 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd), (72 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearFlag), (108 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearITPendingBit), (108 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Cmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_CompandingModeConfig), (116 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DMACmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DeInit), (76 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FlushFIFO), (68 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameInit), (224 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameStructInit), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetCmdStatus), (80 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFIFOStatus), (72 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFlagStatus), (120 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetITStatus), (132 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ITConfig), (140 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Init), (432 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MonoModeConfig), (80 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteFrameCounterConfig), (96 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteModeCmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteValueConfig), (96 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ReceiveData), (64 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SendData), (64 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotInit), (180 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotStructInit), (16 bytes).
    Removing stm32f4xx_sai.o(i.SAI_StructInit), (30 bytes).
    Removing stm32f4xx_sai.o(i.SAI_TRIStateConfig), (96 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CEATAITCmd), (64 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearFlag), (68 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearITPendingBit), (68 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClockCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CommandCompletionCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DMACmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataConfig), (268 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DeInit), (22 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFlagStatus), (196 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetITStatus), (196 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetResponse), (80 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ITConfig), (104 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_Init), (208 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCEATACmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCommand), (180 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendSDIOSuspendCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetPowerState), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOOperation), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOReadWaitMode), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StartSDIOReadWait), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StopSDIOReadWait), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f4xx_spdifrx.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Cmd), (120 bytes).
    Removing stm32f4xx_spi.o(i.I2S_FullDuplexConfig), (276 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Init), (608 bytes).
    Removing stm32f4xx_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig), (156 bytes).
    Removing stm32f4xx_spi.o(i.SPI_CalculateCRC), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Cmd), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_DataSizeConfig), (140 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRC), (136 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRCPolynomial), (104 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearFlag), (140 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit), (152 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DMACmd), (180 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DeInit), (248 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus), (188 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetITStatus), (220 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ITConfig), (204 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ReceiveData), (120 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_SendData), (124 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Init), (416 bytes).
    Removing stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig), (160 bytes).
    Removing stm32f4xx_spi.o(i.SPI_SSOutputCmd), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TIModeCmd), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TransmitCRC), (112 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (60 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (22 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (60 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig), (220 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (68 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (60 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_tim.o(i.TI1_Config), (58 bytes).
    Removing stm32f4xx_tim.o(i.TI2_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f4xx_tim.o(i.TI4_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ARRPreloadConfig), (220 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRConfig), (256 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (236 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (136 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearFlag), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs), (112 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMACmd), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (316 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (516 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (196 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (208 bytes).
    Removing stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig), (280 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (196 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (136 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GenerateEvent), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture1), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture2), (120 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture3), (100 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture4), (100 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (264 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICInit), (528 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (128 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1Init), (468 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (96 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PolarityConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PreloadConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2Init), (468 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PolarityConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PreloadConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3Init), (444 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PolarityConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PreloadConfig), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4Init), (336 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PolarityConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PreloadConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (236 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (128 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (156 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (320 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOutputTrigger), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetAutoreload), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCounter), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC1Prescaler), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC2Prescaler), (172 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC3Prescaler), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC4Prescaler), (152 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (252 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateDisableConfig), (220 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (220 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (220 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearITPendingBit), (256 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (212 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_Cmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (208 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (344 bytes).
    Removing stm32f4xx_usart.o(i.USART_GetFlagStatus), (264 bytes).
    Removing stm32f4xx_usart.o(i.USART_GetITStatus), (372 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_ITConfig), (352 bytes).
    Removing stm32f4xx_usart.o(i.USART_Init), (556 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (184 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (184 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiveData), (152 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (156 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendData), (172 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (180 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (104 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (164 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (128 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (128 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (184 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_Enable), (64 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetCounter), (64 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetPrescaler), (84 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetWindowValue), (84 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing systick.o(i.Delay_s), (24 bytes).
    Removing systick.o(i.Delay_us), (80 bytes).
    Removing systick.o(i.SysTick_Calibrate), (96 bytes).
    Removing systick.o(i.SysTick_GetCalibratedDelay), (72 bytes).
    Removing systick.o(i.SysTick_GetStats), (24 bytes).
    Removing systick.o(i.SysTick_GetTick), (12 bytes).
    Removing systick.o(i.SysTick_GetTimestamp_us), (56 bytes).
    Removing systick.o(i.SysTick_GetUptime_ms), (12 bytes).
    Removing systick.o(i.SysTick_NonBlocking_Init), (32 bytes).
    Removing systick.o(i.SysTick_NonBlocking_IsCompleted), (48 bytes).
    Removing systick.o(i.SysTick_SetTemperatureCompensation), (24 bytes).
    Removing dac904.o(.rev16_text), (4 bytes).
    Removing dac904.o(.revsh_text), (4 bytes).
    Removing dac904.o(.rrx_text), (6 bytes).
    Removing dac904.o(i.DAC904_GetHandle), (8 bytes).
    Removing dac904.o(i.DAC904_SetVoltage), (80 bytes).
    Removing dac904.o(i.DAC904_TimerCallback), (32 bytes).
    Removing wave_tables_14bit.o(.rev16_text), (4 bytes).
    Removing wave_tables_14bit.o(.revsh_text), (4 bytes).
    Removing wave_tables_14bit.o(.rrx_text), (6 bytes).
    Removing wave_tables_14bit.o(i.WaveTable_CalculateTHD), (64 bytes).
    Removing wave_tables_14bit.o(i.WaveTable_GetStats), (98 bytes).
    Removing wave_tables_14bit.o(i.WaveTable_GetValue), (44 bytes).
    Removing wave_tables_14bit.o(i.WaveTable_Verify), (130 bytes).
    Removing dds_dac904.o(.rev16_text), (4 bytes).
    Removing dds_dac904.o(.revsh_text), (4 bytes).
    Removing dds_dac904.o(.rrx_text), (6 bytes).
    Removing dds_dac904.o(i.DDS_DAC904_DeInit), (36 bytes).
    Removing dds_dac904.o(i.DDS_DAC904_GetHandle), (8 bytes).
    Removing dds_dac904.o(i.DDS_DAC904_ResetPhase), (28 bytes).
    Removing dds_dac904.o(i.DDS_DAC904_SetOffset), (88 bytes).

854 unused section(s) (total 96096 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    Library\\misc.c                          0x00000000   Number         0  misc.o ABSOLUTE
    Library\\stm32f4xx_adc.c                 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    Library\\stm32f4xx_can.c                 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    Library\\stm32f4xx_cec.c                 0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    Library\\stm32f4xx_crc.c                 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    Library\\stm32f4xx_cryp.c                0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    Library\\stm32f4xx_cryp_aes.c            0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    Library\\stm32f4xx_cryp_des.c            0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    Library\\stm32f4xx_cryp_tdes.c           0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    Library\\stm32f4xx_dac.c                 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    Library\\stm32f4xx_dbgmcu.c              0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    Library\\stm32f4xx_dcmi.c                0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    Library\\stm32f4xx_dfsdm.c               0x00000000   Number         0  stm32f4xx_dfsdm.o ABSOLUTE
    Library\\stm32f4xx_dma.c                 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Library\\stm32f4xx_dma2d.c               0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    Library\\stm32f4xx_dsi.c                 0x00000000   Number         0  stm32f4xx_dsi.o ABSOLUTE
    Library\\stm32f4xx_exti.c                0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    Library\\stm32f4xx_flash.c               0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\\stm32f4xx_flash_ramfunc.c       0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    Library\\stm32f4xx_fmpi2c.c              0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    Library\\stm32f4xx_fsmc.c                0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    Library\\stm32f4xx_gpio.c                0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\\stm32f4xx_hash.c                0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    Library\\stm32f4xx_hash_md5.c            0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    Library\\stm32f4xx_hash_sha1.c           0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    Library\\stm32f4xx_i2c.c                 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    Library\\stm32f4xx_iwdg.c                0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    Library\\stm32f4xx_lptim.c               0x00000000   Number         0  stm32f4xx_lptim.o ABSOLUTE
    Library\\stm32f4xx_ltdc.c                0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    Library\\stm32f4xx_pwr.c                 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    Library\\stm32f4xx_qspi.c                0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    Library\\stm32f4xx_rcc.c                 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Library\\stm32f4xx_rng.c                 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    Library\\stm32f4xx_rtc.c                 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    Library\\stm32f4xx_sai.c                 0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    Library\\stm32f4xx_sdio.c                0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    Library\\stm32f4xx_spdifrx.c             0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    Library\\stm32f4xx_spi.c                 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    Library\\stm32f4xx_syscfg.c              0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    Library\\stm32f4xx_tim.c                 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    Library\\stm32f4xx_usart.c               0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Library\\stm32f4xx_wwdg.c                0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f4xx_adc.c                  0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    Library\stm32f4xx_can.c                  0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    Library\stm32f4xx_cec.c                  0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    Library\stm32f4xx_crc.c                  0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    Library\stm32f4xx_cryp.c                 0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    Library\stm32f4xx_cryp_aes.c             0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    Library\stm32f4xx_cryp_des.c             0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    Library\stm32f4xx_cryp_tdes.c            0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    Library\stm32f4xx_dac.c                  0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    Library\stm32f4xx_dbgmcu.c               0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    Library\stm32f4xx_dcmi.c                 0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    Library\stm32f4xx_dfsdm.c                0x00000000   Number         0  stm32f4xx_dfsdm.o ABSOLUTE
    Library\stm32f4xx_dma.c                  0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Library\stm32f4xx_dma2d.c                0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    Library\stm32f4xx_dsi.c                  0x00000000   Number         0  stm32f4xx_dsi.o ABSOLUTE
    Library\stm32f4xx_exti.c                 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    Library\stm32f4xx_flash.c                0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\stm32f4xx_flash_ramfunc.c        0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    Library\stm32f4xx_fmpi2c.c               0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    Library\stm32f4xx_fsmc.c                 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    Library\stm32f4xx_gpio.c                 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\stm32f4xx_hash.c                 0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    Library\stm32f4xx_hash_md5.c             0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    Library\stm32f4xx_hash_sha1.c            0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    Library\stm32f4xx_i2c.c                  0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    Library\stm32f4xx_iwdg.c                 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    Library\stm32f4xx_lptim.c                0x00000000   Number         0  stm32f4xx_lptim.o ABSOLUTE
    Library\stm32f4xx_ltdc.c                 0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    Library\stm32f4xx_pwr.c                  0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    Library\stm32f4xx_qspi.c                 0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    Library\stm32f4xx_rcc.c                  0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Library\stm32f4xx_rng.c                  0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    Library\stm32f4xx_rtc.c                  0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    Library\stm32f4xx_sai.c                  0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    Library\stm32f4xx_sdio.c                 0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    Library\stm32f4xx_spdifrx.c              0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    Library\stm32f4xx_spi.c                  0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    Library\stm32f4xx_syscfg.c               0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    Library\stm32f4xx_tim.c                  0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    Library\stm32f4xx_usart.c                0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Library\stm32f4xx_wwdg.c                 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    Modules\Core\systick.c                   0x00000000   Number         0  systick.o ABSOLUTE
    Modules\Generation\dac904.c              0x00000000   Number         0  dac904.o ABSOLUTE
    Modules\Generation\dds_dac904.c          0x00000000   Number         0  dds_dac904.o ABSOLUTE
    Modules\Generation\wave_tables_14bit.c   0x00000000   Number         0  wave_tables_14bit.o ABSOLUTE
    Modules\\Core\\systick.c                 0x00000000   Number         0  systick.o ABSOLUTE
    Modules\\Generation\\dac904.c            0x00000000   Number         0  dac904.o ABSOLUTE
    Modules\\Generation\\dds_dac904.c        0x00000000   Number         0  dds_dac904.o ABSOLUTE
    Modules\\Generation\\wave_tables_14bit.c 0x00000000   Number         0  wave_tables_14bit.o ABSOLUTE
    Start\startup_stm32f40_41xxx.s           0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    User\\bsp.c                              0x00000000   Number         0  bsp.o ABSOLUTE
    User\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    User\\stm32f4xx_it.c                     0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\\system_stm32f4xx.c                 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    User\bsp.c                               0x00000000   Number         0  bsp.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f4xx_it.c                      0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\system_stm32f4xx.c                  0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001fc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080001fe   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000202   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000204   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000206   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000208   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000208   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000208   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800020e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800020e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000212   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000212   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800021a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800021c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800021c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000220   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000228   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000228   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000268   Section      238  lludivv7m.o(.text)
    .text                                    0x08000356   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080003ba   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000408   Section        0  heapauxi.o(.text)
    .text                                    0x0800040e   Section        0  _rserrno.o(.text)
    .text                                    0x08000424   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x0800042c   Section        8  libspace.o(.text)
    .text                                    0x08000434   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800047e   Section        0  exit.o(.text)
    .text                                    0x08000490   Section        0  sys_exit.o(.text)
    .text                                    0x0800049c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800049e   Section        0  indicate_semi.o(.text)
    i.BSP_Init                               0x0800049e   Section        0  bsp.o(i.BSP_Init)
    i.BusFault_Handler                       0x080004ca   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DAC904_DDS_Demo                        0x080004ce   Section        0  main.o(i.DAC904_DDS_Demo)
    i.DAC904_DDS_Init                        0x08000540   Section        0  main.o(i.DAC904_DDS_Init)
    i.DAC904_DeInit                          0x08000594   Section        0  dac904.o(i.DAC904_DeInit)
    i.DAC904_GPIO_Config                     0x080005b8   Section        0  dac904.o(i.DAC904_GPIO_Config)
    DAC904_GPIO_Config                       0x080005b9   Thumb Code   114  dac904.o(i.DAC904_GPIO_Config)
    i.DAC904_GPIO_DeConfig                   0x08000634   Section        0  dac904.o(i.DAC904_GPIO_DeConfig)
    DAC904_GPIO_DeConfig                     0x08000635   Thumb Code    28  dac904.o(i.DAC904_GPIO_DeConfig)
    i.DAC904_Init                            0x08000654   Section        0  dac904.o(i.DAC904_Init)
    i.DAC904_StartTimer                      0x080006c8   Section        0  dac904.o(i.DAC904_StartTimer)
    i.DAC904_StopTimer                       0x08000714   Section        0  dac904.o(i.DAC904_StopTimer)
    i.DAC904_Test_Amplitudes                 0x08000740   Section        0  main.o(i.DAC904_Test_Amplitudes)
    i.DAC904_Test_Frequencies                0x080007a4   Section        0  main.o(i.DAC904_Test_Frequencies)
    i.DAC904_Test_Waveforms                  0x080007f8   Section        0  main.o(i.DAC904_Test_Waveforms)
    i.DAC904_Timer_Config                    0x0800083c   Section        0  dac904.o(i.DAC904_Timer_Config)
    DAC904_Timer_Config                      0x0800083d   Thumb Code    94  dac904.o(i.DAC904_Timer_Config)
    i.DAC904_Timer_DeConfig                  0x080008a4   Section        0  dac904.o(i.DAC904_Timer_DeConfig)
    DAC904_Timer_DeConfig                    0x080008a5   Thumb Code    50  dac904.o(i.DAC904_Timer_DeConfig)
    i.DAC904_WriteData                       0x080008dc   Section        0  dac904.o(i.DAC904_WriteData)
    i.DDS_DAC904_ApplyAmplitudeAndOffset     0x08000944   Section        0  dds_dac904.o(i.DDS_DAC904_ApplyAmplitudeAndOffset)
    DDS_DAC904_ApplyAmplitudeAndOffset       0x08000945   Thumb Code    50  dds_dac904.o(i.DDS_DAC904_ApplyAmplitudeAndOffset)
    i.DDS_DAC904_GenerateSample              0x0800097c   Section        0  dds_dac904.o(i.DDS_DAC904_GenerateSample)
    i.DDS_DAC904_Init                        0x080009e4   Section        0  dds_dac904.o(i.DDS_DAC904_Init)
    i.DDS_DAC904_SetAmplitude                0x08000aa8   Section        0  dds_dac904.o(i.DDS_DAC904_SetAmplitude)
    i.DDS_DAC904_SetFrequency                0x08000af0   Section        0  dds_dac904.o(i.DDS_DAC904_SetFrequency)
    i.DDS_DAC904_SetWaveType                 0x08000b20   Section        0  dds_dac904.o(i.DDS_DAC904_SetWaveType)
    i.DDS_DAC904_Start                       0x08000b48   Section        0  dds_dac904.o(i.DDS_DAC904_Start)
    i.DDS_DAC904_Stop                        0x08000b88   Section        0  dds_dac904.o(i.DDS_DAC904_Stop)
    i.DDS_DAC904_TimerHandler                0x08000bb4   Section        0  dds_dac904.o(i.DDS_DAC904_TimerHandler)
    i.DDS_DAC904_UpdateAmplitudeScale        0x08000bd8   Section        0  dds_dac904.o(i.DDS_DAC904_UpdateAmplitudeScale)
    DDS_DAC904_UpdateAmplitudeScale          0x08000bd9   Thumb Code    42  dds_dac904.o(i.DDS_DAC904_UpdateAmplitudeScale)
    i.DDS_DAC904_UpdateFrequencyWord         0x08000c0c   Section        0  dds_dac904.o(i.DDS_DAC904_UpdateFrequencyWord)
    DDS_DAC904_UpdateFrequencyWord           0x08000c0d   Thumb Code    34  dds_dac904.o(i.DDS_DAC904_UpdateFrequencyWord)
    i.DDS_DAC904_UpdateWaveTable             0x08000c34   Section        0  dds_dac904.o(i.DDS_DAC904_UpdateWaveTable)
    DDS_DAC904_UpdateWaveTable               0x08000c35   Thumb Code    28  dds_dac904.o(i.DDS_DAC904_UpdateWaveTable)
    i.DWT_Init                               0x08000c54   Section        0  systick.o(i.DWT_Init)
    i.DebugMon_Handler                       0x08000cb0   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x08000cb4   Section        0  systick.o(i.Delay_ms)
    i.EXTI0_IRQHandler                       0x08000ce0   Section        0  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    i.EXTI0_IRQHandler_Internal              0x08000ce8   Section        0  main.o(i.EXTI0_IRQHandler_Internal)
    i.FLASH_SetLatency                       0x08000cec   Section        0  stm32f4xx_flash.o(i.FLASH_SetLatency)
    i.GPIO_Init                              0x08000d60   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_ResetBits                         0x08000f08   Section        0  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000fb8   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.GenerateSawtoothTable                  0x08001068   Section        0  wave_tables_14bit.o(i.GenerateSawtoothTable)
    GenerateSawtoothTable                    0x08001069   Thumb Code    36  wave_tables_14bit.o(i.GenerateSawtoothTable)
    i.GenerateSineTable                      0x08001090   Section        0  wave_tables_14bit.o(i.GenerateSineTable)
    GenerateSineTable                        0x08001091   Thumb Code   114  wave_tables_14bit.o(i.GenerateSineTable)
    i.GenerateSquareTable                    0x08001120   Section        0  wave_tables_14bit.o(i.GenerateSquareTable)
    GenerateSquareTable                      0x08001121   Thumb Code    42  wave_tables_14bit.o(i.GenerateSquareTable)
    i.GenerateTriangleTable                  0x08001150   Section        0  wave_tables_14bit.o(i.GenerateTriangleTable)
    GenerateTriangleTable                    0x08001151   Thumb Code    78  wave_tables_14bit.o(i.GenerateTriangleTable)
    i.HardFault_Handler                      0x080011a4   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x080011a8   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080011ac   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x080011b0   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_SetPriority                       0x08001270   Section        0  systick.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x08001271   Thumb Code    32  systick.o(i.NVIC_SetPriority)
    i.PendSV_Handler                         0x08001298   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.RCC_AHB1PeriphClockCmd                 0x0800129c   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x08001304   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001368   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_DeInit                             0x080013d0   Section        0  stm32f4xx_rcc.o(i.RCC_DeInit)
    i.RCC_GetFlagStatus                      0x08001434   Section        0  stm32f4xx_rcc.o(i.RCC_GetFlagStatus)
    i.RCC_GetSYSCLKSource                    0x080014d8   Section        0  stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource)
    i.RCC_HCLKConfig                         0x080014e8   Section        0  stm32f4xx_rcc.o(i.RCC_HCLKConfig)
    i.RCC_HSEConfig                          0x08001548   Section        0  stm32f4xx_rcc.o(i.RCC_HSEConfig)
    i.RCC_PCLK1Config                        0x0800158c   Section        0  stm32f4xx_rcc.o(i.RCC_PCLK1Config)
    i.RCC_PCLK2Config                        0x080015e4   Section        0  stm32f4xx_rcc.o(i.RCC_PCLK2Config)
    i.RCC_PLLCmd                             0x0800163c   Section        0  stm32f4xx_rcc.o(i.RCC_PLLCmd)
    i.RCC_PLLConfig                          0x08001674   Section        0  stm32f4xx_rcc.o(i.RCC_PLLConfig)
    i.RCC_SYSCLKConfig                       0x0800172c   Section        0  stm32f4xx_rcc.o(i.RCC_SYSCLKConfig)
    i.RCC_WaitForHSEStartUp                  0x08001774   Section        0  stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp)
    i.SVC_Handler                            0x080017ac   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x080017b0   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x080017b1   Thumb Code   220  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_Handler                        0x0800189c   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SysTick_Handler_Internal               0x080018b8   Section        0  systick.o(i.SysTick_Handler_Internal)
    i.SysTick_Init                           0x080018f0   Section        0  systick.o(i.SysTick_Init)
    i.SysTick_ResetStats                     0x0800196c   Section        0  systick.o(i.SysTick_ResetStats)
    i.SysTick_UpdateStats                    0x0800198c   Section        0  systick.o(i.SysTick_UpdateStats)
    SysTick_UpdateStats                      0x0800198d   Thumb Code    80  systick.o(i.SysTick_UpdateStats)
    i.SystemClock_Config                     0x080019ec   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08001a58   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08001ac0   Section        0  stm32f4xx_it.o(i.TIM2_IRQHandler)
    i.TIM6_DAC_IRQHandler                    0x08001ac8   Section        0  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    i.TIM_ClearITPendingBit                  0x08001acc   Section        0  stm32f4xx_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08001b84   Section        0  stm32f4xx_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x08001c60   Section        0  stm32f4xx_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x08001d68   Section        0  stm32f4xx_tim.o(i.TIM_ITConfig)
    i.TIM_TimeBaseInit                       0x08001e54   Section        0  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    i.TimingDelay_Decrement                  0x08001fb8   Section        0  main.o(i.TimingDelay_Decrement)
    i.UsageFault_Handler                     0x08001fba   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.WaveTable_GetTable                     0x08001fc0   Section        0  wave_tables_14bit.o(i.WaveTable_GetTable)
    i.WaveTable_InitializeTables             0x08002010   Section        0  wave_tables_14bit.o(i.WaveTable_InitializeTables)
    WaveTable_InitializeTables               0x08002011   Thumb Code    36  wave_tables_14bit.o(i.WaveTable_InitializeTables)
    i.__ARM_fpclassifyf                      0x08002038   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__hardfp_sinf                          0x08002060   Section        0  sinf.o(i.__hardfp_sinf)
    i.__mathlib_flt_infnan                   0x080021f0   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x080021f8   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x08002208   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x08002218   Section        0  rredf.o(i.__mathlib_rredf2)
    i.assert_failed                          0x0800236c   Section        0  main.o(i.assert_failed)
    i.main                                   0x08002370   Section        0  main.o(i.main)
    x$fpl$d2f                                0x08002444   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x08002444   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$ddiv                               0x080024a8   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x080024a8   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x080024af   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfltu                              0x08002758   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x08002758   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x08002780   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08002780   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080028d4   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x080028d4   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08002970   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08002970   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$fpinit                             0x0800297c   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800297c   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x08002986   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x08002986   Number         0  fretinf.o(x$fpl$fretinf)
    .constdata                               0x08002990   Section       72  main.o(.constdata)
    x$fpl$usenofp                            0x08002990   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x080029d8   Section       16  dds_dac904.o(.constdata)
    .constdata                               0x080029e8   Section       32  rredf.o(.constdata)
    twooverpi                                0x080029e8   Data          32  rredf.o(.constdata)
    .data                                    0x20000000   Section       14  main.o(.data)
    current_frequency                        0x20000004   Data           4  main.o(.data)
    current_amplitude                        0x20000008   Data           4  main.o(.data)
    current_wave_type                        0x2000000c   Data           1  main.o(.data)
    demo_mode                                0x2000000d   Data           1  main.o(.data)
    .data                                    0x20000010   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000024   Section       25  systick.o(.data)
    s_delay_counter                          0x20000038   Data           4  systick.o(.data)
    s_dwt_initialized                        0x2000003c   Data           1  systick.o(.data)
    .data                                    0x20000040   Section       20  wave_tables_14bit.o(.data)
    g_tables_initialized                     0x20000040   Data           1  wave_tables_14bit.o(.data)
    .bss                                     0x20000054   Section       16  systick.o(.bss)
    .bss                                     0x20000064   Section       28  dac904.o(.bss)
    .bss                                     0x20000080   Section     8192  wave_tables_14bit.o(.bss)
    g_sine_table_14bit_ram                   0x20000080   Data        2048  wave_tables_14bit.o(.bss)
    g_square_table_14bit_ram                 0x20000880   Data        2048  wave_tables_14bit.o(.bss)
    g_triangle_table_14bit_ram               0x20001080   Data        2048  wave_tables_14bit.o(.bss)
    g_sawtooth_table_14bit_ram               0x20001880   Data        2048  wave_tables_14bit.o(.bss)
    .bss                                     0x20002080   Section       56  dds_dac904.o(.bss)
    .bss                                     0x200020b8   Section       96  libspace.o(.bss)
    HEAP                                     0x20002118   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x20002118   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x20002318   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x20002318   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20002718   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001fd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000205   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000209   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000209   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000209   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800021b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000221   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000229   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART1_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08000245   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __aeabi_uldivmod                         0x08000269   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000269   Thumb Code   238  lludivv7m.o(.text)
    __aeabi_memcpy4                          0x08000357   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000357   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000357   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x0800039f   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x080003bb   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080003bb   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080003bb   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080003bf   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000409   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800040b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800040d   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x0800040f   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000419   Thumb Code    12  _rserrno.o(.text)
    __aeabi_errno_addr                       0x08000425   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000425   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000425   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __user_libspace                          0x0800042d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800042d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800042d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000435   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800047f   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x08000491   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0800049d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800049d   Thumb Code     2  use_no_semi.o(.text)
    BSP_Init                                 0x0800049f   Thumb Code    44  bsp.o(i.BSP_Init)
    __semihosting_library_function           0x0800049f   Thumb Code     0  indicate_semi.o(.text)
    BusFault_Handler                         0x080004cb   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DAC904_DDS_Demo                          0x080004cf   Thumb Code   112  main.o(i.DAC904_DDS_Demo)
    DAC904_DDS_Init                          0x08000541   Thumb Code    68  main.o(i.DAC904_DDS_Init)
    DAC904_DeInit                            0x08000595   Thumb Code    32  dac904.o(i.DAC904_DeInit)
    DAC904_Init                              0x08000655   Thumb Code   106  dac904.o(i.DAC904_Init)
    DAC904_StartTimer                        0x080006c9   Thumb Code    68  dac904.o(i.DAC904_StartTimer)
    DAC904_StopTimer                         0x08000715   Thumb Code    38  dac904.o(i.DAC904_StopTimer)
    DAC904_Test_Amplitudes                   0x08000741   Thumb Code    92  main.o(i.DAC904_Test_Amplitudes)
    DAC904_Test_Frequencies                  0x080007a5   Thumb Code    76  main.o(i.DAC904_Test_Frequencies)
    DAC904_Test_Waveforms                    0x080007f9   Thumb Code    60  main.o(i.DAC904_Test_Waveforms)
    DAC904_WriteData                         0x080008dd   Thumb Code    94  dac904.o(i.DAC904_WriteData)
    DDS_DAC904_GenerateSample                0x0800097d   Thumb Code   100  dds_dac904.o(i.DDS_DAC904_GenerateSample)
    DDS_DAC904_Init                          0x080009e5   Thumb Code   184  dds_dac904.o(i.DDS_DAC904_Init)
    DDS_DAC904_SetAmplitude                  0x08000aa9   Thumb Code    66  dds_dac904.o(i.DDS_DAC904_SetAmplitude)
    DDS_DAC904_SetFrequency                  0x08000af1   Thumb Code    38  dds_dac904.o(i.DDS_DAC904_SetFrequency)
    DDS_DAC904_SetWaveType                   0x08000b21   Thumb Code    34  dds_dac904.o(i.DDS_DAC904_SetWaveType)
    DDS_DAC904_Start                         0x08000b49   Thumb Code    58  dds_dac904.o(i.DDS_DAC904_Start)
    DDS_DAC904_Stop                          0x08000b89   Thumb Code    38  dds_dac904.o(i.DDS_DAC904_Stop)
    DDS_DAC904_TimerHandler                  0x08000bb5   Thumb Code    36  dds_dac904.o(i.DDS_DAC904_TimerHandler)
    DWT_Init                                 0x08000c55   Thumb Code    78  systick.o(i.DWT_Init)
    DebugMon_Handler                         0x08000cb1   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x08000cb5   Thumb Code    40  systick.o(i.Delay_ms)
    EXTI0_IRQHandler                         0x08000ce1   Thumb Code     8  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    EXTI0_IRQHandler_Internal                0x08000ce9   Thumb Code     2  main.o(i.EXTI0_IRQHandler_Internal)
    FLASH_SetLatency                         0x08000ced   Thumb Code    84  stm32f4xx_flash.o(i.FLASH_SetLatency)
    GPIO_Init                                0x08000d61   Thumb Code   352  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_ResetBits                           0x08000f09   Thumb Code   104  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000fb9   Thumb Code   104  stm32f4xx_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x080011a5   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x080011a9   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080011ad   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x080011b1   Thumb Code   164  misc.o(i.NVIC_Init)
    PendSV_Handler                           0x08001299   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    RCC_AHB1PeriphClockCmd                   0x0800129d   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x08001305   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001369   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_DeInit                               0x080013d1   Thumb Code    82  stm32f4xx_rcc.o(i.RCC_DeInit)
    RCC_GetFlagStatus                        0x08001435   Thumb Code   134  stm32f4xx_rcc.o(i.RCC_GetFlagStatus)
    RCC_GetSYSCLKSource                      0x080014d9   Thumb Code    10  stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource)
    RCC_HCLKConfig                           0x080014e9   Thumb Code    66  stm32f4xx_rcc.o(i.RCC_HCLKConfig)
    RCC_HSEConfig                            0x08001549   Thumb Code    38  stm32f4xx_rcc.o(i.RCC_HSEConfig)
    RCC_PCLK1Config                          0x0800158d   Thumb Code    58  stm32f4xx_rcc.o(i.RCC_PCLK1Config)
    RCC_PCLK2Config                          0x080015e5   Thumb Code    60  stm32f4xx_rcc.o(i.RCC_PCLK2Config)
    RCC_PLLCmd                               0x0800163d   Thumb Code    28  stm32f4xx_rcc.o(i.RCC_PLLCmd)
    RCC_PLLConfig                            0x08001675   Thumb Code   154  stm32f4xx_rcc.o(i.RCC_PLLConfig)
    RCC_SYSCLKConfig                         0x0800172d   Thumb Code    42  stm32f4xx_rcc.o(i.RCC_SYSCLKConfig)
    RCC_WaitForHSEStartUp                    0x08001775   Thumb Code    56  stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp)
    SVC_Handler                              0x080017ad   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x0800189d   Thumb Code    22  stm32f4xx_it.o(i.SysTick_Handler)
    SysTick_Handler_Internal                 0x080018b9   Thumb Code    42  systick.o(i.SysTick_Handler_Internal)
    SysTick_Init                             0x080018f1   Thumb Code   114  systick.o(i.SysTick_Init)
    SysTick_ResetStats                       0x0800196d   Thumb Code    22  systick.o(i.SysTick_ResetStats)
    SystemClock_Config                       0x080019ed   Thumb Code   106  main.o(i.SystemClock_Config)
    SystemInit                               0x08001a59   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x08001ac1   Thumb Code     8  stm32f4xx_it.o(i.TIM2_IRQHandler)
    TIM6_DAC_IRQHandler                      0x08001ac9   Thumb Code     2  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    TIM_ClearITPendingBit                    0x08001acd   Thumb Code   108  stm32f4xx_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08001b85   Thumb Code   144  stm32f4xx_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x08001c61   Thumb Code   186  stm32f4xx_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x08001d69   Thumb Code   160  stm32f4xx_tim.o(i.TIM_ITConfig)
    TIM_TimeBaseInit                         0x08001e55   Thumb Code   278  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    TimingDelay_Decrement                    0x08001fb9   Thumb Code     2  main.o(i.TimingDelay_Decrement)
    UsageFault_Handler                       0x08001fbb   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    WaveTable_GetTable                       0x08001fc1   Thumb Code    58  wave_tables_14bit.o(i.WaveTable_GetTable)
    __ARM_fpclassifyf                        0x08002039   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_sinf                            0x08002061   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __mathlib_flt_infnan                     0x080021f1   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x080021f9   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x08002209   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x08002219   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    assert_failed                            0x0800236d   Thumb Code     4  main.o(i.assert_failed)
    main                                     0x08002371   Thumb Code   204  main.o(i.main)
    __aeabi_d2f                              0x08002445   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08002445   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_ddiv                             0x080024a9   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x080024a9   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_ui2d                             0x08002759   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08002759   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x08002781   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08002781   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080028d5   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08002971   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    _fp_init                                 0x0800297d   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08002985   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08002985   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x08002987   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __I$use$fp                               0x08002990   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08002a08   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002a28   Number         0  anon$$obj.o(Region$$Table)
    uwTick                                   0x20000000   Data           4  main.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000014   Data          16  system_stm32f4xx.o(.data)
    g_systick_counter                        0x20000024   Data           4  systick.o(.data)
    g_system_uptime_ms                       0x20000028   Data           4  systick.o(.data)
    g_systick_cal                            0x2000002c   Data          12  systick.o(.data)
    g_sine_table_14bit                       0x20000044   Data           4  wave_tables_14bit.o(.data)
    g_square_table_14bit                     0x20000048   Data           4  wave_tables_14bit.o(.data)
    g_triangle_table_14bit                   0x2000004c   Data           4  wave_tables_14bit.o(.data)
    g_sawtooth_table_14bit                   0x20000050   Data           4  wave_tables_14bit.o(.data)
    g_systick_stats                          0x20000054   Data          16  systick.o(.bss)
    g_dac904_handle                          0x20000064   Data          28  dac904.o(.bss)
    g_dds_dac904_handle                      0x20002080   Data          56  dds_dac904.o(.bss)
    __libspace_start                         0x200020b8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20002118   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002a7c, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002a28, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         5976  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         6202    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         6204    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         6206    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000002   Code   RO         6075    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001fe   0x080001fe   0x00000004   Code   RO         6084    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6087    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6090    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6092    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6094    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6097    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6099    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6101    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6103    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6105    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6107    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6109    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6111    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6113    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6115    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6117    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6121    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6123    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6125    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         6127    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000002   Code   RO         6128    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000204   0x08000204   0x00000002   Code   RO         6159    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000206   0x08000206   0x00000000   Code   RO         6185    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         6187    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         6190    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         6193    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         6195    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         6198    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000002   Code   RO         6199    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000208   0x08000208   0x00000000   Code   RO         6004    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000208   0x08000208   0x00000000   Code   RO         6038    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000208   0x08000208   0x00000006   Code   RO         6050    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         6040    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800020e   0x0800020e   0x00000004   Code   RO         6041    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         6043    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000212   0x08000212   0x00000008   Code   RO         6044    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800021a   0x0800021a   0x00000002   Code   RO         6076    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800021c   0x0800021c   0x00000000   Code   RO         6132    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800021c   0x0800021c   0x00000004   Code   RO         6133    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000220   0x08000220   0x00000006   Code   RO         6134    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000226   0x08000226   0x00000002   PAD
    0x08000228   0x08000228   0x00000040   Code   RO            4    .text               startup_stm32f40_41xxx.o
    0x08000268   0x08000268   0x000000ee   Code   RO         5968    .text               c_w.l(lludivv7m.o)
    0x08000356   0x08000356   0x00000064   Code   RO         5970    .text               c_w.l(rt_memcpy_w.o)
    0x080003ba   0x080003ba   0x0000004e   Code   RO         5972    .text               c_w.l(rt_memclr_w.o)
    0x08000408   0x08000408   0x00000006   Code   RO         5974    .text               c_w.l(heapauxi.o)
    0x0800040e   0x0800040e   0x00000016   Code   RO         6009    .text               c_w.l(_rserrno.o)
    0x08000424   0x08000424   0x00000008   Code   RO         6057    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x0800042c   0x0800042c   0x00000008   Code   RO         6059    .text               c_w.l(libspace.o)
    0x08000434   0x08000434   0x0000004a   Code   RO         6062    .text               c_w.l(sys_stackheap_outer.o)
    0x0800047e   0x0800047e   0x00000012   Code   RO         6064    .text               c_w.l(exit.o)
    0x08000490   0x08000490   0x0000000c   Code   RO         6129    .text               c_w.l(sys_exit.o)
    0x0800049c   0x0800049c   0x00000002   Code   RO         6148    .text               c_w.l(use_no_semi.o)
    0x0800049e   0x0800049e   0x00000000   Code   RO         6150    .text               c_w.l(indicate_semi.o)
    0x0800049e   0x0800049e   0x0000002c   Code   RO          395    i.BSP_Init          bsp.o
    0x080004ca   0x080004ca   0x00000004   Code   RO          242    i.BusFault_Handler  stm32f4xx_it.o
    0x080004ce   0x080004ce   0x00000070   Code   RO           13    i.DAC904_DDS_Demo   main.o
    0x0800053e   0x0800053e   0x00000002   PAD
    0x08000540   0x08000540   0x00000054   Code   RO           14    i.DAC904_DDS_Init   main.o
    0x08000594   0x08000594   0x00000024   Code   RO         5666    i.DAC904_DeInit     dac904.o
    0x080005b8   0x080005b8   0x0000007c   Code   RO         5667    i.DAC904_GPIO_Config  dac904.o
    0x08000634   0x08000634   0x00000020   Code   RO         5668    i.DAC904_GPIO_DeConfig  dac904.o
    0x08000654   0x08000654   0x00000074   Code   RO         5670    i.DAC904_Init       dac904.o
    0x080006c8   0x080006c8   0x0000004c   Code   RO         5672    i.DAC904_StartTimer  dac904.o
    0x08000714   0x08000714   0x0000002c   Code   RO         5673    i.DAC904_StopTimer  dac904.o
    0x08000740   0x08000740   0x00000064   Code   RO           15    i.DAC904_Test_Amplitudes  main.o
    0x080007a4   0x080007a4   0x00000054   Code   RO           16    i.DAC904_Test_Frequencies  main.o
    0x080007f8   0x080007f8   0x00000044   Code   RO           17    i.DAC904_Test_Waveforms  main.o
    0x0800083c   0x0800083c   0x00000068   Code   RO         5675    i.DAC904_Timer_Config  dac904.o
    0x080008a4   0x080008a4   0x00000038   Code   RO         5676    i.DAC904_Timer_DeConfig  dac904.o
    0x080008dc   0x080008dc   0x00000068   Code   RO         5677    i.DAC904_WriteData  dac904.o
    0x08000944   0x08000944   0x00000038   Code   RO         5844    i.DDS_DAC904_ApplyAmplitudeAndOffset  dds_dac904.o
    0x0800097c   0x0800097c   0x00000068   Code   RO         5846    i.DDS_DAC904_GenerateSample  dds_dac904.o
    0x080009e4   0x080009e4   0x000000c4   Code   RO         5848    i.DDS_DAC904_Init   dds_dac904.o
    0x08000aa8   0x08000aa8   0x00000048   Code   RO         5850    i.DDS_DAC904_SetAmplitude  dds_dac904.o
    0x08000af0   0x08000af0   0x00000030   Code   RO         5851    i.DDS_DAC904_SetFrequency  dds_dac904.o
    0x08000b20   0x08000b20   0x00000028   Code   RO         5853    i.DDS_DAC904_SetWaveType  dds_dac904.o
    0x08000b48   0x08000b48   0x00000040   Code   RO         5854    i.DDS_DAC904_Start  dds_dac904.o
    0x08000b88   0x08000b88   0x0000002c   Code   RO         5855    i.DDS_DAC904_Stop   dds_dac904.o
    0x08000bb4   0x08000bb4   0x00000024   Code   RO         5856    i.DDS_DAC904_TimerHandler  dds_dac904.o
    0x08000bd8   0x08000bd8   0x00000034   Code   RO         5857    i.DDS_DAC904_UpdateAmplitudeScale  dds_dac904.o
    0x08000c0c   0x08000c0c   0x00000028   Code   RO         5858    i.DDS_DAC904_UpdateFrequencyWord  dds_dac904.o
    0x08000c34   0x08000c34   0x00000020   Code   RO         5859    i.DDS_DAC904_UpdateWaveTable  dds_dac904.o
    0x08000c54   0x08000c54   0x0000005c   Code   RO         5528    i.DWT_Init          systick.o
    0x08000cb0   0x08000cb0   0x00000002   Code   RO          243    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000cb2   0x08000cb2   0x00000002   PAD
    0x08000cb4   0x08000cb4   0x0000002c   Code   RO         5529    i.Delay_ms          systick.o
    0x08000ce0   0x08000ce0   0x00000008   Code   RO          244    i.EXTI0_IRQHandler  stm32f4xx_it.o
    0x08000ce8   0x08000ce8   0x00000002   Code   RO           18    i.EXTI0_IRQHandler_Internal  main.o
    0x08000cea   0x08000cea   0x00000002   PAD
    0x08000cec   0x08000cec   0x00000074   Code   RO         1942    i.FLASH_SetLatency  stm32f4xx_flash.o
    0x08000d60   0x08000d60   0x000001a8   Code   RO         2359    i.GPIO_Init         stm32f4xx_gpio.o
    0x08000f08   0x08000f08   0x000000b0   Code   RO         2366    i.GPIO_ResetBits    stm32f4xx_gpio.o
    0x08000fb8   0x08000fb8   0x000000b0   Code   RO         2367    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x08001068   0x08001068   0x00000028   Code   RO         5761    i.GenerateSawtoothTable  wave_tables_14bit.o
    0x08001090   0x08001090   0x00000090   Code   RO         5762    i.GenerateSineTable  wave_tables_14bit.o
    0x08001120   0x08001120   0x00000030   Code   RO         5763    i.GenerateSquareTable  wave_tables_14bit.o
    0x08001150   0x08001150   0x00000054   Code   RO         5764    i.GenerateTriangleTable  wave_tables_14bit.o
    0x080011a4   0x080011a4   0x00000004   Code   RO          245    i.HardFault_Handler  stm32f4xx_it.o
    0x080011a8   0x080011a8   0x00000004   Code   RO          246    i.MemManage_Handler  stm32f4xx_it.o
    0x080011ac   0x080011ac   0x00000002   Code   RO          247    i.NMI_Handler       stm32f4xx_it.o
    0x080011ae   0x080011ae   0x00000002   PAD
    0x080011b0   0x080011b0   0x000000c0   Code   RO          419    i.NVIC_Init         misc.o
    0x08001270   0x08001270   0x00000028   Code   RO         5532    i.NVIC_SetPriority  systick.o
    0x08001298   0x08001298   0x00000002   Code   RO          248    i.PendSV_Handler    stm32f4xx_it.o
    0x0800129a   0x0800129a   0x00000002   PAD
    0x0800129c   0x0800129c   0x00000068   Code   RO         3300    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08001304   0x08001304   0x00000064   Code   RO         3309    i.RCC_APB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08001368   0x08001368   0x00000068   Code   RO         3312    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x080013d0   0x080013d0   0x00000064   Code   RO         3320    i.RCC_DeInit        stm32f4xx_rcc.o
    0x08001434   0x08001434   0x000000a4   Code   RO         3322    i.RCC_GetFlagStatus  stm32f4xx_rcc.o
    0x080014d8   0x080014d8   0x00000010   Code   RO         3324    i.RCC_GetSYSCLKSource  stm32f4xx_rcc.o
    0x080014e8   0x080014e8   0x00000060   Code   RO         3325    i.RCC_HCLKConfig    stm32f4xx_rcc.o
    0x08001548   0x08001548   0x00000044   Code   RO         3326    i.RCC_HSEConfig     stm32f4xx_rcc.o
    0x0800158c   0x0800158c   0x00000058   Code   RO         3336    i.RCC_PCLK1Config   stm32f4xx_rcc.o
    0x080015e4   0x080015e4   0x00000058   Code   RO         3337    i.RCC_PCLK2Config   stm32f4xx_rcc.o
    0x0800163c   0x0800163c   0x00000038   Code   RO         3338    i.RCC_PLLCmd        stm32f4xx_rcc.o
    0x08001674   0x08001674   0x000000b8   Code   RO         3339    i.RCC_PLLConfig     stm32f4xx_rcc.o
    0x0800172c   0x0800172c   0x00000048   Code   RO         3350    i.RCC_SYSCLKConfig  stm32f4xx_rcc.o
    0x08001774   0x08001774   0x00000038   Code   RO         3352    i.RCC_WaitForHSEStartUp  stm32f4xx_rcc.o
    0x080017ac   0x080017ac   0x00000002   Code   RO          249    i.SVC_Handler       stm32f4xx_it.o
    0x080017ae   0x080017ae   0x00000002   PAD
    0x080017b0   0x080017b0   0x000000ec   Code   RO          354    i.SetSysClock       system_stm32f4xx.o
    0x0800189c   0x0800189c   0x0000001c   Code   RO          250    i.SysTick_Handler   stm32f4xx_it.o
    0x080018b8   0x080018b8   0x00000038   Code   RO         5539    i.SysTick_Handler_Internal  systick.o
    0x080018f0   0x080018f0   0x0000007c   Code   RO         5540    i.SysTick_Init      systick.o
    0x0800196c   0x0800196c   0x00000020   Code   RO         5543    i.SysTick_ResetStats  systick.o
    0x0800198c   0x0800198c   0x00000060   Code   RO         5545    i.SysTick_UpdateStats  systick.o
    0x080019ec   0x080019ec   0x0000006a   Code   RO           19    i.SystemClock_Config  main.o
    0x08001a56   0x08001a56   0x00000002   PAD
    0x08001a58   0x08001a58   0x00000068   Code   RO          356    i.SystemInit        system_stm32f4xx.o
    0x08001ac0   0x08001ac0   0x00000008   Code   RO          251    i.TIM2_IRQHandler   stm32f4xx_it.o
    0x08001ac8   0x08001ac8   0x00000002   Code   RO          252    i.TIM6_DAC_IRQHandler  stm32f4xx_it.o
    0x08001aca   0x08001aca   0x00000002   PAD
    0x08001acc   0x08001acc   0x000000b8   Code   RO         4706    i.TIM_ClearITPendingBit  stm32f4xx_tim.o
    0x08001b84   0x08001b84   0x000000dc   Code   RO         4711    i.TIM_Cmd           stm32f4xx_tim.o
    0x08001c60   0x08001c60   0x00000108   Code   RO         4732    i.TIM_GetITStatus   stm32f4xx_tim.o
    0x08001d68   0x08001d68   0x000000ec   Code   RO         4736    i.TIM_ITConfig      stm32f4xx_tim.o
    0x08001e54   0x08001e54   0x00000164   Code   RO         4783    i.TIM_TimeBaseInit  stm32f4xx_tim.o
    0x08001fb8   0x08001fb8   0x00000002   Code   RO           20    i.TimingDelay_Decrement  main.o
    0x08001fba   0x08001fba   0x00000004   Code   RO          253    i.UsageFault_Handler  stm32f4xx_it.o
    0x08001fbe   0x08001fbe   0x00000002   PAD
    0x08001fc0   0x08001fc0   0x00000050   Code   RO         5767    i.WaveTable_GetTable  wave_tables_14bit.o
    0x08002010   0x08002010   0x00000028   Code   RO         5769    i.WaveTable_InitializeTables  wave_tables_14bit.o
    0x08002038   0x08002038   0x00000026   Code   RO         6018    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x0800205e   0x0800205e   0x00000002   PAD
    0x08002060   0x08002060   0x00000190   Code   RO         5992    i.__hardfp_sinf     m_wm.l(sinf.o)
    0x080021f0   0x080021f0   0x00000006   Code   RO         6021    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x080021f6   0x080021f6   0x00000002   PAD
    0x080021f8   0x080021f8   0x00000010   Code   RO         6023    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x08002208   0x08002208   0x00000010   Code   RO         6026    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x08002218   0x08002218   0x00000154   Code   RO         6034    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x0800236c   0x0800236c   0x00000004   Code   RO           21    i.assert_failed     main.o
    0x08002370   0x08002370   0x000000d4   Code   RO           22    i.main              main.o
    0x08002444   0x08002444   0x00000062   Code   RO         5978    x$fpl$d2f           fz_wm.l(d2f.o)
    0x080024a6   0x080024a6   0x00000002   PAD
    0x080024a8   0x080024a8   0x000002b0   Code   RO         5981    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08002758   0x08002758   0x00000026   Code   RO         5984    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x0800277e   0x0800277e   0x00000002   PAD
    0x08002780   0x08002780   0x00000154   Code   RO         5990    x$fpl$dmul          fz_wm.l(dmul.o)
    0x080028d4   0x080028d4   0x0000009c   Code   RO         6011    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08002970   0x08002970   0x0000000c   Code   RO         6013    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800297c   0x0800297c   0x0000000a   Code   RO         6144    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08002986   0x08002986   0x0000000a   Code   RO         6015    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x08002990   0x08002990   0x00000000   Code   RO         6017    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08002990   0x08002990   0x00000048   Data   RO           23    .constdata          main.o
    0x080029d8   0x080029d8   0x00000010   Data   RO         5861    .constdata          dds_dac904.o
    0x080029e8   0x080029e8   0x00000020   Data   RO         6035    .constdata          m_wm.l(rredf.o)
    0x08002a08   0x08002a08   0x00000020   Data   RO         6200    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08002a7c, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002a28, Size: 0x00002718, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002a28   0x0000000e   Data   RW           24    .data               main.o
    0x2000000e   0x08002a36   0x00000002   PAD
    0x20000010   0x08002a38   0x00000014   Data   RW          357    .data               system_stm32f4xx.o
    0x20000024   0x08002a4c   0x00000019   Data   RW         5547    .data               systick.o
    0x2000003d   0x08002a65   0x00000003   PAD
    0x20000040   0x08002a68   0x00000014   Data   RW         5772    .data               wave_tables_14bit.o
    0x20000054        -       0x00000010   Zero   RW         5546    .bss                systick.o
    0x20000064        -       0x0000001c   Zero   RW         5678    .bss                dac904.o
    0x20000080        -       0x00002000   Zero   RW         5771    .bss                wave_tables_14bit.o
    0x20002080        -       0x00000038   Zero   RW         5860    .bss                dds_dac904.o
    0x200020b8        -       0x00000060   Zero   RW         6060    .bss                c_w.l(libspace.o)
    0x20002118        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f40_41xxx.o
    0x20002318        -       0x00000400   Zero   RW            1    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        44          0          0          0          0        459   bsp.o
       692         68          0          0         28       8182   dac904.o
       784         76         16          0         56      13426   dds_dac904.o
       774         48         72         14          0      41766   main.o
       192         28          0          0          0     239328   misc.o
        64         26        392          0       1536        836   startup_stm32f40_41xxx.o
         0          0          0          0          0       1624   stm32f4xx_adc.o
         0          0          0          0          0      15356   stm32f4xx_dfsdm.o
       116         32          0          0          0        615   stm32f4xx_flash.o
       776        216          0          0          0       9460   stm32f4xx_gpio.o
        70          6          0          0          0       5473   stm32f4xx_it.o
      1296        358          0          0          0      10351   stm32f4xx_rcc.o
      1260        384          0          0          0      23834   stm32f4xx_tim.o
       340         32          0         20          0       1737   system_stm32f4xx.o
       484         76          0         25         16      32005   systick.o
       436         72          0         20       8192       5873   wave_tables_14bit.o

    ----------------------------------------------------------------------
      7346       <USER>        <GROUP>         84       9828     410325   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        18          0          0          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        22          0          0          0          0        100   _rserrno.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       238          0          0          0          0        100   lludivv7m.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        98          4          0          0          0        140   d2f.o
       688        140          0          0          0        256   ddiv.o
        38          0          0          0          0        116   dflt_clz.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         0          0          0          0          0          0   usenofp.o
        38          0          0          0          0        116   fpclassifyf.o
        38         12          0          0          0        348   funder.o
       340         24         32          0          0        160   rredf.o
       400         56          0          0          0        212   sinf.o

    ----------------------------------------------------------------------
      2902        <USER>         <GROUP>          0         96       3000   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       722         20          0          0         96       1012   c_w.l
      1352        160          0          0          0       1152   fz_wm.l
       816         92         32          0          0        836   m_wm.l

    ----------------------------------------------------------------------
      2902        <USER>         <GROUP>          0         96       3000   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     10248       1694        544         84       9924     403909   Grand Totals
     10248       1694        544         84       9924     403909   ELF Image Totals
     10248       1694        544         84          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                10792 (  10.54kB)
    Total RW  Size (RW Data + ZI Data)             10008 (   9.77kB)
    Total ROM Size (Code + RO Data + RW Data)      10876 (  10.62kB)

==============================================================================

