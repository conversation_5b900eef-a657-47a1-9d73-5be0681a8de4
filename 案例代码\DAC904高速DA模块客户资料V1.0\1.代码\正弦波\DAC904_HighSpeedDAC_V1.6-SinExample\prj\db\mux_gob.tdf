--lpm_mux CBX_DECLARE_ALL_CONNECTED_PORTS="OFF" DEVICE_FAMILY="Cyclone IV E" LPM_SIZE=2 LPM_WIDTH=14 LPM_WIDTHS=1 data result sel
--VERSION_BEGIN 11.0SP1 cbx_lpm_mux 2011:07:03:21:05:55:SJ cbx_mgl 2011:07:03:21:07:56:SJ  VERSION_END


-- Copyright (C) 1991-2011 Altera Corporation
--  Your use of Altera Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Altera Program License 
--  Subscription Agreement, Altera MegaCore Function License 
--  Agreement, or other applicable license agreement, including, 
--  without limitation, that your use is for the sole purpose of 
--  programming logic devices manufactured by Altera and sold by 
--  Altera or its authorized distributors.  Please refer to the 
--  applicable agreement for further details.



--synthesis_resources = lut 14 
SUBDESIGN mux_gob
( 
	data[27..0]	:	input;
	result[13..0]	:	output;
	sel[0..0]	:	input;
) 
VARIABLE 
	result_node[13..0]	: WIRE;
	sel_node[0..0]	: WIRE;
	w_data131w[1..0]	: WIRE;
	w_data145w[1..0]	: WIRE;
	w_data157w[1..0]	: WIRE;
	w_data169w[1..0]	: WIRE;
	w_data181w[1..0]	: WIRE;
	w_data193w[1..0]	: WIRE;
	w_data205w[1..0]	: WIRE;
	w_data217w[1..0]	: WIRE;
	w_data229w[1..0]	: WIRE;
	w_data241w[1..0]	: WIRE;
	w_data253w[1..0]	: WIRE;
	w_data265w[1..0]	: WIRE;
	w_data277w[1..0]	: WIRE;
	w_data289w[1..0]	: WIRE;

BEGIN 
	result[] = result_node[];
	result_node[] = ( ((sel_node[] & w_data289w[1..1]) # ((! sel_node[]) & w_data289w[0..0])), ((sel_node[] & w_data277w[1..1]) # ((! sel_node[]) & w_data277w[0..0])), ((sel_node[] & w_data265w[1..1]) # ((! sel_node[]) & w_data265w[0..0])), ((sel_node[] & w_data253w[1..1]) # ((! sel_node[]) & w_data253w[0..0])), ((sel_node[] & w_data241w[1..1]) # ((! sel_node[]) & w_data241w[0..0])), ((sel_node[] & w_data229w[1..1]) # ((! sel_node[]) & w_data229w[0..0])), ((sel_node[] & w_data217w[1..1]) # ((! sel_node[]) & w_data217w[0..0])), ((sel_node[] & w_data205w[1..1]) # ((! sel_node[]) & w_data205w[0..0])), ((sel_node[] & w_data193w[1..1]) # ((! sel_node[]) & w_data193w[0..0])), ((sel_node[] & w_data181w[1..1]) # ((! sel_node[]) & w_data181w[0..0])), ((sel_node[] & w_data169w[1..1]) # ((! sel_node[]) & w_data169w[0..0])), ((sel_node[] & w_data157w[1..1]) # ((! sel_node[]) & w_data157w[0..0])), ((sel_node[] & w_data145w[1..1]) # ((! sel_node[]) & w_data145w[0..0])), ((sel_node[] & w_data131w[1..1]) # ((! sel_node[]) & w_data131w[0..0])));
	sel_node[] = ( sel[0..0]);
	w_data131w[] = ( data[14..14], data[0..0]);
	w_data145w[] = ( data[15..15], data[1..1]);
	w_data157w[] = ( data[16..16], data[2..2]);
	w_data169w[] = ( data[17..17], data[3..3]);
	w_data181w[] = ( data[18..18], data[4..4]);
	w_data193w[] = ( data[19..19], data[5..5]);
	w_data205w[] = ( data[20..20], data[6..6]);
	w_data217w[] = ( data[21..21], data[7..7]);
	w_data229w[] = ( data[22..22], data[8..8]);
	w_data241w[] = ( data[23..23], data[9..9]);
	w_data253w[] = ( data[24..24], data[10..10]);
	w_data265w[] = ( data[25..25], data[11..11]);
	w_data277w[] = ( data[26..26], data[12..12]);
	w_data289w[] = ( data[27..27], data[13..13]);
END;
--VALID FILE
