--lpm_decode CBX_DECLARE_ALL_CONNECTED_PORTS="OFF" DEVICE_FAMILY="Cyclone IV E" LPM_DECODES=2 LPM_WIDTH=1 data eq
--VERSION_BEGIN 11.0SP1 cbx_cycloneii 2011:07:03:21:05:55:SJ cbx_lpm_add_sub 2011:07:03:21:05:55:SJ cbx_lpm_compare 2011:07:03:21:05:55:SJ cbx_lpm_decode 2011:07:03:21:05:55:SJ cbx_mgl 2011:07:03:21:07:56:SJ cbx_stratix 2011:07:03:21:05:55:SJ cbx_stratixii 2011:07:03:21:05:55:SJ  VERSION_END


-- Copyright (C) 1991-2011 Altera Corporation
--  Your use of Altera Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Altera Program License 
--  Subscription Agreement, Altera MegaCore Function License 
--  Agreement, or other applicable license agreement, including, 
--  without limitation, that your use is for the sole purpose of 
--  programming logic devices manufactured by Altera and sold by 
--  Altera or its authorized distributors.  Please refer to the 
--  applicable agreement for further details.



--synthesis_resources = lut 1 
SUBDESIGN decode_c8a
( 
	data[0..0]	:	input;
	eq[1..0]	:	output;
) 
VARIABLE 
	enable	: NODE;
	eq_node[1..0]	: WIRE;

BEGIN 
	enable = VCC;
	eq[] = eq_node[];
	eq_node[] = ( (data[] & enable), ((! data[]) & enable));
END;
--VALID FILE
