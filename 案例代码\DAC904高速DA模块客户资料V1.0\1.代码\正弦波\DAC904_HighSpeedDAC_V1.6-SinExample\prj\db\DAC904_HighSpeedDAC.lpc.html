<TABLE BORDER="1" cellspacing="1" cellpadding="2">
<TR valign="middle" bgcolor="#C0C0C0">
<TH>Hierarchy</TH>
<TH>Input</TH>
<TH>Constant Input</TH>
<TH>Unused Input</TH>
<TH>Floating Input</TH>
<TH>Output</TH>
<TH>Constant Output</TH>
<TH>Unused Output</TH>
<TH>Floating Output</TH>
<TH>Bidir</TH>
<TH>Constant Bidir</TH>
<TH>Unused Bidir</TH>
<TH>Input only Bidir</TH>
<TH>Output only Bidir</TH>
</TR>
<TR valign="middle">
<TD ALIGN="LEFT">U3_DAC904_WriteModule</TD>
<TD ALIGN="LEFT">16</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">14</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
</TR>
<TR valign="middle">
<TD ALIGN="LEFT">U2_rom_14B_Sin|altsyncram_component|auto_generated|mux2</TD>
<TD ALIGN="LEFT">29</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">14</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
</TR>
<TR valign="middle">
<TD ALIGN="LEFT">U2_rom_14B_Sin|altsyncram_component|auto_generated|rden_decode</TD>
<TD ALIGN="LEFT">1</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">2</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
</TR>
<TR valign="middle">
<TD ALIGN="LEFT">U2_rom_14B_Sin|altsyncram_component|auto_generated</TD>
<TD ALIGN="LEFT">15</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">14</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
</TR>
<TR valign="middle">
<TD ALIGN="LEFT">U2_rom_14B_Sin</TD>
<TD ALIGN="LEFT">15</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">14</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
</TR>
<TR valign="middle">
<TD ALIGN="LEFT">U1_PhaseAccumulator</TD>
<TD ALIGN="LEFT">34</TD>
<TD ALIGN="LEFT">32</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">32</TD>
<TD ALIGN="LEFT">14</TD>
<TD ALIGN="LEFT">32</TD>
<TD ALIGN="LEFT">32</TD>
<TD ALIGN="LEFT">32</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
</TR>
<TR valign="middle">
<TD ALIGN="LEFT">U0_pll165m_inst|altpll_component|auto_generated</TD>
<TD ALIGN="LEFT">3</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">6</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
</TR>
<TR valign="middle">
<TD ALIGN="LEFT">U0_pll165m_inst</TD>
<TD ALIGN="LEFT">2</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">3</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
<TD ALIGN="LEFT">0</TD>
</TR>
</TABLE>
