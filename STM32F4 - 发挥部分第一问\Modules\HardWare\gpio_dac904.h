/**
  ******************************************************************************
  * @file    gpio_dac904.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   DAC904高速DAC模块GPIO配置头文件
  ******************************************************************************
  * @attention
  *
  * DAC904采用14位并行数据接口 + 时钟信号
  * 引脚分配：
  * - 数据线：PE0-PE13 (14位并行数据)
  * - 时钟线：PE14 (DAC时钟信号)
  * - 电源：5V, GND
  *
  ******************************************************************************
  */

#ifndef __GPIO_DAC904_H
#define __GPIO_DAC904_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  DAC904 GPIO配置结构体
  */
typedef struct {
    GPIO_TypeDef* data_port;        ///< 数据端口 (GPIOE)
    GPIO_TypeDef* clock_port;       ///< 时钟端口 (GPIOE)
    uint16_t data_pins;             ///< 数据引脚掩码 (PE0-PE13)
    uint16_t clock_pin;             ///< 时钟引脚 (PE14)
    uint32_t gpio_speed;            ///< GPIO速度配置
} DAC904_GPIO_Config_t;

/**
  * @brief  DAC904状态枚举
  */
typedef enum {
    DAC904_OK = 0,                  ///< 操作成功
    DAC904_ERROR,                   ///< 一般错误
    DAC904_PARAM_ERROR,             ///< 参数错误
    DAC904_TIMEOUT_ERROR            ///< 超时错误
} DAC904_Status_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup DAC904_GPIO_Pins DAC904 GPIO引脚定义
  * @{
  */
#define DAC904_DATA_PORT            GPIOE
#define DAC904_CLOCK_PORT           GPIOE

// 数据引脚定义 (PE0-PE13)
#define DAC904_D0_PIN               GPIO_Pin_0
#define DAC904_D1_PIN               GPIO_Pin_1
#define DAC904_D2_PIN               GPIO_Pin_2
#define DAC904_D3_PIN               GPIO_Pin_3
#define DAC904_D4_PIN               GPIO_Pin_4
#define DAC904_D5_PIN               GPIO_Pin_5
#define DAC904_D6_PIN               GPIO_Pin_6
#define DAC904_D7_PIN               GPIO_Pin_7
#define DAC904_D8_PIN               GPIO_Pin_8
#define DAC904_D9_PIN               GPIO_Pin_9
#define DAC904_D10_PIN              GPIO_Pin_10
#define DAC904_D11_PIN              GPIO_Pin_11
#define DAC904_D12_PIN              GPIO_Pin_12
#define DAC904_D13_PIN              GPIO_Pin_13

// 时钟引脚定义 (PE14)
#define DAC904_CLK_PIN              GPIO_Pin_14

// 数据引脚组合掩码
#define DAC904_DATA_PINS_MASK       (GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | \
                                     GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7 | \
                                     GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_10 | GPIO_Pin_11 | \
                                     GPIO_Pin_12 | GPIO_Pin_13)

/**
  * @}
  */

/** @defgroup DAC904_GPIO_Speed GPIO速度配置
  * @{
  */
#define DAC904_GPIO_SPEED_LOW       GPIO_Speed_25MHz    ///< 低速模式
#define DAC904_GPIO_SPEED_MEDIUM    GPIO_Speed_50MHz    ///< 中速模式  
#define DAC904_GPIO_SPEED_HIGH      GPIO_Speed_100MHz   ///< 高速模式
/**
  * @}
  */

/** @defgroup DAC904_Data_Range 数据范围定义
  * @{
  */
#define DAC904_DATA_BITS            14                  ///< 数据位数
#define DAC904_DATA_MAX             ((1 << DAC904_DATA_BITS) - 1)  ///< 最大数据值 (16383)
#define DAC904_DATA_MID             (1 << (DAC904_DATA_BITS - 1))  ///< 中间值 (8192)
#define DAC904_DATA_MIN             0                   ///< 最小数据值
/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup DAC904_GPIO_Macros GPIO操作宏
  * @{
  */

/**
  * @brief  设置DAC904时钟信号为高电平
  */
#define DAC904_CLK_HIGH()           GPIO_SetBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN)

/**
  * @brief  设置DAC904时钟信号为低电平
  */
#define DAC904_CLK_LOW()            GPIO_ResetBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN)

/**
  * @brief  切换DAC904时钟信号
  */
#define DAC904_CLK_TOGGLE()         GPIO_ToggleBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN)

/**
  * @brief  设置DAC904并行数据
  * @param  data: 14位数据值 (0-16383)
  */
#define DAC904_SET_DATA(data)       do { \
    uint32_t temp_odr = DAC904_DATA_PORT->ODR; \
    temp_odr &= ~DAC904_DATA_PINS_MASK; \
    temp_odr |= ((data) & DAC904_DATA_MAX); \
    DAC904_DATA_PORT->ODR = temp_odr; \
} while(0)

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup DAC904_GPIO_Functions GPIO配置函数
  * @{
  */

/**
  * @brief  DAC904 GPIO初始化
  * @param  None
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_GPIO_Init(void);

/**
  * @brief  DAC904 GPIO反初始化
  * @param  None
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_GPIO_DeInit(void);

/**
  * @brief  写入14位并行数据到DAC904
  * @param  data: 14位数据值 (0-16383)
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_WriteParallel(uint16_t data);

/**
  * @brief  产生DAC904时钟脉冲
  * @param  None
  * @retval None
  */
void DAC904_ClockPulse(void);

/**
  * @brief  设置DAC904时钟频率
  * @param  frequency: 时钟频率 (Hz)
  * @retval DAC904_Status_t 操作状态
  * @note   此函数配置定时器产生指定频率的时钟信号
  */
DAC904_Status_t DAC904_SetClockFrequency(uint32_t frequency);

/**
  * @brief  获取DAC904 GPIO配置信息
  * @param  config: 配置结构体指针
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_GetGPIOConfig(DAC904_GPIO_Config_t* config);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __GPIO_DAC904_H */

/************************ (C) COPYRIGHT DAC904移植项目组 *****END OF FILE****/
