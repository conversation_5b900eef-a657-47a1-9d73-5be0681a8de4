/**
  ******************************************************************************
  * @file    wave_tables_14bit.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   14位高精度波形查找表实现
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "wave_tables_14bit.h"
#include <math.h>
#include <stdbool.h>

/* Private constants ---------------------------------------------------------*/
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

/* Private variables ---------------------------------------------------------*/

// 动态生成的波形表 (RAM中)
static uint16_t g_sine_table_14bit_ram[WAVE_TABLE_SIZE_14BIT];
static uint16_t g_square_table_14bit_ram[WAVE_TABLE_SIZE_14BIT];
static uint16_t g_triangle_table_14bit_ram[WAVE_TABLE_SIZE_14BIT];
static uint16_t g_sawtooth_table_14bit_ram[WAVE_TABLE_SIZE_14BIT];
static bool g_tables_initialized = false;

// 导出的波形表指针
const uint16_t* g_sine_table_14bit = g_sine_table_14bit_ram;
const uint16_t* g_square_table_14bit = g_square_table_14bit_ram;
const uint16_t* g_triangle_table_14bit = g_triangle_table_14bit_ram;
const uint16_t* g_sawtooth_table_14bit = g_sawtooth_table_14bit_ram;
/* Private function prototypes -----------------------------------------------*/
static void WaveTable_InitializeTables(void);
static void GenerateSineTable(void);
static void GenerateSquareTable(void);
static void GenerateTriangleTable(void);
static void GenerateSawtoothTable(void);

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  获取指定波形类型的查找表指针
  * @param  wave_type: 波形类型
  * @retval const uint16_t* 波形表指针，失败返回NULL
  */
const uint16_t* WaveTable_GetTable(Wave_Type_14bit_t wave_type)
{
    // 确保波形表已初始化
    if (!g_tables_initialized) {
        WaveTable_InitializeTables();
    }

    switch (wave_type) {
        case WAVE_TYPE_SINE:
            return g_sine_table_14bit;
        case WAVE_TYPE_SQUARE:
            return g_square_table_14bit;
        case WAVE_TYPE_TRIANGLE:
            return g_triangle_table_14bit;
        case WAVE_TYPE_SAWTOOTH:
            return g_sawtooth_table_14bit;
        default:
            return NULL;
    }
}

/**
  * @brief  从波形表获取指定索引的数值
  * @param  wave_type: 波形类型
  * @param  index: 表索引 (0-1023)
  * @retval uint16_t 波形数值，失败返回中心值
  */
uint16_t WaveTable_GetValue(Wave_Type_14bit_t wave_type, uint16_t index)
{
    // 检查参数有效性
    if (!IS_VALID_WAVE_TYPE(wave_type) || !IS_VALID_TABLE_INDEX(index)) {
        return WAVE_14BIT_CENTER;
    }

    const uint16_t* table = WaveTable_GetTable(wave_type);
    if (table == NULL) {
        return WAVE_14BIT_CENTER;
    }

    return table[index];
}

/**
  * @brief  验证波形表完整性
  * @param  wave_type: 波形类型
  * @retval bool true: 验证通过, false: 验证失败
  */
bool WaveTable_Verify(Wave_Type_14bit_t wave_type)
{
    const uint16_t* table = WaveTable_GetTable(wave_type);
    if (table == NULL) {
        return false;
    }

    // 检查数值范围
    for (uint16_t i = 0; i < WAVE_TABLE_SIZE_14BIT; i++) {
        if (table[i] > WAVE_14BIT_MAX) {
            return false;
        }
    }

    // 对于正弦波，检查对称性
    if (wave_type == WAVE_TYPE_SINE) {
        for (uint16_t i = 0; i < WAVE_TABLE_SIZE_14BIT / 2; i++) {
            uint16_t opposite_index = (i + WAVE_TABLE_SIZE_14BIT / 2) % WAVE_TABLE_SIZE_14BIT;
            uint16_t val1 = table[i];
            uint16_t val2 = table[opposite_index];
            
            // 检查对称性 (允许小误差)
            int32_t diff = (int32_t)val1 + (int32_t)val2 - 2 * WAVE_14BIT_CENTER;
            if (abs(diff) > 10) {  // 允许10个LSB的误差
                return false;
            }
        }
    }

    return true;
}

/**
  * @brief  获取波形表统计信息
  * @param  wave_type: 波形类型
  * @param  min_val: 输出最小值指针
  * @param  max_val: 输出最大值指针
  * @param  avg_val: 输出平均值指针
  * @retval bool true: 成功, false: 失败
  */
bool WaveTable_GetStats(Wave_Type_14bit_t wave_type, uint16_t* min_val, 
                        uint16_t* max_val, uint16_t* avg_val)
{
    const uint16_t* table = WaveTable_GetTable(wave_type);
    if (table == NULL || min_val == NULL || max_val == NULL || avg_val == NULL) {
        return false;
    }

    uint16_t min = 65535;
    uint16_t max = 0;
    uint32_t sum = 0;

    for (uint16_t i = 0; i < WAVE_TABLE_SIZE_14BIT; i++) {
        uint16_t val = table[i];
        if (val < min) min = val;
        if (val > max) max = val;
        sum += val;
    }

    *min_val = min;
    *max_val = max;
    *avg_val = (uint16_t)(sum / WAVE_TABLE_SIZE_14BIT);

    return true;
}

/**
  * @brief  计算波形表的THD (总谐波失真)
  * @param  wave_type: 波形类型
  * @retval float THD值 (百分比)
  * @note   这是一个简化的THD计算，实际应用中需要FFT分析
  */
float WaveTable_CalculateTHD(Wave_Type_14bit_t wave_type)
{
    // 简化实现：对于正弦波返回理论值，其他波形返回估算值
    switch (wave_type) {
        case WAVE_TYPE_SINE:
            return 0.1f;  // 理想正弦波的THD很低
        case WAVE_TYPE_SQUARE:
            return 48.3f; // 方波的理论THD
        case WAVE_TYPE_TRIANGLE:
            return 12.1f; // 三角波的理论THD
        case WAVE_TYPE_SAWTOOTH:
            return 25.0f; // 锯齿波的理论THD
        default:
            return 100.0f;
    }
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  初始化所有波形表
  * @param  None
  * @retval None
  */
static void WaveTable_InitializeTables(void)
{
    if (g_tables_initialized) {
        return;
    }

    GenerateSineTable();
    GenerateSquareTable();
    GenerateTriangleTable();
    GenerateSawtoothTable();

    g_tables_initialized = true;
}

/**
  * @brief  生成正弦波表
  * @param  None
  * @retval None
  */
static void GenerateSineTable(void)
{
    for (uint16_t i = 0; i < WAVE_TABLE_SIZE_14BIT; i++) {
        float angle = 2.0f * M_PI * i / WAVE_TABLE_SIZE_14BIT;
        float sine_val = sinf(angle);
        g_sine_table_14bit_ram[i] = (uint16_t)(WAVE_14BIT_CENTER + WAVE_14BIT_AMPLITUDE * sine_val);
    }
}

/**
  * @brief  生成方波表
  * @param  None
  * @retval None
  */
static void GenerateSquareTable(void)
{
    for (uint16_t i = 0; i < WAVE_TABLE_SIZE_14BIT; i++) {
        if (i < WAVE_TABLE_SIZE_14BIT / 2) {
            g_square_table_14bit_ram[i] = WAVE_14BIT_MAX;  // 高电平
        } else {
            g_square_table_14bit_ram[i] = WAVE_14BIT_MIN;  // 低电平
        }
    }
}

/**
  * @brief  生成三角波表
  * @param  None
  * @retval None
  */
static void GenerateTriangleTable(void)
{
    for (uint16_t i = 0; i < WAVE_TABLE_SIZE_14BIT; i++) {
        if (i < WAVE_TABLE_SIZE_14BIT / 2) {
            // 上升沿：从0到最大值
            g_triangle_table_14bit_ram[i] = (uint16_t)(i * WAVE_14BIT_MAX / (WAVE_TABLE_SIZE_14BIT / 2));
        } else {
            // 下降沿：从最大值到0
            uint16_t idx = i - WAVE_TABLE_SIZE_14BIT / 2;
            g_triangle_table_14bit_ram[i] = (uint16_t)(WAVE_14BIT_MAX - idx * WAVE_14BIT_MAX / (WAVE_TABLE_SIZE_14BIT / 2));
        }
    }
}

/**
  * @brief  生成锯齿波表
  * @param  None
  * @retval None
  */
static void GenerateSawtoothTable(void)
{
    for (uint16_t i = 0; i < WAVE_TABLE_SIZE_14BIT; i++) {
        // 线性上升：从0到最大值
        g_sawtooth_table_14bit_ram[i] = (uint16_t)(i * WAVE_14BIT_MAX / WAVE_TABLE_SIZE_14BIT);
    }
}

/************************ (C) COPYRIGHT DAC904移植项目组 *****END OF FILE****/
