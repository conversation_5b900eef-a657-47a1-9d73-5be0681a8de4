/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024-08-02
  * @brief   STM32F4控制DAC904产生高精度信号
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
#include "bsp.h"

// DAC904内联实现 (避免复杂的头文件依赖)
// #include "../Modules/Generation/dac904_simple.h"

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// DAC904简化测试变量
static float current_voltage = 2.5f;           // 当前电压 (默认2.5V)

// DAC904技术优势：
// - 14位分辨率，165MHz最大采样率
// - 并行数据接口，超高速数据传输
// - 0-5V输出范围，适合各种应用
// - 简单易用的接口设计

/* DAC904 内联定义 -----------------------------------------------------------*/
#define DAC904_DATA_MAX             16383       ///< 14位最大值
#define DAC904_DATA_MID             8192        ///< 14位中间值
#define DAC904_DATA_MIN             0           ///< 14位最小值

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void DAC904_Inline_Init(void);
void DAC904_Inline_WriteData(uint16_t data);
void DAC904_Inline_SetVoltage(float voltage);
void DAC904_Simple_Demo(void);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();

    /* ==================== DAC904简化系统初始化 ==================== */
    DAC904_Inline_Init();

    // LED快闪表示DAC904初始化完成
    for (int i = 0; i < 5; i++) {
        GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
        Delay_ms(200);  // 快闪表示就绪状态
    }

    /* ==================== DAC904功能演示 ==================== */
    DAC904_Simple_Demo();

    /* 主循环 - DAC904持续运行 */
    uint32_t demo_counter = 0;
    uint32_t led_counter = 0;

    while (1)
    {
        demo_counter++;
        led_counter++;

        // ==================== 系统状态指示 ====================
        // LED慢闪表示DAC904正常运行
        if (led_counter % 2000000 == 0) {  // 慢闪
            GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
        }

        // ==================== 定期演示不同电压 ====================
        // 每10秒切换一次电压
        if (demo_counter % 10000000 == 0) {
            static uint8_t voltage_index = 0;
            float test_voltages[] = {0.0f, 1.0f, 2.0f, 3.0f, 4.0f, 5.0f};

            DAC904_Inline_SetVoltage(test_voltages[voltage_index]);
            current_voltage = test_voltages[voltage_index];

            voltage_index = (voltage_index + 1) % 6;
        }

        // ==================== 低功耗模式 ====================
        // 短暂延时，让DAC904继续工作
        for (volatile int i = 0; i < 1000; i++) {
            __NOP();
        }
    }
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  DAC904内联初始化
  * @param  None
  * @retval None
  */
void DAC904_Inline_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    // 使能GPIOE时钟 (在BSP_Init中已使能)
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);

    // 配置数据引脚 (PE0-PE13)
    GPIO_InitStructure.GPIO_Pin = DAC904_DATA_PINS_MASK;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(DAC904_DATA_PORT, &GPIO_InitStructure);

    // 配置时钟引脚 (PE14)
    GPIO_InitStructure.GPIO_Pin = DAC904_CLK_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(DAC904_CLOCK_PORT, &GPIO_InitStructure);

    // 初始化引脚状态
    GPIO_ResetBits(DAC904_DATA_PORT, DAC904_DATA_PINS_MASK);  // 数据引脚清零
    GPIO_ResetBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN);        // 时钟引脚清零

    // 设置初始输出为中间值
    DAC904_Inline_WriteData(DAC904_DATA_MID);
}

/**
  * @brief  DAC904内联写入数据
  * @param  data: 14位数据值 (0-16383)
  * @retval None
  */
void DAC904_Inline_WriteData(uint16_t data)
{
    // 限制数据范围
    if (data > DAC904_DATA_MAX) {
        data = DAC904_DATA_MAX;
    }

    // 写入并行数据
    uint32_t temp_odr = DAC904_DATA_PORT->ODR;
    temp_odr &= ~DAC904_DATA_PINS_MASK;
    temp_odr |= (data & DAC904_DATA_MAX);
    DAC904_DATA_PORT->ODR = temp_odr;

    // 产生时钟脉冲
    GPIO_SetBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN);
    // 短暂延时确保建立时间
    __NOP(); __NOP(); __NOP(); __NOP();
    GPIO_ResetBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN);
}

/**
  * @brief  DAC904内联设置电压
  * @param  voltage: 电压值 (0.0-5.0V)
  * @retval None
  */
void DAC904_Inline_SetVoltage(float voltage)
{
    // 限制电压范围
    if (voltage < 0.0f) voltage = 0.0f;
    if (voltage > 5.0f) voltage = 5.0f;

    // 转换为DAC数据
    uint16_t dac_data = (uint16_t)((voltage * DAC904_DATA_MAX) / 5.0f);

    // 写入DAC
    DAC904_Inline_WriteData(dac_data);
}

/**
  * @brief  DAC904简化功能演示
  * @param  None
  * @retval None
  */
void DAC904_Simple_Demo(void)
{
    // 延时让系统稳定
    Delay_ms(1000);

    // 演示电压调整：0V -> 1V -> 2V -> 3V -> 4V -> 5V -> 2.5V
    DAC904_Inline_SetVoltage(0.0f);   // 0V
    Delay_ms(1000);

    DAC904_Inline_SetVoltage(1.0f);   // 1V
    Delay_ms(1000);

    DAC904_Inline_SetVoltage(2.0f);   // 2V
    Delay_ms(1000);

    DAC904_Inline_SetVoltage(3.0f);   // 3V
    Delay_ms(1000);

    DAC904_Inline_SetVoltage(4.0f);   // 4V
    Delay_ms(1000);

    DAC904_Inline_SetVoltage(5.0f);   // 5V
    Delay_ms(1000);

    DAC904_Inline_SetVoltage(2.5f);   // 回到2.5V
    current_voltage = 2.5f;
    Delay_ms(1000);
}










/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


