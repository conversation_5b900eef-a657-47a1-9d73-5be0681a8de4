/**
  ******************************************************************************
  * @file    dds_dac904.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   基于DAC904的高性能DDS信号发生器头文件
  ******************************************************************************
  * @attention
  *
  * DDS特性：
  * - 32位相位累加器
  * - 14位输出分辨率
  * - 支持22.3kHz - 1MHz采样率
  * - 四种波形：正弦波、方波、三角波、锯齿波
  * - 频率范围：1Hz - 100kHz (受硬件限制)
  * - 幅度控制：0-5V可调
  *
  ******************************************************************************
  */

#ifndef __DDS_DAC904_H
#define __DDS_DAC904_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "dac904.h"
#include "wave_tables_14bit.h"

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  DDS DAC904状态枚举
  */
typedef enum {
    DDS_DAC904_OK = 0,              ///< 操作成功
    DDS_DAC904_ERROR,               ///< 一般错误
    DDS_DAC904_PARAM_ERROR,         ///< 参数错误
    DDS_DAC904_NOT_INITIALIZED,     ///< 未初始化
    DDS_DAC904_BUSY                 ///< 设备忙
} DDS_DAC904_Status_t;

/**
  * @brief  DDS DAC904配置结构体
  */
typedef struct {
    uint32_t frequency;             ///< 输出频率 (Hz)
    float amplitude;                ///< 输出幅度 (0.0-5.0V)
    float offset;                   ///< 直流偏移 (0.0-5.0V)
    Wave_Type_14bit_t wave_type;    ///< 波形类型
    uint32_t sample_rate;           ///< 采样率 (Hz)
    bool enable_continuous;         ///< 连续输出使能
    bool enable_phase_sync;         ///< 相位同步使能
} DDS_DAC904_Config_t;

/**
  * @brief  DDS DAC904句柄结构体
  */
typedef struct {
    DDS_DAC904_Config_t config;     ///< 配置参数
    
    // DDS核心参数
    uint32_t phase_accumulator;     ///< 32位相位累加器
    uint32_t frequency_word;        ///< 32位频率控制字
    uint32_t phase_increment;       ///< 相位增量
    
    // 幅度和偏移控制
    uint16_t amplitude_scale;       ///< 幅度缩放因子 (0-16383)
    uint16_t dc_offset;             ///< 直流偏移值 (0-16383)
    
    // 状态信息
    bool is_initialized;            ///< 初始化状态
    bool is_running;                ///< 运行状态
    uint32_t sample_count;          ///< 采样计数
    uint32_t error_count;           ///< 错误计数
    
    // 波形表指针
    const uint16_t* current_table;  ///< 当前波形表指针
} DDS_DAC904_Handle_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup DDS_DAC904_Constants DDS常量定义
  * @{
  */
#define DDS_DAC904_PHASE_BITS       32          ///< 相位累加器位数
#define DDS_DAC904_FREQ_MIN         1           ///< 最小频率 (Hz)
#define DDS_DAC904_FREQ_MAX         100000      ///< 最大频率 (Hz)
#define DDS_DAC904_AMPLITUDE_MIN    0.0f        ///< 最小幅度 (V)
#define DDS_DAC904_AMPLITUDE_MAX    5.0f        ///< 最大幅度 (V)

// 预定义采样率
#define DDS_DAC904_SAMPLE_RATE_22K3 22300       ///< 22.3kHz (兼容现有项目)
#define DDS_DAC904_SAMPLE_RATE_100K 100000      ///< 100kHz
#define DDS_DAC904_SAMPLE_RATE_1M   1000000     ///< 1MHz

// 预定义频率
#define DDS_DAC904_FREQ_1HZ         1           ///< 1Hz
#define DDS_DAC904_FREQ_1KHZ        1000        ///< 1kHz
#define DDS_DAC904_FREQ_10KHZ       10000       ///< 10kHz
#define DDS_DAC904_FREQ_100KHZ      100000      ///< 100kHz
/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup DDS_DAC904_Macros DDS操作宏
  * @{
  */

/**
  * @brief  计算32位频率控制字
  * @param  freq: 目标频率 (Hz)
  * @param  sample_rate: 采样率 (Hz)
  * @retval 32位频率控制字
  */
#define DDS_DAC904_CALC_FREQ_WORD(freq, sample_rate) \
    ((uint32_t)(((uint64_t)(freq) << DDS_DAC904_PHASE_BITS) / (sample_rate)))

/**
  * @brief  从32位相位累加器获取波形表索引
  * @param  phase_acc: 32位相位累加器
  * @retval 波形表索引 (0-1023)
  */
#define DDS_DAC904_PHASE_TO_INDEX(phase_acc) \
    PHASE_TO_TABLE_INDEX(phase_acc)

/**
  * @brief  检查频率是否有效
  * @param  freq: 频率值
  * @retval true: 有效, false: 无效
  */
#define DDS_DAC904_IS_VALID_FREQ(freq) \
    ((freq) >= DDS_DAC904_FREQ_MIN && (freq) <= DDS_DAC904_FREQ_MAX)

/**
  * @brief  检查幅度是否有效
  * @param  amp: 幅度值
  * @retval true: 有效, false: 无效
  */
#define DDS_DAC904_IS_VALID_AMPLITUDE(amp) \
    ((amp) >= DDS_DAC904_AMPLITUDE_MIN && (amp) <= DDS_DAC904_AMPLITUDE_MAX)

/**
  * @brief  将电压值转换为14位DAC数据
  * @param  voltage: 电压值 (0.0-5.0V)
  * @retval 14位DAC数据
  */
#define DDS_DAC904_VOLTAGE_TO_DAC(voltage) \
    ((uint16_t)(((voltage) * DAC904_DATA_MAX) / 5.0f))

/**
  * @brief  应用幅度缩放和偏移
  * @param  raw_data: 原始波形数据
  * @param  scale: 幅度缩放因子
  * @param  offset: 直流偏移
  * @retval 缩放后的数据
  */
#define DDS_DAC904_APPLY_SCALE_OFFSET(raw_data, scale, offset) \
    ((uint16_t)((((int32_t)(raw_data) - WAVE_14BIT_CENTER) * (scale)) / WAVE_14BIT_AMPLITUDE + (offset)))

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/
extern DDS_DAC904_Handle_t g_dds_dac904_handle;

/* Exported functions --------------------------------------------------------*/

/** @defgroup DDS_DAC904_Functions DDS DAC904函数
  * @{
  */

/**
  * @brief  DDS DAC904初始化
  * @param  config: 配置参数指针
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_Init(DDS_DAC904_Config_t* config);

/**
  * @brief  DDS DAC904反初始化
  * @param  None
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_DeInit(void);

/**
  * @brief  设置DDS频率
  * @param  frequency: 目标频率 (Hz)
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_SetFrequency(uint32_t frequency);

/**
  * @brief  设置DDS幅度
  * @param  amplitude: 目标幅度 (0.0-5.0V)
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_SetAmplitude(float amplitude);

/**
  * @brief  设置DDS波形类型
  * @param  wave_type: 波形类型
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_SetWaveType(Wave_Type_14bit_t wave_type);

/**
  * @brief  设置DDS直流偏移
  * @param  offset: 直流偏移 (0.0-5.0V)
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_SetOffset(float offset);

/**
  * @brief  启动DDS输出
  * @param  None
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_Start(void);

/**
  * @brief  停止DDS输出
  * @param  None
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_Stop(void);

/**
  * @brief  生成下一个DDS采样点
  * @param  None
  * @retval uint16_t 14位DAC数据
  * @note   此函数在定时器中断中调用
  */
uint16_t DDS_DAC904_GenerateSample(void);

/**
  * @brief  DDS定时器中断处理函数
  * @param  None
  * @retval None
  * @note   此函数需要在定时器中断中调用
  */
void DDS_DAC904_TimerHandler(void);

/**
  * @brief  获取DDS状态信息
  * @param  None
  * @retval DDS_DAC904_Handle_t* 句柄指针
  */
DDS_DAC904_Handle_t* DDS_DAC904_GetHandle(void);

/**
  * @brief  重置DDS相位累加器
  * @param  None
  * @retval DDS_DAC904_Status_t 操作状态
  */
DDS_DAC904_Status_t DDS_DAC904_ResetPhase(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __DDS_DAC904_H */

/************************ (C) COPYRIGHT DAC904移植项目组 *****END OF FILE****/
