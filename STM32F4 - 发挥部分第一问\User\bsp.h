#ifndef __BSP_H
#define __BSP_H

#include "stm32f4xx.h"

// G题项目硬件引脚定义

// 1. DAC8552 (使用 SPI2)
#define DAC8552_SPI_PORT         GPIOB
#define DAC8552_SPI_CLK          RCC_AHB1Periph_GPIOB
#define DAC8552_SCK_PIN          GPIO_Pin_13
#define DAC8552_MOSI_PIN         GPIO_Pin_15
#define DAC8552_SCK_PINSOURCE    GPIO_PinSource13
#define DAC8552_MOSI_PINSOURCE   GPIO_PinSource15
#define DAC8552_SPI_AF           GPIO_AF_SPI2
#define DAC8552_SPI_PERIPH       SPI2

#define DAC8552_SYNC_PORT        GPIOB
#define DAC8552_SYNC_CLK         RCC_AHB1Periph_GPIOB
#define DAC8552_SYNC_PIN         GPIO_Pin_12

// 2. AD7606 (使用 SPI1)
#define AD7606_SPI_PORT          GPIOA
#define AD7606_SPI_CLK           RCC_AHB1Periph_GPIOA
#define AD7606_SCK_PIN           GPIO_Pin_5
#define AD7606_MISO_PIN          GPIO_Pin_6
#define AD7606_SCK_PINSOURCE     GPIO_PinSource5
#define AD7606_MISO_PINSOURCE    GPIO_PinSource6
#define AD7606_SPI_AF            GPIO_AF_SPI1
#define AD7606_SPI_PERIPH        SPI1

#define AD7606_CS_PORT           GPIOA
#define AD7606_CS_CLK            RCC_AHB1Periph_GPIOA
#define AD7606_CS_PIN            GPIO_Pin_15

#define AD7606_CTRL_PORT         GPIOC
#define AD7606_CTRL_CLK          RCC_AHB1Periph_GPIOC
#define AD7606_CONVST_PIN        GPIO_Pin_7
#define AD7606_BUSY_PIN          GPIO_Pin_6
#define AD7606_RESET_PIN         GPIO_Pin_8

// 3. CD4052 增益控制
#define CD4052_CTRL_PORT         GPIOE
#define CD4052_CTRL_CLK          RCC_AHB1Periph_GPIOE
#define CD4052_A_PIN             GPIO_Pin_2
#define CD4052_B_PIN             GPIO_Pin_3

// 4. DAC904 高速DAC (14位并行接口)
#define DAC904_DATA_PORT         GPIOE
#define DAC904_DATA_CLK          RCC_AHB1Periph_GPIOE
#define DAC904_CLOCK_PORT        GPIOE
#define DAC904_CLOCK_CLK         RCC_AHB1Periph_GPIOE

// DAC904 数据引脚 (PE0-PE13)
#define DAC904_D0_PIN            GPIO_Pin_0
#define DAC904_D1_PIN            GPIO_Pin_1
#define DAC904_D2_PIN            GPIO_Pin_2
#define DAC904_D3_PIN            GPIO_Pin_3
#define DAC904_D4_PIN            GPIO_Pin_4
#define DAC904_D5_PIN            GPIO_Pin_5
#define DAC904_D6_PIN            GPIO_Pin_6
#define DAC904_D7_PIN            GPIO_Pin_7
#define DAC904_D8_PIN            GPIO_Pin_8
#define DAC904_D9_PIN            GPIO_Pin_9
#define DAC904_D10_PIN           GPIO_Pin_10
#define DAC904_D11_PIN           GPIO_Pin_11
#define DAC904_D12_PIN           GPIO_Pin_12
#define DAC904_D13_PIN           GPIO_Pin_13

// DAC904 时钟引脚 (PE14)
#define DAC904_CLK_PIN           GPIO_Pin_14

// DAC904 数据引脚组合掩码
#define DAC904_DATA_PINS_MASK    (GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | \
                                  GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7 | \
                                  GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_10 | GPIO_Pin_11 | \
                                  GPIO_Pin_12 | GPIO_Pin_13)

// DAC904 常量定义
#define DAC904_DATA_BITS         14
#define DAC904_DATA_MAX          ((1 << DAC904_DATA_BITS) - 1)  // 16383
#define DAC904_DATA_MID          (1 << (DAC904_DATA_BITS - 1))  // 8192

// 函数声明
void BSP_Init(void);

#endif
