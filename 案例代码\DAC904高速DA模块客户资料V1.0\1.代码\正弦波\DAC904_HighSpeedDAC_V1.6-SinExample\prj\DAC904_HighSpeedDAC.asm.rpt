Assembler report for DAC904_HighSpeedDAC
Tue Jul 25 11:30:38 2023
Quartus II 64-Bit Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Assembler Summary
  3. Assembler Settings
  4. Assembler Generated Files
  5. Assembler Device Options: D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/DAC904_HighSpeedDAC.sof
  6. Assembler Device Options: D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/DAC904_HighSpeedDAC.pof
  7. Assembler Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2011 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+---------------------------------------------------------------+
; Assembler Summary                                             ;
+-----------------------+---------------------------------------+
; Assembler Status      ; Successful - Tue Jul 25 11:30:38 2023 ;
; Revision Name         ; DAC904_HighSpeedDAC                   ;
; Top-level Entity Name ; DAC904_WriteTEST                      ;
; Family                ; Cyclone IV E                          ;
; Device                ; EP4CE15F17C8                          ;
+-----------------------+---------------------------------------+


+--------------------------------------------------------------------------------------------------------+
; Assembler Settings                                                                                     ;
+-----------------------------------------------------------------------------+----------+---------------+
; Option                                                                      ; Setting  ; Default Value ;
+-----------------------------------------------------------------------------+----------+---------------+
; Use configuration device                                                    ; On       ; Off           ;
; Configuration device                                                        ; Epcs64   ; Auto          ;
; Use smart compilation                                                       ; Off      ; Off           ;
; Enable parallel Assembler and TimeQuest Timing Analyzer during compilation  ; On       ; On            ;
; Enable compact report table                                                 ; Off      ; Off           ;
; Generate compressed bitstreams                                              ; On       ; On            ;
; Compression mode                                                            ; Off      ; Off           ;
; Clock source for configuration device                                       ; Internal ; Internal      ;
; Clock frequency of the configuration device                                 ; 10 MHZ   ; 10 MHz        ;
; Divide clock frequency by                                                   ; 1        ; 1             ;
; Auto user code                                                              ; Off      ; Off           ;
; Configuration device auto user code                                         ; Off      ; Off           ;
; Generate Tabular Text File (.ttf) For Target Device                         ; Off      ; Off           ;
; Generate Raw Binary File (.rbf) For Target Device                           ; Off      ; Off           ;
; Generate Hexadecimal (Intel-Format) Output File (.hexout) for Target Device ; Off      ; Off           ;
; Hexadecimal Output File start address                                       ; 0        ; 0             ;
; Hexadecimal Output File count direction                                     ; Up       ; Up            ;
; Release clears before tri-states                                            ; Off      ; Off           ;
; Auto-restart configuration after error                                      ; On       ; On            ;
; Enable OCT_DONE                                                             ; Off      ; Off           ;
; Generate Serial Vector Format File (.svf) for Target Device                 ; Off      ; Off           ;
; Generate a JEDEC STAPL Format File (.jam) for Target Device                 ; Off      ; Off           ;
; Generate a compressed Jam STAPL Byte Code 2.0 File (.jbc) for Target Device ; Off      ; Off           ;
; Generate a compressed Jam STAPL Byte Code 2.0 File (.jbc) for Target Device ; On       ; On            ;
+-----------------------------------------------------------------------------+----------+---------------+


+--------------------------------------------------------------------------------------------------------------+
; Assembler Generated Files                                                                                    ;
+--------------------------------------------------------------------------------------------------------------+
; File Name                                                                                                    ;
+--------------------------------------------------------------------------------------------------------------+
; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/DAC904_HighSpeedDAC.sof ;
; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/DAC904_HighSpeedDAC.pof ;
+--------------------------------------------------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------+
; Assembler Device Options: D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/DAC904_HighSpeedDAC.sof ;
+----------------+-----------------------------------------------------------------------------------------------------------------------+
; Option         ; Setting                                                                                                               ;
+----------------+-----------------------------------------------------------------------------------------------------------------------+
; Device         ; EP4CE15F17C8                                                                                                          ;
; JTAG usercode  ; 0xFFFFFFFF                                                                                                            ;
; Checksum       ; 0x00473D55                                                                                                            ;
+----------------+-----------------------------------------------------------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------+
; Assembler Device Options: D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/DAC904_HighSpeedDAC.pof ;
+--------------------+-------------------------------------------------------------------------------------------------------------------+
; Option             ; Setting                                                                                                           ;
+--------------------+-------------------------------------------------------------------------------------------------------------------+
; Device             ; EPCS64                                                                                                            ;
; JTAG usercode      ; 0x00000000                                                                                                        ;
; Checksum           ; 0x7D02930B                                                                                                        ;
; Compression Ratio  ; 2                                                                                                                 ;
+--------------------+-------------------------------------------------------------------------------------------------------------------+


+--------------------+
; Assembler Messages ;
+--------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit Assembler
    Info: Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version
    Info: Processing started: Tue Jul 25 11:30:37 2023
Info: Command: quartus_asm --read_settings_files=off --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC
Info: Writing out detailed assembly data for power analysis
Info: Assembler is generating device programming files
Info: Quartus II 64-Bit Assembler was successful. 0 errors, 0 warnings
    Info: Peak virtual memory: 4472 megabytes
    Info: Processing ended: Tue Jul 25 11:30:38 2023
    Info: Elapsed time: 00:00:01
    Info: Total CPU time (on all processors): 00:00:01


