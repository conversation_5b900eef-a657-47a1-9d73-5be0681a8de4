/**
  ******************************************************************************
  * @file    wave_tables_14bit.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   14位高精度波形查找表
  ******************************************************************************
  * @attention
  *
  * 波形表特性：
  * - 14位分辨率 (0-16383)
  * - 1024点采样 (平衡精度和内存使用)
  * - 中心值：8192
  * - 幅度：±8191
  * - 支持正弦波、方波、三角波、锯齿波
  *
  ******************************************************************************
  */

#ifndef __WAVE_TABLES_14BIT_H
#define __WAVE_TABLES_14BIT_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"

/* Exported constants --------------------------------------------------------*/

/** @defgroup Wave_Table_Constants 波形表常量
  * @{
  */
#define WAVE_TABLE_SIZE_14BIT       1024        ///< 波形表大小 (1024点)
#define WAVE_TABLE_BITS_14BIT       10          ///< 波形表索引位数 (2^10 = 1024)
#define WAVE_TABLE_MASK_14BIT       (WAVE_TABLE_SIZE_14BIT - 1)  ///< 波形表索引掩码

#define WAVE_14BIT_CENTER           8192        ///< 14位中心值
#define WAVE_14BIT_AMPLITUDE        8191        ///< 14位最大幅度
#define WAVE_14BIT_MAX              16383       ///< 14位最大值
#define WAVE_14BIT_MIN              0           ///< 14位最小值
/**
  * @}
  */

/** @defgroup Wave_Types 波形类型枚举
  * @{
  */
typedef enum {
    WAVE_TYPE_SINE = 0,             ///< 正弦波
    WAVE_TYPE_SQUARE,               ///< 方波
    WAVE_TYPE_TRIANGLE,             ///< 三角波
    WAVE_TYPE_SAWTOOTH,             ///< 锯齿波
    WAVE_TYPE_COUNT                 ///< 波形类型总数
} Wave_Type_14bit_t;
/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup Wave_Tables 波形查找表
  * @{
  */
extern const uint16_t g_sine_table_14bit[WAVE_TABLE_SIZE_14BIT];        ///< 14位正弦波表
extern const uint16_t g_square_table_14bit[WAVE_TABLE_SIZE_14BIT];      ///< 14位方波表
extern const uint16_t g_triangle_table_14bit[WAVE_TABLE_SIZE_14BIT];    ///< 14位三角波表
extern const uint16_t g_sawtooth_table_14bit[WAVE_TABLE_SIZE_14BIT];    ///< 14位锯齿波表
/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup Wave_Table_Functions 波形表函数
  * @{
  */

/**
  * @brief  获取指定波形类型的查找表指针
  * @param  wave_type: 波形类型
  * @retval const uint16_t* 波形表指针，失败返回NULL
  */
const uint16_t* WaveTable_GetTable(Wave_Type_14bit_t wave_type);

/**
  * @brief  从波形表获取指定索引的数值
  * @param  wave_type: 波形类型
  * @param  index: 表索引 (0-1023)
  * @retval uint16_t 波形数值，失败返回中心值
  */
uint16_t WaveTable_GetValue(Wave_Type_14bit_t wave_type, uint16_t index);

/**
  * @brief  验证波形表完整性
  * @param  wave_type: 波形类型
  * @retval bool true: 验证通过, false: 验证失败
  */
bool WaveTable_Verify(Wave_Type_14bit_t wave_type);

/**
  * @brief  计算波形表的THD (总谐波失真)
  * @param  wave_type: 波形类型
  * @retval float THD值 (百分比)
  */
float WaveTable_CalculateTHD(Wave_Type_14bit_t wave_type);

/**
  * @brief  获取波形表统计信息
  * @param  wave_type: 波形类型
  * @param  min_val: 输出最小值指针
  * @param  max_val: 输出最大值指针
  * @param  avg_val: 输出平均值指针
  * @retval bool true: 成功, false: 失败
  */
bool WaveTable_GetStats(Wave_Type_14bit_t wave_type, uint16_t* min_val, 
                        uint16_t* max_val, uint16_t* avg_val);

/**
  * @}
  */

/* Exported macros -----------------------------------------------------------*/

/** @defgroup Wave_Table_Macros 波形表操作宏
  * @{
  */

/**
  * @brief  快速获取正弦波数值
  * @param  index: 表索引
  * @retval 正弦波数值
  */
#define SINE_TABLE_GET(index)       g_sine_table_14bit[(index) & WAVE_TABLE_MASK_14BIT]

/**
  * @brief  快速获取方波数值
  * @param  index: 表索引
  * @retval 方波数值
  */
#define SQUARE_TABLE_GET(index)     g_square_table_14bit[(index) & WAVE_TABLE_MASK_14BIT]

/**
  * @brief  快速获取三角波数值
  * @param  index: 表索引
  * @retval 三角波数值
  */
#define TRIANGLE_TABLE_GET(index)   g_triangle_table_14bit[(index) & WAVE_TABLE_MASK_14BIT]

/**
  * @brief  快速获取锯齿波数值
  * @param  index: 表索引
  * @retval 锯齿波数值
  */
#define SAWTOOTH_TABLE_GET(index)   g_sawtooth_table_14bit[(index) & WAVE_TABLE_MASK_14BIT]

/**
  * @brief  将相位累加器转换为表索引
  * @param  phase_acc: 32位相位累加器
  * @retval 表索引 (0-1023)
  */
#define PHASE_TO_TABLE_INDEX(phase_acc) \
    (((phase_acc) >> (32 - WAVE_TABLE_BITS_14BIT)) & WAVE_TABLE_MASK_14BIT)

/**
  * @brief  检查表索引是否有效
  * @param  index: 表索引
  * @retval true: 有效, false: 无效
  */
#define IS_VALID_TABLE_INDEX(index) ((index) < WAVE_TABLE_SIZE_14BIT)

/**
  * @brief  检查波形类型是否有效
  * @param  type: 波形类型
  * @retval true: 有效, false: 无效
  */
#define IS_VALID_WAVE_TYPE(type)    ((type) < WAVE_TYPE_COUNT)

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __WAVE_TABLES_14BIT_H */

/************************ (C) COPYRIGHT DAC904移植项目组 *****END OF FILE****/
