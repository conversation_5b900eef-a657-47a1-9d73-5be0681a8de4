{ "Info" "IQEXE_SEPARATOR" "" "Info: *******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus II 64-Bit " "Info: Running Quartus II 64-Bit Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version " "Info: Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_START_BANNER_TIME" "Tue Jul 25 11:26:08 2023 " "Info: Processing started: Tue Jul 25 11:26:08 2023" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "" 0 -1}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC " "Info: Command: quartus_map --read_settings_files=on --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC" {  } {  } 0 0 "Command: %1!s!" 0 0 "" 0 -1}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "6 6 " "Info: Parallel compilation is enabled and will use 6 of the 6 processors detected" {  } {  } 0 0 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "" 0 -1}
{ "Warning" "WVRFX_VERI_IGNORED_ANONYMOUS_PORT" "PhaseAccumulator PhaseAccumulator.V(17) " "Warning (10238): Verilog Module Declaration warning at PhaseAccumulator.V(17): ignored anonymous port(s) indicated by duplicate or dangling comma(s) in the port list for module \"PhaseAccumulator\"" {  } { { "../rtl/PhaseAccumulator.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PhaseAccumulator.V" 17 0 0 } }  } 0 10238 "Verilog Module Declaration warning at %2!s!: ignored anonymous port(s) indicated by duplicate or dangling comma(s) in the port list for module \"%1!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/phaseaccumulator.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/phaseaccumulator.v" { { "Info" "ISGN_ENTITY_NAME" "1 PhaseAccumulator " "Info: Found entity 1: PhaseAccumulator" {  } { { "../rtl/PhaseAccumulator.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PhaseAccumulator.V" 11 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Warning" "WVRFX_L2_VERI_INGORE_DANGLING_COMMA" "DAC904_WriteTEST.V(59) " "Warning (10275): Verilog HDL Module Instantiation warning at DAC904_WriteTEST.V(59): ignored dangling comma in List of Port Connections" {  } { { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 59 0 0 } }  } 0 10275 "Verilog HDL Module Instantiation warning at %1!s!: ignored dangling comma in List of Port Connections" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/dac904_writetest.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/dac904_writetest.v" { { "Info" "ISGN_ENTITY_NAME" "1 DAC904_WriteTEST " "Info: Found entity 1: DAC904_WriteTEST" {  } { { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 1 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/dac904_writemodule.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/dac904_writemodule.v" { { "Info" "ISGN_ENTITY_NAME" "1 DAC904_WriteModule " "Info: Found entity 1: DAC904_WriteModule" {  } { { "../rtl/DAC904_WriteModule.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteModule.V" 1 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/rom/rom_14b_sin/rom_14b_sin.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/rom/rom_14b_sin/rom_14b_sin.v" { { "Info" "ISGN_ENTITY_NAME" "1 rom_14B_Sin " "Info: Found entity 1: rom_14B_Sin" {  } { { "../rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" 39 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/pll/pll165m/pll165m.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/pll/pll165m/pll165m.v" { { "Info" "ISGN_ENTITY_NAME" "1 pll165m " "Info: Found entity 1: pll165m" {  } { { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 39 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Warning" "WVRFX_L2_VERI_CREATED_IMPLICIT_NET" "pll_65M DAC904_WriteTEST.V(18) " "Warning (10236): Verilog HDL Implicit Net warning at DAC904_WriteTEST.V(18): created implicit net for \"pll_65M\"" {  } { { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 18 0 0 } }  } 0 10236 "Verilog HDL Implicit Net warning at %2!s!: created implicit net for \"%1!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_TOP" "DAC904_WriteTEST " "Info: Elaborating entity \"DAC904_WriteTEST\" for the top level hierarchy" {  } {  } 0 0 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "pll165m pll165m:U0_pll165m_inst " "Info: Elaborating entity \"pll165m\" for hierarchy \"pll165m:U0_pll165m_inst\"" {  } { { "../rtl/DAC904_WriteTEST.V" "U0_pll165m_inst" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 21 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altpll pll165m:U0_pll165m_inst\|altpll:altpll_component " "Info: Elaborating entity \"altpll\" for hierarchy \"pll165m:U0_pll165m_inst\|altpll:altpll_component\"" {  } { { "../rtl/PLL/pll165m/pll165m.v" "altpll_component" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_ELABORATION_HEADER" "pll165m:U0_pll165m_inst\|altpll:altpll_component " "Info: Elaborated megafunction instantiation \"pll165m:U0_pll165m_inst\|altpll:altpll_component\"" {  } { { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } }  } 0 0 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "pll165m:U0_pll165m_inst\|altpll:altpll_component " "Info: Instantiated megafunction \"pll165m:U0_pll165m_inst\|altpll:altpll_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "bandwidth_type AUTO " "Info: Parameter \"bandwidth_type\" = \"AUTO\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_divide_by 25 " "Info: Parameter \"clk0_divide_by\" = \"25\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_duty_cycle 50 " "Info: Parameter \"clk0_duty_cycle\" = \"50\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_multiply_by 33 " "Info: Parameter \"clk0_multiply_by\" = \"33\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_phase_shift 0 " "Info: Parameter \"clk0_phase_shift\" = \"0\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_divide_by 10 " "Info: Parameter \"clk1_divide_by\" = \"10\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_duty_cycle 50 " "Info: Parameter \"clk1_duty_cycle\" = \"50\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_multiply_by 33 " "Info: Parameter \"clk1_multiply_by\" = \"33\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_phase_shift 0 " "Info: Parameter \"clk1_phase_shift\" = \"0\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "compensate_clock CLK0 " "Info: Parameter \"compensate_clock\" = \"CLK0\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "inclk0_input_frequency 20000 " "Info: Parameter \"inclk0_input_frequency\" = \"20000\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Info: Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint CBX_MODULE_PREFIX=pll165m " "Info: Parameter \"lpm_hint\" = \"CBX_MODULE_PREFIX=pll165m\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altpll " "Info: Parameter \"lpm_type\" = \"altpll\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode NORMAL " "Info: Parameter \"operation_mode\" = \"NORMAL\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "pll_type AUTO " "Info: Parameter \"pll_type\" = \"AUTO\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_activeclock PORT_UNUSED " "Info: Parameter \"port_activeclock\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_areset PORT_USED " "Info: Parameter \"port_areset\" = \"PORT_USED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad0 PORT_UNUSED " "Info: Parameter \"port_clkbad0\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad1 PORT_UNUSED " "Info: Parameter \"port_clkbad1\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkloss PORT_UNUSED " "Info: Parameter \"port_clkloss\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkswitch PORT_UNUSED " "Info: Parameter \"port_clkswitch\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_configupdate PORT_UNUSED " "Info: Parameter \"port_configupdate\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_fbin PORT_UNUSED " "Info: Parameter \"port_fbin\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk0 PORT_USED " "Info: Parameter \"port_inclk0\" = \"PORT_USED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk1 PORT_UNUSED " "Info: Parameter \"port_inclk1\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_locked PORT_USED " "Info: Parameter \"port_locked\" = \"PORT_USED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pfdena PORT_UNUSED " "Info: Parameter \"port_pfdena\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasecounterselect PORT_UNUSED " "Info: Parameter \"port_phasecounterselect\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasedone PORT_UNUSED " "Info: Parameter \"port_phasedone\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasestep PORT_UNUSED " "Info: Parameter \"port_phasestep\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phaseupdown PORT_UNUSED " "Info: Parameter \"port_phaseupdown\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pllena PORT_UNUSED " "Info: Parameter \"port_pllena\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanaclr PORT_UNUSED " "Info: Parameter \"port_scanaclr\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclk PORT_UNUSED " "Info: Parameter \"port_scanclk\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclkena PORT_UNUSED " "Info: Parameter \"port_scanclkena\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandata PORT_UNUSED " "Info: Parameter \"port_scandata\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandataout PORT_UNUSED " "Info: Parameter \"port_scandataout\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandone PORT_UNUSED " "Info: Parameter \"port_scandone\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanread PORT_UNUSED " "Info: Parameter \"port_scanread\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanwrite PORT_UNUSED " "Info: Parameter \"port_scanwrite\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk0 PORT_USED " "Info: Parameter \"port_clk0\" = \"PORT_USED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk1 PORT_USED " "Info: Parameter \"port_clk1\" = \"PORT_USED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk2 PORT_UNUSED " "Info: Parameter \"port_clk2\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk3 PORT_UNUSED " "Info: Parameter \"port_clk3\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk4 PORT_UNUSED " "Info: Parameter \"port_clk4\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk5 PORT_UNUSED " "Info: Parameter \"port_clk5\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena0 PORT_UNUSED " "Info: Parameter \"port_clkena0\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena1 PORT_UNUSED " "Info: Parameter \"port_clkena1\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena2 PORT_UNUSED " "Info: Parameter \"port_clkena2\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena3 PORT_UNUSED " "Info: Parameter \"port_clkena3\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena4 PORT_UNUSED " "Info: Parameter \"port_clkena4\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena5 PORT_UNUSED " "Info: Parameter \"port_clkena5\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk0 PORT_UNUSED " "Info: Parameter \"port_extclk0\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk1 PORT_UNUSED " "Info: Parameter \"port_extclk1\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk2 PORT_UNUSED " "Info: Parameter \"port_extclk2\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk3 PORT_UNUSED " "Info: Parameter \"port_extclk3\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "self_reset_on_loss_lock OFF " "Info: Parameter \"self_reset_on_loss_lock\" = \"OFF\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_clock 5 " "Info: Parameter \"width_clock\" = \"5\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1}  } { { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } }  } 0 0 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/pll165m_altpll.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file db/pll165m_altpll.v" { { "Info" "ISGN_ENTITY_NAME" "1 pll165m_altpll " "Info: Found entity 1: pll165m_altpll" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 30 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "pll165m_altpll pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated " "Info: Elaborating entity \"pll165m_altpll\" for hierarchy \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\"" {  } { { "altpll.tdf" "auto_generated" { Text "c:/altera/11.0/quartus/libraries/megafunctions/altpll.tdf" 897 3 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "PhaseAccumulator PhaseAccumulator:U1_PhaseAccumulator " "Info: Elaborating entity \"PhaseAccumulator\" for hierarchy \"PhaseAccumulator:U1_PhaseAccumulator\"" {  } { { "../rtl/DAC904_WriteTEST.V" "U1_PhaseAccumulator" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 59 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "rom_14B_Sin rom_14B_Sin:U2_rom_14B_Sin " "Info: Elaborating entity \"rom_14B_Sin\" for hierarchy \"rom_14B_Sin:U2_rom_14B_Sin\"" {  } { { "../rtl/DAC904_WriteTEST.V" "U2_rom_14B_Sin" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 74 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component " "Info: Elaborating entity \"altsyncram\" for hierarchy \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\"" {  } { { "../rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" "altsyncram_component" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" 81 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_ELABORATION_HEADER" "rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component " "Info: Elaborated megafunction instantiation \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\"" {  } { { "../rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" 81 0 0 } }  } 0 0 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component " "Info: Instantiated megafunction \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Info: Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Info: Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Info: Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file ../../ROM/ROM_14B_SIN/ROM_14B_SIN.mif " "Info: Parameter \"init_file\" = \"../../ROM/ROM_14B_SIN/ROM_14B_SIN.mif\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Info: Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Info: Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Info: Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 16384 " "Info: Parameter \"numwords_a\" = \"16384\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Info: Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Info: Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a CLOCK0 " "Info: Parameter \"outdata_reg_a\" = \"CLOCK0\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 14 " "Info: Parameter \"widthad_a\" = \"14\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Info: Parameter \"width_a\" = \"14\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Info: Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1}  } { { "../rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" 81 0 0 } }  } 0 0 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_vhb1.tdf 1 1 " "Info: Found 1 design units, including 1 entities, in source file db/altsyncram_vhb1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_vhb1 " "Info: Found entity 1: altsyncram_vhb1" {  } { { "db/altsyncram_vhb1.tdf" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/altsyncram_vhb1.tdf" 31 1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_vhb1 rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated " "Info: Elaborating entity \"altsyncram_vhb1\" for hierarchy \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "c:/altera/11.0/quartus/libraries/megafunctions/altsyncram.tdf" 790 4 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/decode_c8a.tdf 1 1 " "Info: Found 1 design units, including 1 entities, in source file db/decode_c8a.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 decode_c8a " "Info: Found entity 1: decode_c8a" {  } { { "db/decode_c8a.tdf" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/decode_c8a.tdf" 22 1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "decode_c8a rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated\|decode_c8a:rden_decode " "Info: Elaborating entity \"decode_c8a\" for hierarchy \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated\|decode_c8a:rden_decode\"" {  } { { "db/altsyncram_vhb1.tdf" "rden_decode" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/altsyncram_vhb1.tdf" 40 2 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/mux_gob.tdf 1 1 " "Info: Found 1 design units, including 1 entities, in source file db/mux_gob.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 mux_gob " "Info: Found entity 1: mux_gob" {  } { { "db/mux_gob.tdf" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/mux_gob.tdf" 22 1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "mux_gob rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated\|mux_gob:mux2 " "Info: Elaborating entity \"mux_gob\" for hierarchy \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated\|mux_gob:mux2\"" {  } { { "db/altsyncram_vhb1.tdf" "mux2" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/altsyncram_vhb1.tdf" 41 2 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "DAC904_WriteModule DAC904_WriteModule:U3_DAC904_WriteModule " "Info: Elaborating entity \"DAC904_WriteModule\" for hierarchy \"DAC904_WriteModule:U3_DAC904_WriteModule\"" {  } { { "../rtl/DAC904_WriteTEST.V" "U3_DAC904_WriteModule" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 93 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Info: Timing-Driven Synthesis is running" {  } {  } 0 0 "Timing-Driven Synthesis is running" 0 0 "" 0 -1}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Info: Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 " "Info: Adding node \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\"" {  } {  } 0 0 "Adding node \"%1!s!\"" 0 0 "" 0 -1}  } {  } 0 0 "Generating hard_block partition \"%1!s!\"" 0 0 "" 0 -1}
{ "Warning" "WCUT_PLL_COMPENSATE_CLOCK_NOT_CONNECTED" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 compensate_clock clock0 CLK\[0\] " "Warning: PLL \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\" has parameter compensate_clock set to clock0 but port CLK\[0\] is not connected" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 50 -1 0 } } { "altpll.tdf" "" { Text "c:/altera/11.0/quartus/libraries/megafunctions/altpll.tdf" 897 0 0 } } { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } } { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 21 0 0 } }  } 0 0 "PLL \"%1!s!\" has parameter %2!s! set to %3!s! but port %4!s! is not connected" 0 0 "" 0 -1}
{ "Warning" "WCUT_PLL_MULT_DIV_SPECIFIED_CLOCK_NOT_CONNECTED" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 CLK\[0\] clk0_multiply_by clk0_divide_by " "Warning: PLL \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\" has parameters clk0_multiply_by and clk0_divide_by specified but port CLK\[0\] is not connected" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 50 -1 0 } } { "altpll.tdf" "" { Text "c:/altera/11.0/quartus/libraries/megafunctions/altpll.tdf" 897 0 0 } } { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } } { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 21 0 0 } }  } 0 0 "PLL \"%1!s!\" has parameters %3!s! and %4!s! specified but port %2!s! is not connected" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_TM_SUMMARY" "92 " "Info: Implemented 92 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "2 " "Info: Implemented 2 input pins" {  } {  } 0 0 "Implemented %1!d! input pins" 0 0 "" 0 -1} { "Info" "ICUT_CUT_TM_OPINS" "15 " "Info: Implemented 15 output pins" {  } {  } 0 0 "Implemented %1!d! output pins" 0 0 "" 0 -1} { "Info" "ICUT_CUT_TM_LCELLS" "46 " "Info: Implemented 46 logic cells" {  } {  } 0 0 "Implemented %1!d! logic cells" 0 0 "" 0 -1} { "Info" "ICUT_CUT_TM_RAMS" "28 " "Info: Implemented 28 RAM segments" {  } {  } 0 0 "Implemented %1!d! RAM segments" 0 0 "" 0 -1} { "Info" "ICUT_CUT_TM_PLLS" "1 " "Info: Implemented 1 PLLs" {  } {  } 0 0 "Implemented %1!d! PLLs" 0 0 "" 0 -1}  } {  } 0 0 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "" 0 -1}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 5 s Quartus II 64-Bit " "Info: Quartus II 64-Bit Analysis & Synthesis was successful. 0 errors, 5 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4498 " "Info: Peak virtual memory: 4498 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "" 0 -1} { "Info" "IQEXE_END_BANNER_TIME" "Tue Jul 25 11:26:10 2023 " "Info: Processing ended: Tue Jul 25 11:26:10 2023" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_TIME" "00:00:02 " "Info: Elapsed time: 00:00:02" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Info: Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_SEPARATOR" "" "Info: *******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Fitter Quartus II 64-Bit " "Info: Running Quartus II 64-Bit Fitter" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version " "Info: Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_START_BANNER_TIME" "Tue Jul 25 11:26:11 2023 " "Info: Processing started: Tue Jul 25 11:26:11 2023" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "" 0 -1}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_fit --read_settings_files=off --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC " "Info: Command: quartus_fit --read_settings_files=off --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC" {  } {  } 0 0 "Command: %1!s!" 0 0 "" 0 -1}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "6 6 " "Info: Parallel compilation is enabled and will use 6 of the 6 processors detected" {  } {  } 0 0 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "" 0 -1}
{ "Info" "IMPP_MPP_USER_DEVICE" "DAC904_HighSpeedDAC EP4CE15F17C8 " "Info: Selected device EP4CE15F17C8 for design \"DAC904_HighSpeedDAC\"" {  } {  } 0 0 "Selected device %2!s! for design \"%1!s!\"" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Info: Low junction temperature is 0 degrees C" {  } {  } 0 0 "%1!s! is %2!s!" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "Info: High junction temperature is 85 degrees C" {  } {  } 0 0 "%1!s! is %2!s!" 0 0 "" 0 -1}
{ "Warning" "WCUT_CUT_YGR_PLL_SET_COMPENSATE_CLK" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 clock1 " "Warning: Compensate clock of PLL \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\" has been set to clock1" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 92 -1 0 } }  } 0 0 "Compensate clock of PLL \"%1!s!\" has been set to %2!s!" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_PLL_COMPUTATION_SUCCESS" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 Cyclone IV E PLL " "Info: Implemented PLL \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\" as Cyclone IV E PLL type" { { "Info" "ICUT_CUT_YGR_PLL_PARAMETERS_FACTORS" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|wire_pll1_clk\[1\] 33 10 0 0 " "Info: Implementing clock multiplication of 33, clock division of 10, and phase shift of 0 degrees (0 ps) for pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|wire_pll1_clk\[1\] port" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 50 -1 0 } }  } 0 0 "Implementing clock multiplication of %2!d!, clock division of %3!d!, and phase shift of %4!d! degrees (%5!d! ps) for %1!s! port" 0 0 "" 0 -1}  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 92 -1 0 } }  } 0 0 "Implemented %3!s! \"%1!s!\" as %2!s! PLL type" 0 0 "" 0 -1}
{ "Info" "IFITCC_FITCC_INFO_AUTO_FIT_COMPILATION_ON" "" "Info: Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" {  } {  } 0 0 "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" 0 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED" "" "Info: Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" { { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE10F17C8 " "Info: Device EP4CE10F17C8 is compatible" {  } {  } 2 0 "Device %1!s! is compatible" 0 0 "" 0 -1} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE6F17C8 " "Info: Device EP4CE6F17C8 is compatible" {  } {  } 2 0 "Device %1!s! is compatible" 0 0 "" 0 -1} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE22F17C8 " "Info: Device EP4CE22F17C8 is compatible" {  } {  } 2 0 "Device %1!s! is compatible" 0 0 "" 0 -1}  } {  } 2 0 "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" 0 0 "" 0 -1}
{ "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION" "5 " "Info: Fitter converted 5 user pins into dedicated programming pins" { { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_ASDO_DATA1~ C1 " "Info: Pin ~ALTERA_ASDO_DATA1~ is reserved at location C1" {  } { { "c:/altera/11.0/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/altera/11.0/quartus/bin64/pin_planner.ppl" { ~ALTERA_ASDO_DATA1~ } } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_ASDO_DATA1~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 698 5593 6598 0}  }  } }  } 0 0 "Pin %1!s! is reserved at location %2!s!" 0 0 "" 0 -1} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_FLASH_nCE_nCSO~ D2 " "Info: Pin ~ALTERA_FLASH_nCE_nCSO~ is reserved at location D2" {  } { { "c:/altera/11.0/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/altera/11.0/quartus/bin64/pin_planner.ppl" { ~ALTERA_FLASH_nCE_nCSO~ } } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_FLASH_nCE_nCSO~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 700 5593 6598 0}  }  } }  } 0 0 "Pin %1!s! is reserved at location %2!s!" 0 0 "" 0 -1} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DCLK~ H1 " "Info: Pin ~ALTERA_DCLK~ is reserved at location H1" {  } { { "c:/altera/11.0/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/altera/11.0/quartus/bin64/pin_planner.ppl" { ~ALTERA_DCLK~ } } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_DCLK~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 702 5593 6598 0}  }  } }  } 0 0 "Pin %1!s! is reserved at location %2!s!" 0 0 "" 0 -1} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DATA0~ H2 " "Info: Pin ~ALTERA_DATA0~ is reserved at location H2" {  } { { "c:/altera/11.0/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/altera/11.0/quartus/bin64/pin_planner.ppl" { ~ALTERA_DATA0~ } } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_DATA0~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 704 5593 6598 0}  }  } }  } 0 0 "Pin %1!s! is reserved at location %2!s!" 0 0 "" 0 -1} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_nCEO~ F16 " "Info: Pin ~ALTERA_nCEO~ is reserved at location F16" {  } { { "c:/altera/11.0/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/altera/11.0/quartus/bin64/pin_planner.ppl" { ~ALTERA_nCEO~ } } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_nCEO~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 706 5593 6598 0}  }  } }  } 0 0 "Pin %1!s! is reserved at location %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Fitter converted %1!d! user pins into dedicated programming pins" 0 0 "" 0 -1}
{ "Warning" "WCUT_CUT_ATOM_PINS_WITH_INCOMPLETE_IO_ASSIGNMENTS" "" "Warning: Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" {  } {  } 0 0 "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" 0 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_RAM_METASTABILITY_INFO" "" "Info: Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." {  } {  } 0 0 "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." 0 0 "" 0 -1}
{ "Info" "ITDC_FITTER_TIMING_ENGINE" "TimeQuest " "Info: Timing-driven compilation is using the TimeQuest Timing Analyzer" {  } {  } 0 0 "Timing-driven compilation is using the %1!s! Timing Analyzer" 0 0 "" 0 -1}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "DAC904_HighSpeedDAC.sdc " "Critical Warning: Synopsys Design Constraints File file not found: 'DAC904_HighSpeedDAC.sdc'. A Synopsys Design Constraints File is required by the TimeQuest Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 0 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the TimeQuest Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_FOUND_NO_DERIVING_MSG" "generated clocks " "Info: No user constrained generated clocks found in the design" {  } {  } 0 0 "No user constrained %1!s! found in the design" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_FOUND_NO_DERIVING_MSG" "base clocks " "Info: No user constrained base clocks found in the design" {  } {  } 0 0 "No user constrained %1!s! found in the design" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_CLOCKS_FOUND_NO_CLOCKS" "" "Info: The command derive_clocks did not find any clocks to derive.  No clocks were created or changed." {  } {  } 0 0 "The command derive_clocks did not find any clocks to derive.  No clocks were created or changed." 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "Info: No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 0 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_UNCERTAINTY_FOUND" "" "Info: The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." {  } {  } 0 0 "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." 0 0 "" 0 -1}
{ "Info" "ISTA_TDC_NO_DEFAULT_OPTIMIZATION_GOALS" "" "Info: Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." {  } {  } 0 0 "Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." 0 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|wire_pll1_clk\[1\] (placed in counter C0 of PLL_4) " "Info: Automatically promoted node pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|wire_pll1_clk\[1\] (placed in counter C0 of PLL_4)" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G18 " "Info: Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G18" {  } {  } 0 0 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "" 0 -1}  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 92 -1 0 } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { pll165m:U0_pll165m_inst|altpll:altpll_component|pll165m_altpll:auto_generated|wire_pll1_clk[0] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 132 5593 6598 0}  }  } }  } 0 0 "Automatically promoted node %1!s! %2!s!" 0 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "rstn~input (placed in PIN M1 (CLK3, DIFFCLK_1n)) " "Info: Automatically promoted node rstn~input (placed in PIN M1 (CLK3, DIFFCLK_1n))" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G3 " "Info: Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G3" {  } {  } 0 0 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "" 0 -1}  } { { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 3 0 0 } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { rstn~input } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 688 5593 6598 0}  }  } }  } 0 0 "Automatically promoted node %1!s! %2!s!" 0 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_START_REGPACKING_INFO" "" "Info: Starting register packing" {  } {  } 0 0 "Starting register packing" 0 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_START_REG_LOCATION_PROCESSING" "" "Extra Info: Performing register packing on registers with non-logic cell location assignments" {  } {  } 1 0 "Performing register packing on registers with non-logic cell location assignments" 1 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_FINISH_REG_LOCATION_PROCESSING" "" "Extra Info: Completed register packing on registers with non-logic cell location assignments" {  } {  } 1 0 "Completed register packing on registers with non-logic cell location assignments" 1 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_BEGIN_FAST_REGISTER_INFO" "" "Extra Info: Started Fast Input/Output/OE register processing" {  } {  } 1 0 "Started Fast Input/Output/OE register processing" 1 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_FAST_REGISTER_INFO" "" "Extra Info: Finished Fast Input/Output/OE register processing" {  } {  } 1 0 "Finished Fast Input/Output/OE register processing" 1 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_START_IO_MULT_RAM_PACKING" "" "Extra Info: Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" {  } {  } 1 0 "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" 1 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_FINISH_IO_MULT_RAM_PACKING" "" "Extra Info: Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" {  } {  } 1 0 "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" 1 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_REGPACKING_INFO" "" "Info: Finished register packing" { { "Extra Info" "IFSAC_NO_REGISTERS_WERE_PACKED" "" "Extra Info: No registers were packed into other blocks" {  } {  } 1 0 "No registers were packed into other blocks" 0 0 "" 0 -1}  } {  } 0 0 "Finished register packing" 0 0 "" 0 -1}
{ "Warning" "WCUT_PLL_CLK_FEEDS_NON_DEDICATED_IO" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 clk\[1\] clk_driver~output " "Warning: PLL \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\" output port clk\[1\] feeds output pin \"clk_driver~output\" via non-dedicated routing -- jitter performance depends on switching rate of other design elements. Use PLL dedicated clock outputs to ensure jitter performance" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 50 -1 0 } } { "altpll.tdf" "" { Text "c:/altera/11.0/quartus/libraries/megafunctions/altpll.tdf" 897 0 0 } } { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } } { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 21 0 0 } } { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 5 0 0 } }  } 0 0 "PLL \"%1!s!\" output port %2!s! feeds output pin \"%3!s!\" via non-dedicated routing -- jitter performance depends on switching rate of other design elements. Use PLL dedicated clock outputs to ensure jitter performance" 0 0 "" 0 -1}
{ "Warning" "WCUT_CUT_UNATTACHED_ASGN" "" "Warning: Ignored locations or region assignments to the following nodes" { { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyAF " "Warning: Node \"keyAF\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyAF" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1} { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyIn\[0\] " "Warning: Node \"keyIn\[0\]\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyIn\[0\]" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1} { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyIn\[1\] " "Warning: Node \"keyIn\[1\]\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyIn\[1\]" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1} { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyIn\[2\] " "Warning: Node \"keyIn\[2\]\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyIn\[2\]" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1} { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyIn\[3\] " "Warning: Node \"keyIn\[3\]\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyIn\[3\]" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1} { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyIn\[4\] " "Warning: Node \"keyIn\[4\]\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyIn\[4\]" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1}  } {  } 0 0 "Ignored locations or region assignments to the following nodes" 0 0 "" 0 -1}
{ "Info" "IFITCC_FITTER_PREPARATION_END" "00:00:00 " "Info: Fitter preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 0 "Fitter preparation operations ending: elapsed time is %1!s!" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_START" "" "Info: Fitter placement preparation operations beginning" {  } {  } 0 0 "Fitter placement preparation operations beginning" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_END" "00:00:00 " "Info: Fitter placement preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 0 "Fitter placement preparation operations ending: elapsed time is %1!s!" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_START" "" "Info: Fitter placement operations beginning" {  } {  } 0 0 "Fitter placement operations beginning" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_INFO_VPR_PLACEMENT_FINISH" "" "Info: Fitter placement was successful" {  } {  } 0 0 "Fitter placement was successful" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_END" "00:00:00 " "Info: Fitter placement operations ending: elapsed time is 00:00:00" {  } {  } 0 0 "Fitter placement operations ending: elapsed time is %1!s!" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_START" "" "Info: Fitter routing operations beginning" {  } {  } 0 0 "Fitter routing operations beginning" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_PERCENT_ROUTING_RESOURCE_USAGE" "0 " "Info: Router estimated average interconnect usage is 0% of the available device resources" { { "Info" "IFITAPI_FITAPI_VPR_PEAK_ROUTING_REGION" "2 X21_Y10 X30_Y19 " "Info: Router estimated peak interconnect usage is 2% of the available device resources in the region that extends from location X21_Y10 to location X30_Y19" {  } {  } 0 0 "Router estimated peak interconnect usage is %1!d!%% of the available device resources in the region that extends from location %2!s! to location %3!s!" 0 0 "" 0 -1}  } {  } 0 0 "Router estimated average interconnect usage is %1!d!%% of the available device resources" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_END" "00:00:00 " "Info: Fitter routing operations ending: elapsed time is 00:00:00" {  } {  } 0 0 "Fitter routing operations ending: elapsed time is %1!s!" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED" "" "Info: The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." { { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_ROUTABILITY" "" "Info: Optimizations that may affect the design's routability were skipped" {  } {  } 0 0 "Optimizations that may affect the design's routability were skipped" 0 0 "" 0 -1} { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_TIMING" "" "Info: Optimizations that may affect the design's timing were skipped" {  } {  } 0 0 "Optimizations that may affect the design's timing were skipped" 0 0 "" 0 -1}  } {  } 0 0 "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_STARTED" "" "Info: Started post-fitting delay annotation" {  } {  } 0 0 "Started post-fitting delay annotation" 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Info: Delay annotation completed successfully" {  } {  } 0 0 "Delay annotation completed successfully" 0 0 "" 0 -1}
{ "Warning" "WFITCC_FITCC_IGNORED_ASSIGNMENT" "" "Warning: Found invalid Fitter assignments. See the Ignored Assignments panel in the Fitter Compilation Report for more information." {  } {  } 0 0 "Found invalid Fitter assignments. See the Ignored Assignments panel in the Fitter Compilation Report for more information." 0 0 "" 0 -1}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/DAC904_HighSpeedDAC.fit.smsg " "Info: Generated suppressed messages file D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/DAC904_HighSpeedDAC.fit.smsg" {  } {  } 0 0 "Generated suppressed messages file %1!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_ERROR_COUNT" "Fitter 0 s 12 s Quartus II 64-Bit " "Info: Quartus II 64-Bit Fitter was successful. 0 errors, 12 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4696 " "Info: Peak virtual memory: 4696 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "" 0 -1} { "Info" "IQEXE_END_BANNER_TIME" "Tue Jul 25 11:26:14 2023 " "Info: Processing ended: Tue Jul 25 11:26:14 2023" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_TIME" "00:00:03 " "Info: Elapsed time: 00:00:03" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:03 " "Info: Total CPU time (on all processors): 00:00:03" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_SEPARATOR" "" "Info: *******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Assembler Quartus II 64-Bit " "Info: Running Quartus II 64-Bit Assembler" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version " "Info: Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_START_BANNER_TIME" "Tue Jul 25 11:26:15 2023 " "Info: Processing started: Tue Jul 25 11:26:15 2023" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "" 0 -1}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_asm --read_settings_files=off --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC " "Info: Command: quartus_asm --read_settings_files=off --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC" {  } {  } 0 0 "Command: %1!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_SEPARATOR" "" "Info: *******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "TimeQuest Timing Analyzer Quartus II 64-Bit " "Info: Running Quartus II 64-Bit TimeQuest Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version " "Info: Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_START_BANNER_TIME" "Tue Jul 25 11:26:15 2023 " "Info: Processing started: Tue Jul 25 11:26:15 2023" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "" 0 -1}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC " "Info: Command: quartus_sta DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC" {  } {  } 0 0 "Command: %1!s!" 0 0 "" 0 -1}
{ "Info" "0" "" "Info: qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "" 0 -1}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "6 6 " "Info: Parallel compilation is enabled and will use 6 of the 6 processors detected" {  } {  } 0 0 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Info: Low junction temperature is 0 degrees C" {  } {  } 0 0 "%1!s! is %2!s!" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "Info: High junction temperature is 85 degrees C" {  } {  } 0 0 "%1!s! is %2!s!" 0 0 "" 0 -1}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "DAC904_HighSpeedDAC.sdc " "Critical Warning: Synopsys Design Constraints File file not found: 'DAC904_HighSpeedDAC.sdc'. A Synopsys Design Constraints File is required by the TimeQuest Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 0 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the TimeQuest Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "generated clocks \"derive_pll_clocks -create_base_clocks\" " "Info: No user constrained generated clocks found in the design. Calling \"derive_pll_clocks -create_base_clocks\"" {  } {  } 0 0 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL Clocks " "Info: Deriving PLL Clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_clock -period 20.000 -waveform \{0.000 10.000\} -name clk clk " "Info: create_clock -period 20.000 -waveform \{0.000 10.000\} -name clk clk" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 10 -multiply_by 33 -duty_cycle 50.00 -name \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\} \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\} " "Info: create_generated_clock -source \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 10 -multiply_by 33 -duty_cycle 50.00 -name \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\} \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "base clocks \"derive_clocks -period 1.0\" " "Info: No user constrained base clocks found in the design. Calling \"derive_clocks -period 1.0\"" {  } {  } 0 0 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_CLOCKS_FOUND_NO_CLOCKS" "" "Info: The command derive_clocks did not find any clocks to derive.  No clocks were created or changed." {  } {  } 0 0 "The command derive_clocks did not find any clocks to derive.  No clocks were created or changed." 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "Info: No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 0 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty " "Info: Deriving Clock Uncertainty" { { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}
{ "Info" "0" "" "Info: Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "" 0 -1}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Critical Warning: Timing requirements not met" {  } {  } 1 0 "Timing requirements not met" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -0.485 " "Info: Worst-case setup slack is -0.485" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.485        -1.974 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:    -0.485        -1.974 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.463 " "Info: Worst-case hold slack is 0.463" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.463         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     0.463         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "IASM_ASM_GENERATING_POWER_DATA" "" "Info: Writing out detailed assembly data for power analysis" {  } {  } 0 0 "Writing out detailed assembly data for power analysis" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "Info: No Recovery paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "Info: No Removal paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.859 " "Info: Worst-case minimum pulse width slack is 1.859" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.859         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     1.859         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.931         0.000 clk  " "Info:     9.931         0.000 clk " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "IASM_ASM_GENERATING_PROGRAMMING_FILES" "" "Info: Assembler is generating device programming files" {  } {  } 0 0 "Assembler is generating device programming files" 0 0 "" 0 -1}
{ "Info" "0" "" "Info: Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_STARTED" "" "Info: Started post-fitting delay annotation" {  } {  } 0 0 "Started post-fitting delay annotation" 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Info: Delay annotation completed successfully" {  } {  } 0 0 "Delay annotation completed successfully" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty " "Info: Deriving Clock Uncertainty" { { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Critical Warning: Timing requirements not met" {  } {  } 1 0 "Timing requirements not met" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -0.281 " "Info: Worst-case setup slack is -0.281" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.281        -0.678 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:    -0.281        -0.678 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.418 " "Info: Worst-case hold slack is 0.418" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.418         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     0.418         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "Info: No Recovery paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "Info: No Removal paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.859 " "Info: Worst-case minimum pulse width slack is 1.859" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.859         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     1.859         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.943         0.000 clk  " "Info:     9.943         0.000 clk " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "0" "" "Info: Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_STARTED" "" "Info: Started post-fitting delay annotation" {  } {  } 0 0 "Started post-fitting delay annotation" 0 0 "" 0 -1}
{ "Info" "IQEXE_ERROR_COUNT" "Assembler 0 s 0 s Quartus II 64-Bit " "Info: Quartus II 64-Bit Assembler was successful. 0 errors, 0 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4472 " "Info: Peak virtual memory: 4472 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "" 0 -1} { "Info" "IQEXE_END_BANNER_TIME" "Tue Jul 25 11:26:16 2023 " "Info: Processing ended: Tue Jul 25 11:26:16 2023" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Info: Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Info: Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Info: Delay annotation completed successfully" {  } {  } 0 0 "Delay annotation completed successfully" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty " "Info: Deriving Clock Uncertainty" { { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 1.337 " "Info: Worst-case setup slack is 1.337" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.337         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     1.337         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.168 " "Info: Worst-case hold slack is 0.168" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.168         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     0.168         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "Info: No Recovery paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "Info: No Removal paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 2.778 " "Info: Worst-case minimum pulse width slack is 2.778" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.778         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     2.778         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.589         0.000 clk  " "Info:     9.589         0.000 clk " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Info: Design is not fully constrained for setup requirements" {  } {  } 0 0 "Design is not fully constrained for %1!s! requirements" 0 0 "" 0 -1}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Info: Design is not fully constrained for hold requirements" {  } {  } 0 0 "Design is not fully constrained for %1!s! requirements" 0 0 "" 0 -1}
{ "Info" "IQEXE_ERROR_COUNT" "TimeQuest Timing Analyzer 0 s 3 s Quartus II 64-Bit " "Info: Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 3 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4537 " "Info: Peak virtual memory: 4537 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "" 0 -1} { "Info" "IQEXE_END_BANNER_TIME" "Tue Jul 25 11:26:17 2023 " "Info: Processing ended: Tue Jul 25 11:26:17 2023" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_TIME" "00:00:02 " "Info: Elapsed time: 00:00:02" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:02 " "Info: Total CPU time (on all processors): 00:00:02" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "" 0 -1}
{ "Warning" "WFLOW_DISABLED_MODULE" "PowerPlay Power Analyzer FLOW_ENABLE_POWER_ANALYZER " "Warning: Skipped module PowerPlay Power Analyzer due to the assignment FLOW_ENABLE_POWER_ANALYZER" {  } {  } 0 0 "Skipped module %1!s! due to the assignment %2!s!" 0 0 "" 0 -1}
{ "Info" "IFLOW_ERROR_COUNT" "Full Compilation 0 s 21 s " "Info: Quartus II Full Compilation was successful. 0 errors, 21 warnings" {  } {  } 0 0 "Quartus II %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "" 0 -1}
