Analysis & Synthesis report for DAC904_HighSpeedDAC
Tue Jul 25 11:30:33 2023
Quartus II 64-Bit Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Analysis & Synthesis Summary
  3. Analysis & Synthesis Settings
  4. Parallel Compilation
  5. Analysis & Synthesis Source Files Read
  6. Analysis & Synthesis Resource Usage Summary
  7. Analysis & Synthesis Resource Utilization by Entity
  8. Analysis & Synthesis RAM Summary
  9. Analysis & Synthesis IP Cores Summary
 10. Registers Removed During Synthesis
 11. General Register Statistics
 12. Source assignments for rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated
 13. Parameter Settings for User Entity Instance: Top-level Entity: |DAC904_WriteTEST
 14. Parameter Settings for User Entity Instance: pll165m:U0_pll165m_inst|altpll:altpll_component
 15. Parameter Settings for User Entity Instance: rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component
 16. altpll Parameter Settings by Entity Instance
 17. altsyncram Parameter Settings by Entity Instance
 18. Port Connectivity Checks: "PhaseAccumulator:U1_PhaseAccumulator"
 19. Port Connectivity Checks: "pll165m:U0_pll165m_inst"
 20. Elapsed Time Per Partition
 21. Analysis & Synthesis Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2011 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+-------------------------------------------------------------------------------------+
; Analysis & Synthesis Summary                                                        ;
+------------------------------------+------------------------------------------------+
; Analysis & Synthesis Status        ; Successful - Tue Jul 25 11:30:33 2023          ;
; Quartus II 64-Bit Version          ; 11.0 Build 208 07/03/2011 SP 1 SJ Full Version ;
; Revision Name                      ; DAC904_HighSpeedDAC                            ;
; Top-level Entity Name              ; DAC904_WriteTEST                               ;
; Family                             ; Cyclone IV E                                   ;
; Total logic elements               ; 46                                             ;
;     Total combinational functions  ; 44                                             ;
;     Dedicated logic registers      ; 45                                             ;
; Total registers                    ; 45                                             ;
; Total pins                         ; 17                                             ;
; Total virtual pins                 ; 0                                              ;
; Total memory bits                  ; 229,376                                        ;
; Embedded Multiplier 9-bit elements ; 0                                              ;
; Total PLLs                         ; 1                                              ;
+------------------------------------+------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Settings                                                                                         ;
+----------------------------------------------------------------------------+--------------------+---------------------+
; Option                                                                     ; Setting            ; Default Value       ;
+----------------------------------------------------------------------------+--------------------+---------------------+
; Device                                                                     ; EP4CE15F17C8       ;                     ;
; Top-level entity name                                                      ; DAC904_WriteTEST   ; DAC904_HighSpeedDAC ;
; Family name                                                                ; Cyclone IV E       ; Cyclone IV GX       ;
; Use smart compilation                                                      ; Off                ; Off                 ;
; Enable parallel Assembler and TimeQuest Timing Analyzer during compilation ; On                 ; On                  ;
; Enable compact report table                                                ; Off                ; Off                 ;
; Restructure Multiplexers                                                   ; Auto               ; Auto                ;
; Create Debugging Nodes for IP Cores                                        ; Off                ; Off                 ;
; Preserve fewer node names                                                  ; On                 ; On                  ;
; Disable OpenCore Plus hardware evaluation                                  ; Off                ; Off                 ;
; Verilog Version                                                            ; Verilog_2001       ; Verilog_2001        ;
; VHDL Version                                                               ; VHDL_1993          ; VHDL_1993           ;
; State Machine Processing                                                   ; Auto               ; Auto                ;
; Safe State Machine                                                         ; Off                ; Off                 ;
; Extract Verilog State Machines                                             ; On                 ; On                  ;
; Extract VHDL State Machines                                                ; On                 ; On                  ;
; Ignore Verilog initial constructs                                          ; Off                ; Off                 ;
; Iteration limit for constant Verilog loops                                 ; 5000               ; 5000                ;
; Iteration limit for non-constant Verilog loops                             ; 250                ; 250                 ;
; Add Pass-Through Logic to Inferred RAMs                                    ; On                 ; On                  ;
; Parallel Synthesis                                                         ; On                 ; On                  ;
; DSP Block Balancing                                                        ; Auto               ; Auto                ;
; NOT Gate Push-Back                                                         ; On                 ; On                  ;
; Power-Up Don't Care                                                        ; On                 ; On                  ;
; Remove Redundant Logic Cells                                               ; Off                ; Off                 ;
; Remove Duplicate Registers                                                 ; On                 ; On                  ;
; Ignore CARRY Buffers                                                       ; Off                ; Off                 ;
; Ignore CASCADE Buffers                                                     ; Off                ; Off                 ;
; Ignore GLOBAL Buffers                                                      ; Off                ; Off                 ;
; Ignore ROW GLOBAL Buffers                                                  ; Off                ; Off                 ;
; Ignore LCELL Buffers                                                       ; Off                ; Off                 ;
; Ignore SOFT Buffers                                                        ; On                 ; On                  ;
; Limit AHDL Integers to 32 Bits                                             ; Off                ; Off                 ;
; Optimization Technique                                                     ; Balanced           ; Balanced            ;
; Carry Chain Length                                                         ; 70                 ; 70                  ;
; Auto Carry Chains                                                          ; On                 ; On                  ;
; Auto Open-Drain Pins                                                       ; On                 ; On                  ;
; Perform WYSIWYG Primitive Resynthesis                                      ; Off                ; Off                 ;
; Auto ROM Replacement                                                       ; On                 ; On                  ;
; Auto RAM Replacement                                                       ; On                 ; On                  ;
; Auto DSP Block Replacement                                                 ; On                 ; On                  ;
; Auto Shift Register Replacement                                            ; Auto               ; Auto                ;
; Allow Shift Register Merging across Hierarchies                            ; Auto               ; Auto                ;
; Auto Clock Enable Replacement                                              ; On                 ; On                  ;
; Strict RAM Replacement                                                     ; Off                ; Off                 ;
; Allow Synchronous Control Signals                                          ; On                 ; On                  ;
; Force Use of Synchronous Clear Signals                                     ; Off                ; Off                 ;
; Auto RAM Block Balancing                                                   ; On                 ; On                  ;
; Auto RAM to Logic Cell Conversion                                          ; Off                ; Off                 ;
; Auto Resource Sharing                                                      ; Off                ; Off                 ;
; Allow Any RAM Size For Recognition                                         ; Off                ; Off                 ;
; Allow Any ROM Size For Recognition                                         ; Off                ; Off                 ;
; Allow Any Shift Register Size For Recognition                              ; Off                ; Off                 ;
; Use LogicLock Constraints during Resource Balancing                        ; On                 ; On                  ;
; Ignore translate_off and synthesis_off directives                          ; Off                ; Off                 ;
; Timing-Driven Synthesis                                                    ; On                 ; On                  ;
; Report Parameter Settings                                                  ; On                 ; On                  ;
; Report Source Assignments                                                  ; On                 ; On                  ;
; Report Connectivity Checks                                                 ; On                 ; On                  ;
; Ignore Maximum Fan-Out Assignments                                         ; Off                ; Off                 ;
; Synchronization Register Chain Length                                      ; 2                  ; 2                   ;
; PowerPlay Power Optimization                                               ; Normal compilation ; Normal compilation  ;
; HDL message level                                                          ; Level2             ; Level2              ;
; Suppress Register Optimization Related Messages                            ; Off                ; Off                 ;
; Number of Removed Registers Reported in Synthesis Report                   ; 5000               ; 5000                ;
; Number of Inverted Registers Reported in Synthesis Report                  ; 100                ; 100                 ;
; Clock MUX Protection                                                       ; On                 ; On                  ;
; Auto Gated Clock Conversion                                                ; Off                ; Off                 ;
; Block Design Naming                                                        ; Auto               ; Auto                ;
; SDC constraint protection                                                  ; Off                ; Off                 ;
; Synthesis Effort                                                           ; Auto               ; Auto                ;
; Shift Register Replacement - Allow Asynchronous Clear Signal               ; On                 ; On                  ;
; Analysis & Synthesis Message Level                                         ; Medium             ; Medium              ;
; Disable Register Merging Across Hierarchies                                ; Auto               ; Auto                ;
; Resource Aware Inference For Block RAM                                     ; On                 ; On                  ;
; Synthesis Seed                                                             ; 1                  ; 1                   ;
+----------------------------------------------------------------------------+--------------------+---------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 6           ;
; Maximum allowed            ; 6           ;
;                            ;             ;
; Average used               ; 1.00        ;
; Maximum used               ; 1           ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     1 processor            ; 100.0%      ;
;     2-6 processors         ;   0.0%      ;
+----------------------------+-------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Source Files Read                                                                                                                                                                     ;
+--------------------------------------+-----------------+------------------------------+--------------------------------------------------------------------------------------------------------------------+
; File Name with User-Entered Path     ; Used in Netlist ; File Type                    ; File Name with Absolute Path                                                                                       ;
+--------------------------------------+-----------------+------------------------------+--------------------------------------------------------------------------------------------------------------------+
; ../rtl/PhaseAccumulator.V            ; yes             ; User Verilog HDL File        ; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PhaseAccumulator.V            ;
; ../rtl/DAC904_WriteTEST.V            ; yes             ; User Verilog HDL File        ; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V            ;
; ../rtl/DAC904_WriteModule.V          ; yes             ; User Verilog HDL File        ; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteModule.V          ;
; ../rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v ; yes             ; User Wizard-Generated File   ; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v ;
; ../rtl/PLL/pll165m/pll165m.v         ; yes             ; User Wizard-Generated File   ; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v         ;
; altpll.tdf                           ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/altpll.tdf                                                          ;
; aglobal110.inc                       ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/aglobal110.inc                                                      ;
; stratix_pll.inc                      ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/stratix_pll.inc                                                     ;
; stratixii_pll.inc                    ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/stratixii_pll.inc                                                   ;
; cycloneii_pll.inc                    ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/cycloneii_pll.inc                                                   ;
; db/pll165m_altpll.v                  ; yes             ; Auto-Generated Megafunction  ; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v           ;
; altsyncram.tdf                       ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/altsyncram.tdf                                                      ;
; stratix_ram_block.inc                ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/stratix_ram_block.inc                                               ;
; lpm_mux.inc                          ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/lpm_mux.inc                                                         ;
; lpm_decode.inc                       ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/lpm_decode.inc                                                      ;
; a_rdenreg.inc                        ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/a_rdenreg.inc                                                       ;
; altrom.inc                           ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/altrom.inc                                                          ;
; altram.inc                           ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/altram.inc                                                          ;
; altdpram.inc                         ; yes             ; Megafunction                 ; c:/altera/11.0/quartus/libraries/megafunctions/altdpram.inc                                                        ;
; db/altsyncram_vhb1.tdf               ; yes             ; Auto-Generated Megafunction  ; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/altsyncram_vhb1.tdf        ;
; db/decode_c8a.tdf                    ; yes             ; Auto-Generated Megafunction  ; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/decode_c8a.tdf             ;
; db/mux_gob.tdf                       ; yes             ; Auto-Generated Megafunction  ; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/mux_gob.tdf                ;
+--------------------------------------+-----------------+------------------------------+--------------------------------------------------------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Resource Usage Summary                                                                                                  ;
+---------------------------------------------+------------------------------------------------------------------------------------------------+
; Resource                                    ; Usage                                                                                          ;
+---------------------------------------------+------------------------------------------------------------------------------------------------+
; Estimated Total logic elements              ; 46                                                                                             ;
;                                             ;                                                                                                ;
; Total combinational functions               ; 44                                                                                             ;
; Logic element usage by number of LUT inputs ;                                                                                                ;
;     -- 4 input functions                    ; 0                                                                                              ;
;     -- 3 input functions                    ; 14                                                                                             ;
;     -- <=2 input functions                  ; 30                                                                                             ;
;                                             ;                                                                                                ;
; Logic elements by mode                      ;                                                                                                ;
;     -- normal mode                          ; 16                                                                                             ;
;     -- arithmetic mode                      ; 28                                                                                             ;
;                                             ;                                                                                                ;
; Total registers                             ; 45                                                                                             ;
;     -- Dedicated logic registers            ; 45                                                                                             ;
;     -- I/O registers                        ; 0                                                                                              ;
;                                             ;                                                                                                ;
; I/O pins                                    ; 17                                                                                             ;
; Total memory bits                           ; 229376                                                                                         ;
; Total PLLs                                  ; 1                                                                                              ;
;     -- PLLs                                 ; 1                                                                                              ;
;                                             ;                                                                                                ;
; Maximum fan-out node                        ; pll165m:U0_pll165m_inst|altpll:altpll_component|pll165m_altpll:auto_generated|wire_pll1_clk[0] ;
; Maximum fan-out                             ; 75                                                                                             ;
; Total fan-out                               ; 688                                                                                            ;
; Average fan-out                             ; 4.53                                                                                           ;
+---------------------------------------------+------------------------------------------------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Resource Utilization by Entity                                                                                                                                                                                                                                                 ;
+-----------------------------------------------+-------------------+--------------+-------------+--------------+---------+-----------+------+--------------+--------------------------------------------------------------------------------------------------------------------------+--------------+
; Compilation Hierarchy Node                    ; LC Combinationals ; LC Registers ; Memory Bits ; DSP Elements ; DSP 9x9 ; DSP 18x18 ; Pins ; Virtual Pins ; Full Hierarchy Name                                                                                                      ; Library Name ;
+-----------------------------------------------+-------------------+--------------+-------------+--------------+---------+-----------+------+--------------+--------------------------------------------------------------------------------------------------------------------------+--------------+
; |DAC904_WriteTEST                             ; 44 (0)            ; 45 (0)       ; 229376      ; 0            ; 0       ; 0         ; 17   ; 0            ; |DAC904_WriteTEST                                                                                                        ;              ;
;    |DAC904_WriteModule:U3_DAC904_WriteModule| ; 0 (0)             ; 14 (14)      ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_WriteTEST|DAC904_WriteModule:U3_DAC904_WriteModule                                                               ;              ;
;    |PhaseAccumulator:U1_PhaseAccumulator|     ; 30 (30)           ; 29 (29)      ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_WriteTEST|PhaseAccumulator:U1_PhaseAccumulator                                                                   ;              ;
;    |pll165m:U0_pll165m_inst|                  ; 0 (0)             ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_WriteTEST|pll165m:U0_pll165m_inst                                                                                ;              ;
;       |altpll:altpll_component|               ; 0 (0)             ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_WriteTEST|pll165m:U0_pll165m_inst|altpll:altpll_component                                                        ;              ;
;          |pll165m_altpll:auto_generated|      ; 0 (0)             ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_WriteTEST|pll165m:U0_pll165m_inst|altpll:altpll_component|pll165m_altpll:auto_generated                          ;              ;
;    |rom_14B_Sin:U2_rom_14B_Sin|               ; 14 (0)            ; 2 (0)        ; 229376      ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_WriteTEST|rom_14B_Sin:U2_rom_14B_Sin                                                                             ;              ;
;       |altsyncram:altsyncram_component|       ; 14 (0)            ; 2 (0)        ; 229376      ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_WriteTEST|rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component                                             ;              ;
;          |altsyncram_vhb1:auto_generated|     ; 14 (0)            ; 2 (2)        ; 229376      ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_WriteTEST|rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated              ;              ;
;             |mux_gob:mux2|                    ; 14 (14)           ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_WriteTEST|rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|mux_gob:mux2 ;              ;
+-----------------------------------------------+-------------------+--------------+-------------+--------------+---------+-----------+------+--------------+--------------------------------------------------------------------------------------------------------------------------+--------------+
Note: For table entries with two numbers listed, the numbers in parentheses indicate the number of resources of the given type used by the specific entity alone. The numbers listed outside of parentheses indicate the total resources of the given type used by the specific entity and all of its sub-entities in the hierarchy.


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis RAM Summary                                                                                                                                                                                                ;
+------------------------------------------------------------------------------------------------------+------+------+--------------+--------------+--------------+--------------+--------+---------------------------------------+
; Name                                                                                                 ; Type ; Mode ; Port A Depth ; Port A Width ; Port B Depth ; Port B Width ; Size   ; MIF                                   ;
+------------------------------------------------------------------------------------------------------+------+------+--------------+--------------+--------------+--------------+--------+---------------------------------------+
; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ALTSYNCRAM ; AUTO ; ROM  ; 16384        ; 14           ; --           ; --           ; 229376 ; ../../ROM/ROM_14B_SIN/ROM_14B_SIN.mif ;
+------------------------------------------------------------------------------------------------------+------+------+--------------+--------------+--------------+--------------+--------+---------------------------------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis IP Cores Summary                                                                                                                                                                                             ;
+--------+--------------+---------+--------------+--------------+----------------------------------------------+--------------------------------------------------------------------------------------------------------------------+
; Vendor ; IP Core Name ; Version ; Release Date ; License Type ; Entity Instance                              ; IP Include File                                                                                                    ;
+--------+--------------+---------+--------------+--------------+----------------------------------------------+--------------------------------------------------------------------------------------------------------------------+
; Altera ; ALTPLL       ; 11.0    ; N/A          ; N/A          ; |DAC904_WriteTEST|pll165m:U0_pll165m_inst    ; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v         ;
; Altera ; ROM: 1-PORT  ; 11.0    ; N/A          ; N/A          ; |DAC904_WriteTEST|rom_14B_Sin:U2_rom_14B_Sin ; D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v ;
+--------+--------------+---------+--------------+--------------+----------------------------------------------+--------------------------------------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------------+
; Registers Removed During Synthesis                                                                                ;
+----------------------------------------------------+--------------------------------------------------------------+
; Register name                                      ; Reason for Removal                                           ;
+----------------------------------------------------+--------------------------------------------------------------+
; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[1,2] ; Merged with PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[0] ;
; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[0]   ; Stuck at GND due to stuck port data_in                       ;
; Total Number of Removed Registers = 3              ;                                                              ;
+----------------------------------------------------+--------------------------------------------------------------+


+------------------------------------------------------+
; General Register Statistics                          ;
+----------------------------------------------+-------+
; Statistic                                    ; Value ;
+----------------------------------------------+-------+
; Total registers                              ; 45    ;
; Number of registers using Synchronous Clear  ; 0     ;
; Number of registers using Synchronous Load   ; 0     ;
; Number of registers using Asynchronous Clear ; 43    ;
; Number of registers using Asynchronous Load  ; 0     ;
; Number of registers using Clock Enable       ; 0     ;
; Number of registers using Preset             ; 0     ;
+----------------------------------------------+-------+


+------------------------------------------------------------------------------------------------------------------+
; Source assignments for rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated ;
+---------------------------------+--------------------+------+----------------------------------------------------+
; Assignment                      ; Value              ; From ; To                                                 ;
+---------------------------------+--------------------+------+----------------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                                  ;
+---------------------------------+--------------------+------+----------------------------------------------------+


+----------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: Top-level Entity: |DAC904_WriteTEST ;
+------------------+----------------------------------+----------------------------+
; Parameter Name   ; Value                            ; Type                       ;
+------------------+----------------------------------+----------------------------+
; freCtrlWord_1k   ; 00000000000000000110010110101110 ; Unsigned Binary            ;
; freCtrlWord_100k ; 00000000001001111011100000000010 ; Unsigned Binary            ;
; freCtrlWord_1M   ; 00000001100011010011000000011000 ; Unsigned Binary            ;
; freCtrlWord_10M  ; 00001111100000111110000011111000 ; Unsigned Binary            ;
; freCtrlWord_20M  ; 00011111000001111100000111110000 ; Unsigned Binary            ;
; freCtrlWord_30M  ; 00101110100010111010001011101000 ; Unsigned Binary            ;
; freCtrlWord_40M  ; 00111110000011111000001111100000 ; Unsigned Binary            ;
; freCtrlWord_50M  ; 01001101100100110110010011011001 ; Unsigned Binary            ;
+------------------+----------------------------------+----------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: pll165m:U0_pll165m_inst|altpll:altpll_component ;
+-------------------------------+---------------------------+----------------------------------+
; Parameter Name                ; Value                     ; Type                             ;
+-------------------------------+---------------------------+----------------------------------+
; OPERATION_MODE                ; NORMAL                    ; Untyped                          ;
; PLL_TYPE                      ; AUTO                      ; Untyped                          ;
; LPM_HINT                      ; CBX_MODULE_PREFIX=pll165m ; Untyped                          ;
; QUALIFY_CONF_DONE             ; OFF                       ; Untyped                          ;
; COMPENSATE_CLOCK              ; CLK0                      ; Untyped                          ;
; SCAN_CHAIN                    ; LONG                      ; Untyped                          ;
; PRIMARY_CLOCK                 ; INCLK0                    ; Untyped                          ;
; INCLK0_INPUT_FREQUENCY        ; 20000                     ; Signed Integer                   ;
; INCLK1_INPUT_FREQUENCY        ; 0                         ; Untyped                          ;
; GATE_LOCK_SIGNAL              ; NO                        ; Untyped                          ;
; GATE_LOCK_COUNTER             ; 0                         ; Untyped                          ;
; LOCK_HIGH                     ; 1                         ; Untyped                          ;
; LOCK_LOW                      ; 1                         ; Untyped                          ;
; VALID_LOCK_MULTIPLIER         ; 1                         ; Untyped                          ;
; INVALID_LOCK_MULTIPLIER       ; 5                         ; Untyped                          ;
; SWITCH_OVER_ON_LOSSCLK        ; OFF                       ; Untyped                          ;
; SWITCH_OVER_ON_GATED_LOCK     ; OFF                       ; Untyped                          ;
; ENABLE_SWITCH_OVER_COUNTER    ; OFF                       ; Untyped                          ;
; SKIP_VCO                      ; OFF                       ; Untyped                          ;
; SWITCH_OVER_COUNTER           ; 0                         ; Untyped                          ;
; SWITCH_OVER_TYPE              ; AUTO                      ; Untyped                          ;
; FEEDBACK_SOURCE               ; EXTCLK0                   ; Untyped                          ;
; BANDWIDTH                     ; 0                         ; Untyped                          ;
; BANDWIDTH_TYPE                ; AUTO                      ; Untyped                          ;
; SPREAD_FREQUENCY              ; 0                         ; Untyped                          ;
; DOWN_SPREAD                   ; 0                         ; Untyped                          ;
; SELF_RESET_ON_GATED_LOSS_LOCK ; OFF                       ; Untyped                          ;
; SELF_RESET_ON_LOSS_LOCK       ; OFF                       ; Untyped                          ;
; CLK9_MULTIPLY_BY              ; 0                         ; Untyped                          ;
; CLK8_MULTIPLY_BY              ; 0                         ; Untyped                          ;
; CLK7_MULTIPLY_BY              ; 0                         ; Untyped                          ;
; CLK6_MULTIPLY_BY              ; 0                         ; Untyped                          ;
; CLK5_MULTIPLY_BY              ; 1                         ; Untyped                          ;
; CLK4_MULTIPLY_BY              ; 1                         ; Untyped                          ;
; CLK3_MULTIPLY_BY              ; 1                         ; Untyped                          ;
; CLK2_MULTIPLY_BY              ; 1                         ; Untyped                          ;
; CLK1_MULTIPLY_BY              ; 33                        ; Signed Integer                   ;
; CLK0_MULTIPLY_BY              ; 33                        ; Signed Integer                   ;
; CLK9_DIVIDE_BY                ; 0                         ; Untyped                          ;
; CLK8_DIVIDE_BY                ; 0                         ; Untyped                          ;
; CLK7_DIVIDE_BY                ; 0                         ; Untyped                          ;
; CLK6_DIVIDE_BY                ; 0                         ; Untyped                          ;
; CLK5_DIVIDE_BY                ; 1                         ; Untyped                          ;
; CLK4_DIVIDE_BY                ; 1                         ; Untyped                          ;
; CLK3_DIVIDE_BY                ; 1                         ; Untyped                          ;
; CLK2_DIVIDE_BY                ; 1                         ; Untyped                          ;
; CLK1_DIVIDE_BY                ; 10                        ; Signed Integer                   ;
; CLK0_DIVIDE_BY                ; 25                        ; Signed Integer                   ;
; CLK9_PHASE_SHIFT              ; 0                         ; Untyped                          ;
; CLK8_PHASE_SHIFT              ; 0                         ; Untyped                          ;
; CLK7_PHASE_SHIFT              ; 0                         ; Untyped                          ;
; CLK6_PHASE_SHIFT              ; 0                         ; Untyped                          ;
; CLK5_PHASE_SHIFT              ; 0                         ; Untyped                          ;
; CLK4_PHASE_SHIFT              ; 0                         ; Untyped                          ;
; CLK3_PHASE_SHIFT              ; 0                         ; Untyped                          ;
; CLK2_PHASE_SHIFT              ; 0                         ; Untyped                          ;
; CLK1_PHASE_SHIFT              ; 0                         ; Untyped                          ;
; CLK0_PHASE_SHIFT              ; 0                         ; Untyped                          ;
; CLK5_TIME_DELAY               ; 0                         ; Untyped                          ;
; CLK4_TIME_DELAY               ; 0                         ; Untyped                          ;
; CLK3_TIME_DELAY               ; 0                         ; Untyped                          ;
; CLK2_TIME_DELAY               ; 0                         ; Untyped                          ;
; CLK1_TIME_DELAY               ; 0                         ; Untyped                          ;
; CLK0_TIME_DELAY               ; 0                         ; Untyped                          ;
; CLK9_DUTY_CYCLE               ; 50                        ; Untyped                          ;
; CLK8_DUTY_CYCLE               ; 50                        ; Untyped                          ;
; CLK7_DUTY_CYCLE               ; 50                        ; Untyped                          ;
; CLK6_DUTY_CYCLE               ; 50                        ; Untyped                          ;
; CLK5_DUTY_CYCLE               ; 50                        ; Untyped                          ;
; CLK4_DUTY_CYCLE               ; 50                        ; Untyped                          ;
; CLK3_DUTY_CYCLE               ; 50                        ; Untyped                          ;
; CLK2_DUTY_CYCLE               ; 50                        ; Untyped                          ;
; CLK1_DUTY_CYCLE               ; 50                        ; Signed Integer                   ;
; CLK0_DUTY_CYCLE               ; 50                        ; Signed Integer                   ;
; CLK9_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                          ;
; CLK8_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                          ;
; CLK7_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                          ;
; CLK6_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                          ;
; CLK5_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                          ;
; CLK4_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                          ;
; CLK3_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                          ;
; CLK2_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                          ;
; CLK1_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                          ;
; CLK0_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                          ;
; CLK9_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                          ;
; CLK8_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                          ;
; CLK7_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                          ;
; CLK6_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                          ;
; CLK5_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                          ;
; CLK4_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                          ;
; CLK3_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                          ;
; CLK2_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                          ;
; CLK1_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                          ;
; CLK0_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                          ;
; LOCK_WINDOW_UI                ;  0.05                     ; Untyped                          ;
; LOCK_WINDOW_UI_BITS           ; UNUSED                    ; Untyped                          ;
; VCO_RANGE_DETECTOR_LOW_BITS   ; UNUSED                    ; Untyped                          ;
; VCO_RANGE_DETECTOR_HIGH_BITS  ; UNUSED                    ; Untyped                          ;
; DPA_MULTIPLY_BY               ; 0                         ; Untyped                          ;
; DPA_DIVIDE_BY                 ; 1                         ; Untyped                          ;
; DPA_DIVIDER                   ; 0                         ; Untyped                          ;
; EXTCLK3_MULTIPLY_BY           ; 1                         ; Untyped                          ;
; EXTCLK2_MULTIPLY_BY           ; 1                         ; Untyped                          ;
; EXTCLK1_MULTIPLY_BY           ; 1                         ; Untyped                          ;
; EXTCLK0_MULTIPLY_BY           ; 1                         ; Untyped                          ;
; EXTCLK3_DIVIDE_BY             ; 1                         ; Untyped                          ;
; EXTCLK2_DIVIDE_BY             ; 1                         ; Untyped                          ;
; EXTCLK1_DIVIDE_BY             ; 1                         ; Untyped                          ;
; EXTCLK0_DIVIDE_BY             ; 1                         ; Untyped                          ;
; EXTCLK3_PHASE_SHIFT           ; 0                         ; Untyped                          ;
; EXTCLK2_PHASE_SHIFT           ; 0                         ; Untyped                          ;
; EXTCLK1_PHASE_SHIFT           ; 0                         ; Untyped                          ;
; EXTCLK0_PHASE_SHIFT           ; 0                         ; Untyped                          ;
; EXTCLK3_TIME_DELAY            ; 0                         ; Untyped                          ;
; EXTCLK2_TIME_DELAY            ; 0                         ; Untyped                          ;
; EXTCLK1_TIME_DELAY            ; 0                         ; Untyped                          ;
; EXTCLK0_TIME_DELAY            ; 0                         ; Untyped                          ;
; EXTCLK3_DUTY_CYCLE            ; 50                        ; Untyped                          ;
; EXTCLK2_DUTY_CYCLE            ; 50                        ; Untyped                          ;
; EXTCLK1_DUTY_CYCLE            ; 50                        ; Untyped                          ;
; EXTCLK0_DUTY_CYCLE            ; 50                        ; Untyped                          ;
; VCO_MULTIPLY_BY               ; 0                         ; Untyped                          ;
; VCO_DIVIDE_BY                 ; 0                         ; Untyped                          ;
; SCLKOUT0_PHASE_SHIFT          ; 0                         ; Untyped                          ;
; SCLKOUT1_PHASE_SHIFT          ; 0                         ; Untyped                          ;
; VCO_MIN                       ; 0                         ; Untyped                          ;
; VCO_MAX                       ; 0                         ; Untyped                          ;
; VCO_CENTER                    ; 0                         ; Untyped                          ;
; PFD_MIN                       ; 0                         ; Untyped                          ;
; PFD_MAX                       ; 0                         ; Untyped                          ;
; M_INITIAL                     ; 0                         ; Untyped                          ;
; M                             ; 0                         ; Untyped                          ;
; N                             ; 1                         ; Untyped                          ;
; M2                            ; 1                         ; Untyped                          ;
; N2                            ; 1                         ; Untyped                          ;
; SS                            ; 1                         ; Untyped                          ;
; C0_HIGH                       ; 0                         ; Untyped                          ;
; C1_HIGH                       ; 0                         ; Untyped                          ;
; C2_HIGH                       ; 0                         ; Untyped                          ;
; C3_HIGH                       ; 0                         ; Untyped                          ;
; C4_HIGH                       ; 0                         ; Untyped                          ;
; C5_HIGH                       ; 0                         ; Untyped                          ;
; C6_HIGH                       ; 0                         ; Untyped                          ;
; C7_HIGH                       ; 0                         ; Untyped                          ;
; C8_HIGH                       ; 0                         ; Untyped                          ;
; C9_HIGH                       ; 0                         ; Untyped                          ;
; C0_LOW                        ; 0                         ; Untyped                          ;
; C1_LOW                        ; 0                         ; Untyped                          ;
; C2_LOW                        ; 0                         ; Untyped                          ;
; C3_LOW                        ; 0                         ; Untyped                          ;
; C4_LOW                        ; 0                         ; Untyped                          ;
; C5_LOW                        ; 0                         ; Untyped                          ;
; C6_LOW                        ; 0                         ; Untyped                          ;
; C7_LOW                        ; 0                         ; Untyped                          ;
; C8_LOW                        ; 0                         ; Untyped                          ;
; C9_LOW                        ; 0                         ; Untyped                          ;
; C0_INITIAL                    ; 0                         ; Untyped                          ;
; C1_INITIAL                    ; 0                         ; Untyped                          ;
; C2_INITIAL                    ; 0                         ; Untyped                          ;
; C3_INITIAL                    ; 0                         ; Untyped                          ;
; C4_INITIAL                    ; 0                         ; Untyped                          ;
; C5_INITIAL                    ; 0                         ; Untyped                          ;
; C6_INITIAL                    ; 0                         ; Untyped                          ;
; C7_INITIAL                    ; 0                         ; Untyped                          ;
; C8_INITIAL                    ; 0                         ; Untyped                          ;
; C9_INITIAL                    ; 0                         ; Untyped                          ;
; C0_MODE                       ; BYPASS                    ; Untyped                          ;
; C1_MODE                       ; BYPASS                    ; Untyped                          ;
; C2_MODE                       ; BYPASS                    ; Untyped                          ;
; C3_MODE                       ; BYPASS                    ; Untyped                          ;
; C4_MODE                       ; BYPASS                    ; Untyped                          ;
; C5_MODE                       ; BYPASS                    ; Untyped                          ;
; C6_MODE                       ; BYPASS                    ; Untyped                          ;
; C7_MODE                       ; BYPASS                    ; Untyped                          ;
; C8_MODE                       ; BYPASS                    ; Untyped                          ;
; C9_MODE                       ; BYPASS                    ; Untyped                          ;
; C0_PH                         ; 0                         ; Untyped                          ;
; C1_PH                         ; 0                         ; Untyped                          ;
; C2_PH                         ; 0                         ; Untyped                          ;
; C3_PH                         ; 0                         ; Untyped                          ;
; C4_PH                         ; 0                         ; Untyped                          ;
; C5_PH                         ; 0                         ; Untyped                          ;
; C6_PH                         ; 0                         ; Untyped                          ;
; C7_PH                         ; 0                         ; Untyped                          ;
; C8_PH                         ; 0                         ; Untyped                          ;
; C9_PH                         ; 0                         ; Untyped                          ;
; L0_HIGH                       ; 1                         ; Untyped                          ;
; L1_HIGH                       ; 1                         ; Untyped                          ;
; G0_HIGH                       ; 1                         ; Untyped                          ;
; G1_HIGH                       ; 1                         ; Untyped                          ;
; G2_HIGH                       ; 1                         ; Untyped                          ;
; G3_HIGH                       ; 1                         ; Untyped                          ;
; E0_HIGH                       ; 1                         ; Untyped                          ;
; E1_HIGH                       ; 1                         ; Untyped                          ;
; E2_HIGH                       ; 1                         ; Untyped                          ;
; E3_HIGH                       ; 1                         ; Untyped                          ;
; L0_LOW                        ; 1                         ; Untyped                          ;
; L1_LOW                        ; 1                         ; Untyped                          ;
; G0_LOW                        ; 1                         ; Untyped                          ;
; G1_LOW                        ; 1                         ; Untyped                          ;
; G2_LOW                        ; 1                         ; Untyped                          ;
; G3_LOW                        ; 1                         ; Untyped                          ;
; E0_LOW                        ; 1                         ; Untyped                          ;
; E1_LOW                        ; 1                         ; Untyped                          ;
; E2_LOW                        ; 1                         ; Untyped                          ;
; E3_LOW                        ; 1                         ; Untyped                          ;
; L0_INITIAL                    ; 1                         ; Untyped                          ;
; L1_INITIAL                    ; 1                         ; Untyped                          ;
; G0_INITIAL                    ; 1                         ; Untyped                          ;
; G1_INITIAL                    ; 1                         ; Untyped                          ;
; G2_INITIAL                    ; 1                         ; Untyped                          ;
; G3_INITIAL                    ; 1                         ; Untyped                          ;
; E0_INITIAL                    ; 1                         ; Untyped                          ;
; E1_INITIAL                    ; 1                         ; Untyped                          ;
; E2_INITIAL                    ; 1                         ; Untyped                          ;
; E3_INITIAL                    ; 1                         ; Untyped                          ;
; L0_MODE                       ; BYPASS                    ; Untyped                          ;
; L1_MODE                       ; BYPASS                    ; Untyped                          ;
; G0_MODE                       ; BYPASS                    ; Untyped                          ;
; G1_MODE                       ; BYPASS                    ; Untyped                          ;
; G2_MODE                       ; BYPASS                    ; Untyped                          ;
; G3_MODE                       ; BYPASS                    ; Untyped                          ;
; E0_MODE                       ; BYPASS                    ; Untyped                          ;
; E1_MODE                       ; BYPASS                    ; Untyped                          ;
; E2_MODE                       ; BYPASS                    ; Untyped                          ;
; E3_MODE                       ; BYPASS                    ; Untyped                          ;
; L0_PH                         ; 0                         ; Untyped                          ;
; L1_PH                         ; 0                         ; Untyped                          ;
; G0_PH                         ; 0                         ; Untyped                          ;
; G1_PH                         ; 0                         ; Untyped                          ;
; G2_PH                         ; 0                         ; Untyped                          ;
; G3_PH                         ; 0                         ; Untyped                          ;
; E0_PH                         ; 0                         ; Untyped                          ;
; E1_PH                         ; 0                         ; Untyped                          ;
; E2_PH                         ; 0                         ; Untyped                          ;
; E3_PH                         ; 0                         ; Untyped                          ;
; M_PH                          ; 0                         ; Untyped                          ;
; C1_USE_CASC_IN                ; OFF                       ; Untyped                          ;
; C2_USE_CASC_IN                ; OFF                       ; Untyped                          ;
; C3_USE_CASC_IN                ; OFF                       ; Untyped                          ;
; C4_USE_CASC_IN                ; OFF                       ; Untyped                          ;
; C5_USE_CASC_IN                ; OFF                       ; Untyped                          ;
; C6_USE_CASC_IN                ; OFF                       ; Untyped                          ;
; C7_USE_CASC_IN                ; OFF                       ; Untyped                          ;
; C8_USE_CASC_IN                ; OFF                       ; Untyped                          ;
; C9_USE_CASC_IN                ; OFF                       ; Untyped                          ;
; CLK0_COUNTER                  ; G0                        ; Untyped                          ;
; CLK1_COUNTER                  ; G0                        ; Untyped                          ;
; CLK2_COUNTER                  ; G0                        ; Untyped                          ;
; CLK3_COUNTER                  ; G0                        ; Untyped                          ;
; CLK4_COUNTER                  ; G0                        ; Untyped                          ;
; CLK5_COUNTER                  ; G0                        ; Untyped                          ;
; CLK6_COUNTER                  ; E0                        ; Untyped                          ;
; CLK7_COUNTER                  ; E1                        ; Untyped                          ;
; CLK8_COUNTER                  ; E2                        ; Untyped                          ;
; CLK9_COUNTER                  ; E3                        ; Untyped                          ;
; L0_TIME_DELAY                 ; 0                         ; Untyped                          ;
; L1_TIME_DELAY                 ; 0                         ; Untyped                          ;
; G0_TIME_DELAY                 ; 0                         ; Untyped                          ;
; G1_TIME_DELAY                 ; 0                         ; Untyped                          ;
; G2_TIME_DELAY                 ; 0                         ; Untyped                          ;
; G3_TIME_DELAY                 ; 0                         ; Untyped                          ;
; E0_TIME_DELAY                 ; 0                         ; Untyped                          ;
; E1_TIME_DELAY                 ; 0                         ; Untyped                          ;
; E2_TIME_DELAY                 ; 0                         ; Untyped                          ;
; E3_TIME_DELAY                 ; 0                         ; Untyped                          ;
; M_TIME_DELAY                  ; 0                         ; Untyped                          ;
; N_TIME_DELAY                  ; 0                         ; Untyped                          ;
; EXTCLK3_COUNTER               ; E3                        ; Untyped                          ;
; EXTCLK2_COUNTER               ; E2                        ; Untyped                          ;
; EXTCLK1_COUNTER               ; E1                        ; Untyped                          ;
; EXTCLK0_COUNTER               ; E0                        ; Untyped                          ;
; ENABLE0_COUNTER               ; L0                        ; Untyped                          ;
; ENABLE1_COUNTER               ; L0                        ; Untyped                          ;
; CHARGE_PUMP_CURRENT           ; 2                         ; Untyped                          ;
; LOOP_FILTER_R                 ;  1.000000                 ; Untyped                          ;
; LOOP_FILTER_C                 ; 5                         ; Untyped                          ;
; CHARGE_PUMP_CURRENT_BITS      ; 9999                      ; Untyped                          ;
; LOOP_FILTER_R_BITS            ; 9999                      ; Untyped                          ;
; LOOP_FILTER_C_BITS            ; 9999                      ; Untyped                          ;
; VCO_POST_SCALE                ; 0                         ; Untyped                          ;
; CLK2_OUTPUT_FREQUENCY         ; 0                         ; Untyped                          ;
; CLK1_OUTPUT_FREQUENCY         ; 0                         ; Untyped                          ;
; CLK0_OUTPUT_FREQUENCY         ; 0                         ; Untyped                          ;
; INTENDED_DEVICE_FAMILY        ; Cyclone IV E              ; Untyped                          ;
; PORT_CLKENA0                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLKENA1                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLKENA2                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLKENA3                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLKENA4                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLKENA5                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_EXTCLKENA0               ; PORT_CONNECTIVITY         ; Untyped                          ;
; PORT_EXTCLKENA1               ; PORT_CONNECTIVITY         ; Untyped                          ;
; PORT_EXTCLKENA2               ; PORT_CONNECTIVITY         ; Untyped                          ;
; PORT_EXTCLKENA3               ; PORT_CONNECTIVITY         ; Untyped                          ;
; PORT_EXTCLK0                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_EXTCLK1                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_EXTCLK2                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_EXTCLK3                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLKBAD0                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLKBAD1                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLK0                     ; PORT_USED                 ; Untyped                          ;
; PORT_CLK1                     ; PORT_USED                 ; Untyped                          ;
; PORT_CLK2                     ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLK3                     ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLK4                     ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLK5                     ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLK6                     ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLK7                     ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLK8                     ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLK9                     ; PORT_UNUSED               ; Untyped                          ;
; PORT_SCANDATA                 ; PORT_UNUSED               ; Untyped                          ;
; PORT_SCANDATAOUT              ; PORT_UNUSED               ; Untyped                          ;
; PORT_SCANDONE                 ; PORT_UNUSED               ; Untyped                          ;
; PORT_SCLKOUT1                 ; PORT_CONNECTIVITY         ; Untyped                          ;
; PORT_SCLKOUT0                 ; PORT_CONNECTIVITY         ; Untyped                          ;
; PORT_ACTIVECLOCK              ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLKLOSS                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_INCLK1                   ; PORT_UNUSED               ; Untyped                          ;
; PORT_INCLK0                   ; PORT_USED                 ; Untyped                          ;
; PORT_FBIN                     ; PORT_UNUSED               ; Untyped                          ;
; PORT_PLLENA                   ; PORT_UNUSED               ; Untyped                          ;
; PORT_CLKSWITCH                ; PORT_UNUSED               ; Untyped                          ;
; PORT_ARESET                   ; PORT_USED                 ; Untyped                          ;
; PORT_PFDENA                   ; PORT_UNUSED               ; Untyped                          ;
; PORT_SCANCLK                  ; PORT_UNUSED               ; Untyped                          ;
; PORT_SCANACLR                 ; PORT_UNUSED               ; Untyped                          ;
; PORT_SCANREAD                 ; PORT_UNUSED               ; Untyped                          ;
; PORT_SCANWRITE                ; PORT_UNUSED               ; Untyped                          ;
; PORT_ENABLE0                  ; PORT_CONNECTIVITY         ; Untyped                          ;
; PORT_ENABLE1                  ; PORT_CONNECTIVITY         ; Untyped                          ;
; PORT_LOCKED                   ; PORT_USED                 ; Untyped                          ;
; PORT_CONFIGUPDATE             ; PORT_UNUSED               ; Untyped                          ;
; PORT_FBOUT                    ; PORT_CONNECTIVITY         ; Untyped                          ;
; PORT_PHASEDONE                ; PORT_UNUSED               ; Untyped                          ;
; PORT_PHASESTEP                ; PORT_UNUSED               ; Untyped                          ;
; PORT_PHASEUPDOWN              ; PORT_UNUSED               ; Untyped                          ;
; PORT_SCANCLKENA               ; PORT_UNUSED               ; Untyped                          ;
; PORT_PHASECOUNTERSELECT       ; PORT_UNUSED               ; Untyped                          ;
; PORT_VCOOVERRANGE             ; PORT_CONNECTIVITY         ; Untyped                          ;
; PORT_VCOUNDERRANGE            ; PORT_CONNECTIVITY         ; Untyped                          ;
; M_TEST_SOURCE                 ; 5                         ; Untyped                          ;
; C0_TEST_SOURCE                ; 5                         ; Untyped                          ;
; C1_TEST_SOURCE                ; 5                         ; Untyped                          ;
; C2_TEST_SOURCE                ; 5                         ; Untyped                          ;
; C3_TEST_SOURCE                ; 5                         ; Untyped                          ;
; C4_TEST_SOURCE                ; 5                         ; Untyped                          ;
; C5_TEST_SOURCE                ; 5                         ; Untyped                          ;
; C6_TEST_SOURCE                ; 5                         ; Untyped                          ;
; C7_TEST_SOURCE                ; 5                         ; Untyped                          ;
; C8_TEST_SOURCE                ; 5                         ; Untyped                          ;
; C9_TEST_SOURCE                ; 5                         ; Untyped                          ;
; CBXI_PARAMETER                ; pll165m_altpll            ; Untyped                          ;
; VCO_FREQUENCY_CONTROL         ; AUTO                      ; Untyped                          ;
; VCO_PHASE_SHIFT_STEP          ; 0                         ; Untyped                          ;
; WIDTH_CLOCK                   ; 5                         ; Signed Integer                   ;
; WIDTH_PHASECOUNTERSELECT      ; 4                         ; Untyped                          ;
; USING_FBMIMICBIDIR_PORT       ; OFF                       ; Untyped                          ;
; DEVICE_FAMILY                 ; Cyclone IV E              ; Untyped                          ;
; SCAN_CHAIN_MIF_FILE           ; UNUSED                    ; Untyped                          ;
; SIM_GATE_LOCK_DEVICE_BEHAVIOR ; OFF                       ; Untyped                          ;
; AUTO_CARRY_CHAINS             ; ON                        ; AUTO_CARRY                       ;
; IGNORE_CARRY_BUFFERS          ; OFF                       ; IGNORE_CARRY                     ;
; AUTO_CASCADE_CHAINS           ; ON                        ; AUTO_CASCADE                     ;
; IGNORE_CASCADE_BUFFERS        ; OFF                       ; IGNORE_CASCADE                   ;
+-------------------------------+---------------------------+----------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component ;
+------------------------------------+---------------------------------------+----------------------------+
; Parameter Name                     ; Value                                 ; Type                       ;
+------------------------------------+---------------------------------------+----------------------------+
; BYTE_SIZE_BLOCK                    ; 8                                     ; Untyped                    ;
; AUTO_CARRY_CHAINS                  ; ON                                    ; AUTO_CARRY                 ;
; IGNORE_CARRY_BUFFERS               ; OFF                                   ; IGNORE_CARRY               ;
; AUTO_CASCADE_CHAINS                ; ON                                    ; AUTO_CASCADE               ;
; IGNORE_CASCADE_BUFFERS             ; OFF                                   ; IGNORE_CASCADE             ;
; WIDTH_BYTEENA                      ; 1                                     ; Untyped                    ;
; OPERATION_MODE                     ; ROM                                   ; Untyped                    ;
; WIDTH_A                            ; 14                                    ; Signed Integer             ;
; WIDTHAD_A                          ; 14                                    ; Signed Integer             ;
; NUMWORDS_A                         ; 16384                                 ; Signed Integer             ;
; OUTDATA_REG_A                      ; CLOCK0                                ; Untyped                    ;
; ADDRESS_ACLR_A                     ; NONE                                  ; Untyped                    ;
; OUTDATA_ACLR_A                     ; NONE                                  ; Untyped                    ;
; WRCONTROL_ACLR_A                   ; NONE                                  ; Untyped                    ;
; INDATA_ACLR_A                      ; NONE                                  ; Untyped                    ;
; BYTEENA_ACLR_A                     ; NONE                                  ; Untyped                    ;
; WIDTH_B                            ; 1                                     ; Untyped                    ;
; WIDTHAD_B                          ; 1                                     ; Untyped                    ;
; NUMWORDS_B                         ; 1                                     ; Untyped                    ;
; INDATA_REG_B                       ; CLOCK1                                ; Untyped                    ;
; WRCONTROL_WRADDRESS_REG_B          ; CLOCK1                                ; Untyped                    ;
; RDCONTROL_REG_B                    ; CLOCK1                                ; Untyped                    ;
; ADDRESS_REG_B                      ; CLOCK1                                ; Untyped                    ;
; OUTDATA_REG_B                      ; UNREGISTERED                          ; Untyped                    ;
; BYTEENA_REG_B                      ; CLOCK1                                ; Untyped                    ;
; INDATA_ACLR_B                      ; NONE                                  ; Untyped                    ;
; WRCONTROL_ACLR_B                   ; NONE                                  ; Untyped                    ;
; ADDRESS_ACLR_B                     ; NONE                                  ; Untyped                    ;
; OUTDATA_ACLR_B                     ; NONE                                  ; Untyped                    ;
; RDCONTROL_ACLR_B                   ; NONE                                  ; Untyped                    ;
; BYTEENA_ACLR_B                     ; NONE                                  ; Untyped                    ;
; WIDTH_BYTEENA_A                    ; 1                                     ; Signed Integer             ;
; WIDTH_BYTEENA_B                    ; 1                                     ; Untyped                    ;
; RAM_BLOCK_TYPE                     ; AUTO                                  ; Untyped                    ;
; BYTE_SIZE                          ; 8                                     ; Untyped                    ;
; READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                             ; Untyped                    ;
; READ_DURING_WRITE_MODE_PORT_A      ; NEW_DATA_NO_NBE_READ                  ; Untyped                    ;
; READ_DURING_WRITE_MODE_PORT_B      ; NEW_DATA_NO_NBE_READ                  ; Untyped                    ;
; INIT_FILE                          ; ../../ROM/ROM_14B_SIN/ROM_14B_SIN.mif ; Untyped                    ;
; INIT_FILE_LAYOUT                   ; PORT_A                                ; Untyped                    ;
; MAXIMUM_DEPTH                      ; 0                                     ; Untyped                    ;
; CLOCK_ENABLE_INPUT_A               ; BYPASS                                ; Untyped                    ;
; CLOCK_ENABLE_INPUT_B               ; NORMAL                                ; Untyped                    ;
; CLOCK_ENABLE_OUTPUT_A              ; BYPASS                                ; Untyped                    ;
; CLOCK_ENABLE_OUTPUT_B              ; NORMAL                                ; Untyped                    ;
; CLOCK_ENABLE_CORE_A                ; USE_INPUT_CLKEN                       ; Untyped                    ;
; CLOCK_ENABLE_CORE_B                ; USE_INPUT_CLKEN                       ; Untyped                    ;
; ENABLE_ECC                         ; FALSE                                 ; Untyped                    ;
; WIDTH_ECCSTATUS                    ; 3                                     ; Untyped                    ;
; DEVICE_FAMILY                      ; Cyclone IV E                          ; Untyped                    ;
; CBXI_PARAMETER                     ; altsyncram_vhb1                       ; Untyped                    ;
+------------------------------------+---------------------------------------+----------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------------------+
; altpll Parameter Settings by Entity Instance                                    ;
+-------------------------------+-------------------------------------------------+
; Name                          ; Value                                           ;
+-------------------------------+-------------------------------------------------+
; Number of entity instances    ; 1                                               ;
; Entity Instance               ; pll165m:U0_pll165m_inst|altpll:altpll_component ;
;     -- OPERATION_MODE         ; NORMAL                                          ;
;     -- PLL_TYPE               ; AUTO                                            ;
;     -- PRIMARY_CLOCK          ; INCLK0                                          ;
;     -- INCLK0_INPUT_FREQUENCY ; 20000                                           ;
;     -- INCLK1_INPUT_FREQUENCY ; 0                                               ;
;     -- VCO_MULTIPLY_BY        ; 0                                               ;
;     -- VCO_DIVIDE_BY          ; 0                                               ;
+-------------------------------+-------------------------------------------------+


+--------------------------------------------------------------------------------------------------------+
; altsyncram Parameter Settings by Entity Instance                                                       ;
+-------------------------------------------+------------------------------------------------------------+
; Name                                      ; Value                                                      ;
+-------------------------------------------+------------------------------------------------------------+
; Number of entity instances                ; 1                                                          ;
; Entity Instance                           ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component ;
;     -- OPERATION_MODE                     ; ROM                                                        ;
;     -- WIDTH_A                            ; 14                                                         ;
;     -- NUMWORDS_A                         ; 16384                                                      ;
;     -- OUTDATA_REG_A                      ; CLOCK0                                                     ;
;     -- WIDTH_B                            ; 1                                                          ;
;     -- NUMWORDS_B                         ; 1                                                          ;
;     -- ADDRESS_REG_B                      ; CLOCK1                                                     ;
;     -- OUTDATA_REG_B                      ; UNREGISTERED                                               ;
;     -- RAM_BLOCK_TYPE                     ; AUTO                                                       ;
;     -- READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                                                  ;
+-------------------------------------------+------------------------------------------------------------+


+------------------------------------------------------------------+
; Port Connectivity Checks: "PhaseAccumulator:U1_PhaseAccumulator" ;
+-------------------+-------+----------+---------------------------+
; Port              ; Type  ; Severity ; Details                   ;
+-------------------+-------+----------+---------------------------+
; PhaseStep[24..23] ; Input ; Info     ; Stuck at VCC              ;
; PhaseStep[19..18] ; Input ; Info     ; Stuck at VCC              ;
; PhaseStep[13..12] ; Input ; Info     ; Stuck at VCC              ;
; PhaseStep[4..3]   ; Input ; Info     ; Stuck at VCC              ;
; PhaseStep[31..25] ; Input ; Info     ; Stuck at GND              ;
; PhaseStep[22..20] ; Input ; Info     ; Stuck at GND              ;
; PhaseStep[15..14] ; Input ; Info     ; Stuck at GND              ;
; PhaseStep[11..5]  ; Input ; Info     ; Stuck at GND              ;
; PhaseStep[2..0]   ; Input ; Info     ; Stuck at GND              ;
; PhaseStep[17]     ; Input ; Info     ; Stuck at GND              ;
; PhaseStep[16]     ; Input ; Info     ; Stuck at VCC              ;
+-------------------+-------+----------+---------------------------+


+------------------------------------------------------------------------------------------------------------------+
; Port Connectivity Checks: "pll165m:U0_pll165m_inst"                                                              ;
+--------+--------+----------+-------------------------------------------------------------------------------------+
; Port   ; Type   ; Severity ; Details                                                                             ;
+--------+--------+----------+-------------------------------------------------------------------------------------+
; c0     ; Output ; Info     ; Connected to dangling logic. Logic that only feeds a dangling port will be removed. ;
; locked ; Output ; Info     ; Connected to dangling logic. Logic that only feeds a dangling port will be removed. ;
+--------+--------+----------+-------------------------------------------------------------------------------------+


+-------------------------------+
; Elapsed Time Per Partition    ;
+----------------+--------------+
; Partition Name ; Elapsed Time ;
+----------------+--------------+
; Top            ; 00:00:00     ;
+----------------+--------------+


+-------------------------------+
; Analysis & Synthesis Messages ;
+-------------------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit Analysis & Synthesis
    Info: Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version
    Info: Processing started: Tue Jul 25 11:30:31 2023
Info: Command: quartus_map --read_settings_files=on --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC
Info: Parallel compilation is enabled and will use 6 of the 6 processors detected
Warning (10238): Verilog Module Declaration warning at PhaseAccumulator.V(17): ignored anonymous port(s) indicated by duplicate or dangling comma(s) in the port list for module "PhaseAccumulator"
Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/phaseaccumulator.v
    Info: Found entity 1: PhaseAccumulator
Warning (10275): Verilog HDL Module Instantiation warning at DAC904_WriteTEST.V(59): ignored dangling comma in List of Port Connections
Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/dac904_writetest.v
    Info: Found entity 1: DAC904_WriteTEST
Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/dac904_writemodule.v
    Info: Found entity 1: DAC904_WriteModule
Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/rom/rom_14b_sin/rom_14b_sin.v
    Info: Found entity 1: rom_14B_Sin
Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/pll/pll165m/pll165m.v
    Info: Found entity 1: pll165m
Warning (10236): Verilog HDL Implicit Net warning at DAC904_WriteTEST.V(18): created implicit net for "pll_65M"
Info: Elaborating entity "DAC904_WriteTEST" for the top level hierarchy
Info: Elaborating entity "pll165m" for hierarchy "pll165m:U0_pll165m_inst"
Info: Elaborating entity "altpll" for hierarchy "pll165m:U0_pll165m_inst|altpll:altpll_component"
Info: Elaborated megafunction instantiation "pll165m:U0_pll165m_inst|altpll:altpll_component"
Info: Instantiated megafunction "pll165m:U0_pll165m_inst|altpll:altpll_component" with the following parameter:
    Info: Parameter "bandwidth_type" = "AUTO"
    Info: Parameter "clk0_divide_by" = "25"
    Info: Parameter "clk0_duty_cycle" = "50"
    Info: Parameter "clk0_multiply_by" = "33"
    Info: Parameter "clk0_phase_shift" = "0"
    Info: Parameter "clk1_divide_by" = "10"
    Info: Parameter "clk1_duty_cycle" = "50"
    Info: Parameter "clk1_multiply_by" = "33"
    Info: Parameter "clk1_phase_shift" = "0"
    Info: Parameter "compensate_clock" = "CLK0"
    Info: Parameter "inclk0_input_frequency" = "20000"
    Info: Parameter "intended_device_family" = "Cyclone IV E"
    Info: Parameter "lpm_hint" = "CBX_MODULE_PREFIX=pll165m"
    Info: Parameter "lpm_type" = "altpll"
    Info: Parameter "operation_mode" = "NORMAL"
    Info: Parameter "pll_type" = "AUTO"
    Info: Parameter "port_activeclock" = "PORT_UNUSED"
    Info: Parameter "port_areset" = "PORT_USED"
    Info: Parameter "port_clkbad0" = "PORT_UNUSED"
    Info: Parameter "port_clkbad1" = "PORT_UNUSED"
    Info: Parameter "port_clkloss" = "PORT_UNUSED"
    Info: Parameter "port_clkswitch" = "PORT_UNUSED"
    Info: Parameter "port_configupdate" = "PORT_UNUSED"
    Info: Parameter "port_fbin" = "PORT_UNUSED"
    Info: Parameter "port_inclk0" = "PORT_USED"
    Info: Parameter "port_inclk1" = "PORT_UNUSED"
    Info: Parameter "port_locked" = "PORT_USED"
    Info: Parameter "port_pfdena" = "PORT_UNUSED"
    Info: Parameter "port_phasecounterselect" = "PORT_UNUSED"
    Info: Parameter "port_phasedone" = "PORT_UNUSED"
    Info: Parameter "port_phasestep" = "PORT_UNUSED"
    Info: Parameter "port_phaseupdown" = "PORT_UNUSED"
    Info: Parameter "port_pllena" = "PORT_UNUSED"
    Info: Parameter "port_scanaclr" = "PORT_UNUSED"
    Info: Parameter "port_scanclk" = "PORT_UNUSED"
    Info: Parameter "port_scanclkena" = "PORT_UNUSED"
    Info: Parameter "port_scandata" = "PORT_UNUSED"
    Info: Parameter "port_scandataout" = "PORT_UNUSED"
    Info: Parameter "port_scandone" = "PORT_UNUSED"
    Info: Parameter "port_scanread" = "PORT_UNUSED"
    Info: Parameter "port_scanwrite" = "PORT_UNUSED"
    Info: Parameter "port_clk0" = "PORT_USED"
    Info: Parameter "port_clk1" = "PORT_USED"
    Info: Parameter "port_clk2" = "PORT_UNUSED"
    Info: Parameter "port_clk3" = "PORT_UNUSED"
    Info: Parameter "port_clk4" = "PORT_UNUSED"
    Info: Parameter "port_clk5" = "PORT_UNUSED"
    Info: Parameter "port_clkena0" = "PORT_UNUSED"
    Info: Parameter "port_clkena1" = "PORT_UNUSED"
    Info: Parameter "port_clkena2" = "PORT_UNUSED"
    Info: Parameter "port_clkena3" = "PORT_UNUSED"
    Info: Parameter "port_clkena4" = "PORT_UNUSED"
    Info: Parameter "port_clkena5" = "PORT_UNUSED"
    Info: Parameter "port_extclk0" = "PORT_UNUSED"
    Info: Parameter "port_extclk1" = "PORT_UNUSED"
    Info: Parameter "port_extclk2" = "PORT_UNUSED"
    Info: Parameter "port_extclk3" = "PORT_UNUSED"
    Info: Parameter "self_reset_on_loss_lock" = "OFF"
    Info: Parameter "width_clock" = "5"
Info: Found 1 design units, including 1 entities, in source file db/pll165m_altpll.v
    Info: Found entity 1: pll165m_altpll
Info: Elaborating entity "pll165m_altpll" for hierarchy "pll165m:U0_pll165m_inst|altpll:altpll_component|pll165m_altpll:auto_generated"
Info: Elaborating entity "PhaseAccumulator" for hierarchy "PhaseAccumulator:U1_PhaseAccumulator"
Info: Elaborating entity "rom_14B_Sin" for hierarchy "rom_14B_Sin:U2_rom_14B_Sin"
Info: Elaborating entity "altsyncram" for hierarchy "rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component"
Info: Elaborated megafunction instantiation "rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component"
Info: Instantiated megafunction "rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component" with the following parameter:
    Info: Parameter "address_aclr_a" = "NONE"
    Info: Parameter "clock_enable_input_a" = "BYPASS"
    Info: Parameter "clock_enable_output_a" = "BYPASS"
    Info: Parameter "init_file" = "../../ROM/ROM_14B_SIN/ROM_14B_SIN.mif"
    Info: Parameter "intended_device_family" = "Cyclone IV E"
    Info: Parameter "lpm_hint" = "ENABLE_RUNTIME_MOD=NO"
    Info: Parameter "lpm_type" = "altsyncram"
    Info: Parameter "numwords_a" = "16384"
    Info: Parameter "operation_mode" = "ROM"
    Info: Parameter "outdata_aclr_a" = "NONE"
    Info: Parameter "outdata_reg_a" = "CLOCK0"
    Info: Parameter "widthad_a" = "14"
    Info: Parameter "width_a" = "14"
    Info: Parameter "width_byteena_a" = "1"
Info: Found 1 design units, including 1 entities, in source file db/altsyncram_vhb1.tdf
    Info: Found entity 1: altsyncram_vhb1
Info: Elaborating entity "altsyncram_vhb1" for hierarchy "rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated"
Info: Found 1 design units, including 1 entities, in source file db/decode_c8a.tdf
    Info: Found entity 1: decode_c8a
Info: Elaborating entity "decode_c8a" for hierarchy "rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|decode_c8a:rden_decode"
Info: Found 1 design units, including 1 entities, in source file db/mux_gob.tdf
    Info: Found entity 1: mux_gob
Info: Elaborating entity "mux_gob" for hierarchy "rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|mux_gob:mux2"
Info: Elaborating entity "DAC904_WriteModule" for hierarchy "DAC904_WriteModule:U3_DAC904_WriteModule"
Info: Timing-Driven Synthesis is running
Info: Generating hard_block partition "hard_block:auto_generated_inst"
    Info: Adding node "pll165m:U0_pll165m_inst|altpll:altpll_component|pll165m_altpll:auto_generated|pll1"
Warning: PLL "pll165m:U0_pll165m_inst|altpll:altpll_component|pll165m_altpll:auto_generated|pll1" has parameter compensate_clock set to clock0 but port CLK[0] is not connected
Warning: PLL "pll165m:U0_pll165m_inst|altpll:altpll_component|pll165m_altpll:auto_generated|pll1" has parameters clk0_multiply_by and clk0_divide_by specified but port CLK[0] is not connected
Info: Implemented 92 device resources after synthesis - the final resource count might be different
    Info: Implemented 2 input pins
    Info: Implemented 15 output pins
    Info: Implemented 46 logic cells
    Info: Implemented 28 RAM segments
    Info: Implemented 1 PLLs
Info: Quartus II 64-Bit Analysis & Synthesis was successful. 0 errors, 5 warnings
    Info: Peak virtual memory: 4498 megabytes
    Info: Processing ended: Tue Jul 25 11:30:33 2023
    Info: Elapsed time: 00:00:02
    Info: Total CPU time (on all processors): 00:00:01


