 -- Copyright (C) 1991-2011 Altera Corporation
 -- Your use of Altera Corporation's design tools, logic functions 
 -- and other software and tools, and its AMPP partner logic 
 -- functions, and any output files from any of the foregoing 
 -- (including device programming or simulation files), and any 
 -- associated documentation or information are expressly subject 
 -- to the terms and conditions of the Altera Program License 
 -- Subscription Agreement, Altera MegaCore Function License 
 -- Agreement, or other applicable license agreement, including, 
 -- without limitation, that your use is for the sole purpose of 
 -- programming logic devices manufactured by Altera and sold by 
 -- Altera or its authorized distributors.  Please refer to the 
 -- applicable agreement for further details.
 -- 
 -- This is a Quartus II output file. It is for reporting purposes only, and is
 -- not intended for use as a Quartus II input file. This file cannot be used
 -- to make Quartus II pin assignments - for instructions on how to make pin
 -- assignments, please see Quartus II help.
 ---------------------------------------------------------------------------------



 ---------------------------------------------------------------------------------
 -- NC            : No Connect. This pin has no internal connection to the device.
 -- DNU           : Do Not Use. This pin MUST NOT be connected.
 -- VCCINT        : Dedicated power pin, which MUST be connected to VCC  (1.2V).
 -- VCCIO         : Dedicated power pin, which MUST be connected to VCC
 --                 of its bank.
 --					Bank 1:		2.5V
 --					Bank 2:		2.5V
 --					Bank 3:		2.5V
 --					Bank 4:		2.5V
 --					Bank 5:		2.5V
 --					Bank 6:		2.5V
 --					Bank 7:		2.5V
 --					Bank 8:		2.5V
 -- GND           : Dedicated ground pin. Dedicated GND pins MUST be connected to GND.
 --					It can also be used to report unused dedicated pins. The connection
 --					on the board for unused dedicated pins depends on whether this will
 --					be used in a future design. One example is device migration. When
 --					using device migration, refer to the device pin-tables. If it is a
 --					GND pin in the pin table or if it will not be used in a future design
 --					for another purpose the it MUST be connected to GND. If it is an unused
 --					dedicated pin, then it can be connected to a valid signal on the board
 --					(low, high, or toggling) if that signal is required for a different
 --					revision of the design.
 -- GND+          : Unused input pin. It can also be used to report unused dual-purpose pins.
 --					This pin should be connected to GND. It may also be connected  to a
 --					valid signal  on the board  (low, high, or toggling)  if that signal
 --					is required for a different revision of the design.
 -- GND*          : Unused  I/O  pin. Connect each pin marked GND* directly to GND
 --           	    or leave it unconnected.
 -- RESERVED      : Unused I/O pin, which MUST be left unconnected.
 -- RESERVED_INPUT    : Pin is tri-stated and should be connected to the board.
 -- RESERVED_INPUT_WITH_WEAK_PULLUP    : Pin is tri-stated with internal weak pull-up resistor.
 -- RESERVED_INPUT_WITH_BUS_HOLD       : Pin is tri-stated with bus-hold circuitry.
 -- RESERVED_OUTPUT_DRIVEN_HIGH        : Pin is output driven high.
 ---------------------------------------------------------------------------------



 ---------------------------------------------------------------------------------
 -- Pin directions (input, output or bidir) are based on device operating in user mode.
 ---------------------------------------------------------------------------------

Quartus II 64-Bit Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version
CHIP  "DAC904_HighSpeedDAC"  ASSIGNED TO AN: EP4CE15F17C8

Pin Name/Usage               : Location  : Dir.   : I/O Standard      : Voltage : I/O Bank  : User Assignment
-------------------------------------------------------------------------------------------------------------
VCCIO8                       : A1        : power  :                   : 2.5V    : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A2        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A3        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A4        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A5        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A6        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A7        :        :                   :         : 8         :                
GND+                         : A8        :        :                   :         : 8         :                
GND+                         : A9        :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A10       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A11       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A12       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A13       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A14       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A15       :        :                   :         : 7         :                
VCCIO7                       : A16       : power  :                   : 2.5V    : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B1        :        :                   :         : 1         :                
GND                          : B2        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B3        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B4        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B5        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B6        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B7        :        :                   :         : 8         :                
GND+                         : B8        :        :                   :         : 8         :                
GND+                         : B9        :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B10       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B11       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B12       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B13       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B14       :        :                   :         : 7         :                
GND                          : B15       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B16       :        :                   :         : 6         :                
~ALTERA_ASDO_DATA1~ / RESERVED_INPUT_WITH_WEAK_PULLUP : C1        : input  : 2.5 V             :         : 1         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : C2        :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C3        :        :                   :         : 8         :                
VCCIO8                       : C4        : power  :                   : 2.5V    : 8         :                
GND                          : C5        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C6        :        :                   :         : 8         :                
VCCIO8                       : C7        : power  :                   : 2.5V    : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C8        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C9        :        :                   :         : 7         :                
VCCIO7                       : C10       : power  :                   : 2.5V    : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C11       :        :                   :         : 7         :                
GND                          : C12       : gnd    :                   :         :           :                
VCCIO7                       : C13       : power  :                   : 2.5V    : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C14       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C15       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C16       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D1        :        :                   :         : 1         :                
~ALTERA_FLASH_nCE_nCSO~ / RESERVED_INPUT_WITH_WEAK_PULLUP : D2        : input  : 2.5 V             :         : 1         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : D3        :        :                   :         : 8         :                
VCCD_PLL3                    : D4        : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D5        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D6        :        :                   :         : 8         :                
GND                          : D7        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D8        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D9        :        :                   :         : 7         :                
GND                          : D10       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D11       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D12       :        :                   :         : 7         :                
VCCD_PLL2                    : D13       : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D14       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D15       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D16       :        :                   :         : 6         :                
GND+                         : E1        :        :                   :         : 1         :                
GND                          : E2        : gnd    :                   :         :           :                
VCCIO1                       : E3        : power  :                   : 2.5V    : 1         :                
GND                          : E4        : gnd    :                   :         :           :                
GNDA3                        : E5        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : E6        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : E7        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : E8        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : E9        :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : E10       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : E11       :        :                   :         : 7         :                
GNDA2                        : E12       : gnd    :                   :         :           :                
GND                          : E13       : gnd    :                   :         :           :                
VCCIO6                       : E14       : power  :                   : 2.5V    : 6         :                
GND+                         : E15       :        :                   :         : 6         :                
GND+                         : E16       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : F1        :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : F2        :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : F3        :        :                   :         : 1         :                
nSTATUS                      : F4        :        :                   :         : 1         :                
VCCA3                        : F5        : power  :                   : 2.5V    :           :                
GND                          : F6        : gnd    :                   :         :           :                
VCCINT                       : F7        : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : F8        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : F9        :        :                   :         : 7         :                
GND                          : F10       : gnd    :                   :         :           :                
VCCINT                       : F11       : power  :                   : 1.2V    :           :                
VCCA2                        : F12       : power  :                   : 2.5V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : F13       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : F14       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : F15       :        :                   :         : 6         :                
~ALTERA_nCEO~ / RESERVED_OUTPUT_OPEN_DRAIN : F16       : output : 2.5 V             :         : 6         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : G1        :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : G2        :        :                   :         : 1         :                
VCCIO1                       : G3        : power  :                   : 2.5V    : 1         :                
GND                          : G4        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : G5        :        :                   :         : 1         :                
VCCINT                       : G6        : power  :                   : 1.2V    :           :                
VCCINT                       : G7        : power  :                   : 1.2V    :           :                
VCCINT                       : G8        : power  :                   : 1.2V    :           :                
VCCINT                       : G9        : power  :                   : 1.2V    :           :                
VCCINT                       : G10       : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : G11       :        :                   :         : 6         :                
MSEL2                        : G12       :        :                   :         : 6         :                
GND                          : G13       : gnd    :                   :         :           :                
VCCIO6                       : G14       : power  :                   : 2.5V    : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : G15       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : G16       :        :                   :         : 6         :                
~ALTERA_DCLK~                : H1        : output : 2.5 V             :         : 1         : N              
~ALTERA_DATA0~ / RESERVED_INPUT_WITH_WEAK_PULLUP : H2        : input  : 2.5 V             :         : 1         : N              
TCK                          : H3        : input  :                   :         : 1         :                
TDI                          : H4        : input  :                   :         : 1         :                
nCONFIG                      : H5        :        :                   :         : 1         :                
VCCINT                       : H6        : power  :                   : 1.2V    :           :                
GND                          : H7        : gnd    :                   :         :           :                
GND                          : H8        : gnd    :                   :         :           :                
GND                          : H9        : gnd    :                   :         :           :                
GND                          : H10       : gnd    :                   :         :           :                
VCCINT                       : H11       : power  :                   : 1.2V    :           :                
MSEL1                        : H12       :        :                   :         : 6         :                
MSEL0                        : H13       :        :                   :         : 6         :                
CONF_DONE                    : H14       :        :                   :         : 6         :                
GND                          : H15       : gnd    :                   :         :           :                
GND                          : H16       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : J1        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : J2        :        :                   :         : 2         :                
nCE                          : J3        :        :                   :         : 1         :                
TDO                          : J4        : output :                   :         : 1         :                
TMS                          : J5        : input  :                   :         : 1         :                
VCCINT                       : J6        : power  :                   : 1.2V    :           :                
GND                          : J7        : gnd    :                   :         :           :                
GND                          : J8        : gnd    :                   :         :           :                
GND                          : J9        : gnd    :                   :         :           :                
GND                          : J10       : gnd    :                   :         :           :                
GND                          : J11       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : J12       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : J13       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : J14       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : J15       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : J16       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K1        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K2        :        :                   :         : 2         :                
VCCIO2                       : K3        : power  :                   : 2.5V    : 2         :                
GND                          : K4        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K5        :        :                   :         : 2         :                
IO_data[0]                   : K6        : output : 2.5 V             :         : 2         : Y              
VCCINT                       : K7        : power  :                   : 1.2V    :           :                
GND                          : K8        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K9        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K10       :        :                   :         : 4         :                
VCCINT                       : K11       : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K12       :        :                   :         : 5         :                
GND                          : K13       : gnd    :                   :         :           :                
VCCIO5                       : K14       : power  :                   : 2.5V    : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K15       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K16       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L1        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L2        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L3        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L4        :        :                   :         : 2         :                
VCCA1                        : L5        : power  :                   : 2.5V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L6        :        :                   :         : 2         :                
IO_data[9]                   : L7        : output : 2.5 V             :         : 3         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : L8        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L9        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L10       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L11       :        :                   :         : 4         :                
VCCA4                        : L12       : power  :                   : 2.5V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L13       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L14       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L15       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L16       :        :                   :         : 5         :                
rstn                         : M1        : input  : 2.5 V             :         : 2         : Y              
GND+                         : M2        :        :                   :         : 2         :                
VCCIO2                       : M3        : power  :                   : 2.5V    : 2         :                
GND                          : M4        : gnd    :                   :         :           :                
GNDA1                        : M5        : gnd    :                   :         :           :                
IO_data[4]                   : M6        : output : 2.5 V             :         : 3         : Y              
IO_data[5]                   : M7        : output : 2.5 V             :         : 3         : Y              
IO_data[8]                   : M8        : output : 2.5 V             :         : 3         : Y              
IO_data[12]                  : M9        : output : 2.5 V             :         : 4         : Y              
IO_data[13]                  : M10       : output : 2.5 V             :         : 4         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : M11       :        :                   :         : 4         :                
GNDA4                        : M12       : gnd    :                   :         :           :                
GND                          : M13       : gnd    :                   :         :           :                
VCCIO5                       : M14       : power  :                   : 2.5V    : 5         :                
GND+                         : M15       :        :                   :         : 5         :                
GND+                         : M16       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N1        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N2        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N3        :        :                   :         : 3         :                
VCCD_PLL1                    : N4        : power  :                   : 1.2V    :           :                
IO_data[1]                   : N5        : output : 2.5 V             :         : 3         : Y              
IO_data[2]                   : N6        : output : 2.5 V             :         : 3         : Y              
GND                          : N7        : gnd    :                   :         :           :                
IO_data[7]                   : N8        : output : 2.5 V             :         : 3         : Y              
IO_data[11]                  : N9        : output : 2.5 V             :         : 4         : Y              
GND                          : N10       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N11       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N12       :        :                   :         : 4         :                
VCCD_PLL4                    : N13       : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N14       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N15       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N16       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : P1        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : P2        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : P3        :        :                   :         : 3         :                
VCCIO3                       : P4        : power  :                   : 2.5V    : 3         :                
GND                          : P5        : gnd    :                   :         :           :                
IO_data[3]                   : P6        : output : 2.5 V             :         : 3         : Y              
VCCIO3                       : P7        : power  :                   : 2.5V    : 3         :                
IO_data[6]                   : P8        : output : 2.5 V             :         : 3         : Y              
IO_data[10]                  : P9        : output : 2.5 V             :         : 4         : Y              
VCCIO4                       : P10       : power  :                   : 2.5V    : 4         :                
clk_driver                   : P11       : output : 2.5 V             :         : 4         : Y              
GND                          : P12       : gnd    :                   :         :           :                
VCCIO4                       : P13       : power  :                   : 2.5V    : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : P14       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : P15       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : P16       :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R1        :        :                   :         : 2         :                
GND                          : R2        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R3        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R4        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R5        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R6        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R7        :        :                   :         : 3         :                
GND+                         : R8        :        :                   :         : 3         :                
clk                          : R9        : input  : 2.5 V             :         : 4         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : R10       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R11       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R12       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R13       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R14       :        :                   :         : 4         :                
GND                          : R15       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R16       :        :                   :         : 5         :                
VCCIO3                       : T1        : power  :                   : 2.5V    : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T2        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T3        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T4        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T5        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T6        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T7        :        :                   :         : 3         :                
GND+                         : T8        :        :                   :         : 3         :                
GND+                         : T9        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T10       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T11       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T12       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T13       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T14       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T15       :        :                   :         : 4         :                
VCCIO4                       : T16       : power  :                   : 2.5V    : 4         :                
