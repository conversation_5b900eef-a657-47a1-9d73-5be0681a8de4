{ "Info" "IQEXE_SEPARATOR" "" "Info: *******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Assembler Quartus II 64-Bit " "Info: Running Quartus II 64-Bit Assembler" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version " "Info: Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_START_BANNER_TIME" "Tue Jul 25 11:30:37 2023 " "Info: Processing started: Tue Jul 25 11:30:37 2023" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "" 0 -1}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_asm --read_settings_files=off --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC " "Info: Command: quartus_asm --read_settings_files=off --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC" {  } {  } 0 0 "Command: %1!s!" 0 0 "" 0 -1}
{ "Info" "IASM_ASM_GENERATING_POWER_DATA" "" "Info: Writing out detailed assembly data for power analysis" {  } {  } 0 0 "Writing out detailed assembly data for power analysis" 0 0 "" 0 -1}
{ "Info" "IASM_ASM_GENERATING_PROGRAMMING_FILES" "" "Info: Assembler is generating device programming files" {  } {  } 0 0 "Assembler is generating device programming files" 0 0 "" 0 -1}
{ "Info" "IQEXE_ERROR_COUNT" "Assembler 0 s 0 s Quartus II 64-Bit " "Info: Quartus II 64-Bit Assembler was successful. 0 errors, 0 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4472 " "Info: Peak virtual memory: 4472 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "" 0 -1} { "Info" "IQEXE_END_BANNER_TIME" "Tue Jul 25 11:30:38 2023 " "Info: Processing ended: Tue Jul 25 11:30:38 2023" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Info: Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Info: Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "" 0 -1}
