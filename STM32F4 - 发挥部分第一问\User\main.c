/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024-08-02
  * @brief   STM32F4控制DAC904产生信号 - 简化版
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
#include "bsp.h"

// DAC904高性能模块
#include "../Modules/Generation/dac904.h"
#include "../Modules/Generation/dds_dac904.h"
#include "../Modules/Generation/wave_tables_14bit.h"

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// DAC904 DDS配置变量
static uint32_t current_frequency = 1000;      // 当前频率 (默认1kHz)
static float current_amplitude = 2.5f;         // 当前峰峰值 (默认2.5V)
static Wave_Type_14bit_t current_wave_type = WAVE_TYPE_SINE;  // 当前波形 (默认正弦波)

// DAC904技术优势：
// - 14位分辨率，165MHz最大采样率
// - 32位相位累加器，高精度频率控制
// - 并行数据接口，超高速数据传输
// - 1Hz-100kHz频率范围，适合音频和射频应用
// - 支持多种波形：正弦波、方波、三角波、锯齿波

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void DAC904_DDS_Init(void);
void DAC904_DDS_Demo(void);
void DAC904_Test_Frequencies(void);
void DAC904_Test_Amplitudes(void);
void DAC904_Test_Waveforms(void);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();

    /* ==================== DAC904 DDS系统初始化 ==================== */
    DAC904_DDS_Init();

    /* ==================== 简单测试：输出固定电压 ==================== */
    // 先测试基本功能：输出2.5V直流电压
    Delay_ms(1000);
    DDS_DAC904_Stop();  // 停止DDS，输出固定值

    // 直接设置DAC输出2.5V (中间值)
    DAC904_WriteData(8192);  // 8192 = 2.5V (14位中间值)
    Delay_ms(2000);

    // 测试不同电压
    DAC904_WriteData(0);      // 0V
    Delay_ms(1000);
    DAC904_WriteData(16383);  // 5V
    Delay_ms(1000);
    DAC904_WriteData(8192);   // 2.5V
    Delay_ms(1000);

    /* ==================== DAC904功能演示 ==================== */
    DAC904_DDS_Demo();

    /* 主循环 - DAC904持续运行 */
    uint32_t demo_counter = 0;

    while (1)
    {
        demo_counter++;

        // ==================== 定期演示不同配置 ====================
        // 每30秒切换一次演示内容
        if (demo_counter % 30000000 == 0) {
            static uint8_t demo_mode = 0;

            switch (demo_mode) {
                case 0:
                    DAC904_Test_Frequencies();  // 测试不同频率
                    break;
                case 1:
                    DAC904_Test_Amplitudes();   // 测试不同幅度
                    break;
                case 2:
                    DAC904_Test_Waveforms();    // 测试不同波形
                    break;
                default:
                    demo_mode = 0;
                    continue;
            }

            demo_mode = (demo_mode + 1) % 3;
        }

        // ==================== 低功耗模式 ====================
        // 短暂延时，让DAC904继续工作
        for (volatile int i = 0; i < 1000; i++) {
            __NOP();
        }
    }
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  DAC904 DDS系统初始化
  * @param  None
  * @retval None
  */
void DAC904_DDS_Init(void)
{
    // 配置DAC904 DDS参数
    DDS_DAC904_Config_t dds_config = {
        .frequency = current_frequency,         // 1kHz
        .amplitude = current_amplitude,         // 2.5V峰峰值
        .offset = 2.5f,                        // 2.5V直流偏移 (单极性输出)
        .wave_type = current_wave_type,        // 正弦波
        .sample_rate = DDS_DAC904_SAMPLE_RATE_22K3,  // 22.3kHz采样率 (兼容现有项目)
        .enable_continuous = true,             // 连续输出
        .enable_phase_sync = false             // 不需要相位同步
    };

    // 初始化DDS DAC904
    DDS_DAC904_Status_t status = DDS_DAC904_Init(&dds_config);
    if (status != DDS_DAC904_OK) {
        // 初始化失败，进入死循环
        while (1) {
            // 可以在这里添加错误指示
        }
    }

    // 立即启动DDS输出
    status = DDS_DAC904_Start();
    if (status != DDS_DAC904_OK) {
        // 启动失败，进入死循环
        while (1) {
            // 可以在这里添加错误指示
        }
    }
}

/**
  * @brief  DAC904功能演示
  * @param  None
  * @retval None
  */
void DAC904_DDS_Demo(void)
{
    // 启动DDS输出
    DDS_DAC904_Start();

    // 延时让信号稳定
    Delay_ms(1000);

    // 演示频率调整：1kHz -> 5kHz -> 10kHz -> 1kHz
    DDS_DAC904_SetFrequency(5000);   // 5kHz
    Delay_ms(2000);

    DDS_DAC904_SetFrequency(10000);  // 10kHz
    Delay_ms(2000);

    DDS_DAC904_SetFrequency(1000);   // 回到1kHz
    Delay_ms(1000);

    // 演示幅度调整：2.5V -> 1.0V -> 4.0V -> 2.5V
    DDS_DAC904_SetAmplitude(1.0f);   // 1.0V峰峰值
    Delay_ms(2000);

    DDS_DAC904_SetAmplitude(4.0f);   // 4.0V峰峰值
    Delay_ms(2000);

    DDS_DAC904_SetAmplitude(2.5f);   // 回到2.5V
    Delay_ms(1000);
}

/**
  * @brief  测试不同频率
  * @param  None
  * @retval None
  */
void DAC904_Test_Frequencies(void)
{
    // 测试频率序列：100Hz, 500Hz, 1kHz, 2kHz, 5kHz, 10kHz
    uint32_t test_frequencies[] = {100, 500, 1000, 2000, 5000, 10000};
    uint8_t freq_count = sizeof(test_frequencies) / sizeof(test_frequencies[0]);

    for (uint8_t i = 0; i < freq_count; i++) {
        DDS_DAC904_SetFrequency(test_frequencies[i]);
        current_frequency = test_frequencies[i];
        Delay_ms(3000);  // 每个频率持续3秒
    }

    // 回到默认频率
    DDS_DAC904_SetFrequency(1000);
    current_frequency = 1000;
}

/**
  * @brief  测试不同幅度
  * @param  None
  * @retval None
  */
void DAC904_Test_Amplitudes(void)
{
    // 测试幅度序列：0.5V, 1.0V, 2.0V, 3.0V, 4.0V, 5.0V
    float test_amplitudes[] = {0.5f, 1.0f, 2.0f, 3.0f, 4.0f, 5.0f};
    uint8_t amp_count = sizeof(test_amplitudes) / sizeof(test_amplitudes[0]);

    for (uint8_t i = 0; i < amp_count; i++) {
        DDS_DAC904_SetAmplitude(test_amplitudes[i]);
        current_amplitude = test_amplitudes[i];
        Delay_ms(2000);  // 每个幅度持续2秒
    }

    // 回到默认幅度
    DDS_DAC904_SetAmplitude(2.5f);
    current_amplitude = 2.5f;
}

/**
  * @brief  测试不同波形
  * @param  None
  * @retval None
  */
void DAC904_Test_Waveforms(void)
{
    // 测试波形序列：正弦波、方波、三角波、锯齿波
    Wave_Type_14bit_t test_waveforms[] = {
        WAVE_TYPE_SINE,
        WAVE_TYPE_SQUARE,
        WAVE_TYPE_TRIANGLE,
        WAVE_TYPE_SAWTOOTH
    };
    uint8_t wave_count = sizeof(test_waveforms) / sizeof(test_waveforms[0]);

    for (uint8_t i = 0; i < wave_count; i++) {
        DDS_DAC904_SetWaveType(test_waveforms[i]);
        current_wave_type = test_waveforms[i];
        Delay_ms(4000);  // 每个波形持续4秒
    }

    // 回到默认波形
    DDS_DAC904_SetWaveType(WAVE_TYPE_SINE);
    current_wave_type = WAVE_TYPE_SINE;
}










/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


