#include "bsp.h"

/**
 * @brief  板级支持包初始化函数
 * @param  None
 * @retval None
 * @note   完成系统时钟和基础外设的初始化
 */
void BSP_Init(void)
{
    // 1. 使能所有GPIO端口时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);

    // 2. 使能SPI外设时钟 (为DAC904模块预留)
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_SPI1, ENABLE);  // AD7606 (保留)
    // RCC_APB1PeriphClockCmd(RCC_APB1Periph_SPI2, ENABLE);  // 原DAC8552，已清除，为DAC904预留
}

// SysTick_Handler函数已在stm32f4xx_it.c中定义，此处不重复定义
