{ "Info" "IQEXE_SEPARATOR" "" "Info: *******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Fitter Quartus II 64-Bit " "Info: Running Quartus II 64-Bit Fitter" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version " "Info: Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_START_BANNER_TIME" "Tue Jul 25 11:30:33 2023 " "Info: Processing started: Tue Jul 25 11:30:33 2023" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "" 0 -1}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_fit --read_settings_files=off --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC " "Info: Command: quartus_fit --read_settings_files=off --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC" {  } {  } 0 0 "Command: %1!s!" 0 0 "" 0 -1}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "6 6 " "Info: Parallel compilation is enabled and will use 6 of the 6 processors detected" {  } {  } 0 0 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "" 0 -1}
{ "Info" "IMPP_MPP_USER_DEVICE" "DAC904_HighSpeedDAC EP4CE15F17C8 " "Info: Selected device EP4CE15F17C8 for design \"DAC904_HighSpeedDAC\"" {  } {  } 0 0 "Selected device %2!s! for design \"%1!s!\"" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Info: Low junction temperature is 0 degrees C" {  } {  } 0 0 "%1!s! is %2!s!" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "Info: High junction temperature is 85 degrees C" {  } {  } 0 0 "%1!s! is %2!s!" 0 0 "" 0 -1}
{ "Warning" "WCUT_CUT_YGR_PLL_SET_COMPENSATE_CLK" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 clock1 " "Warning: Compensate clock of PLL \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\" has been set to clock1" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 92 -1 0 } }  } 0 0 "Compensate clock of PLL \"%1!s!\" has been set to %2!s!" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_PLL_COMPUTATION_SUCCESS" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 Cyclone IV E PLL " "Info: Implemented PLL \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\" as Cyclone IV E PLL type" { { "Info" "ICUT_CUT_YGR_PLL_PARAMETERS_FACTORS" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|wire_pll1_clk\[1\] 33 10 0 0 " "Info: Implementing clock multiplication of 33, clock division of 10, and phase shift of 0 degrees (0 ps) for pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|wire_pll1_clk\[1\] port" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 50 -1 0 } }  } 0 0 "Implementing clock multiplication of %2!d!, clock division of %3!d!, and phase shift of %4!d! degrees (%5!d! ps) for %1!s! port" 0 0 "" 0 -1}  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 92 -1 0 } }  } 0 0 "Implemented %3!s! \"%1!s!\" as %2!s! PLL type" 0 0 "" 0 -1}
{ "Info" "IFITCC_FITCC_INFO_AUTO_FIT_COMPILATION_ON" "" "Info: Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" {  } {  } 0 0 "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" 0 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED" "" "Info: Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" { { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE10F17C8 " "Info: Device EP4CE10F17C8 is compatible" {  } {  } 2 0 "Device %1!s! is compatible" 0 0 "" 0 -1} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE6F17C8 " "Info: Device EP4CE6F17C8 is compatible" {  } {  } 2 0 "Device %1!s! is compatible" 0 0 "" 0 -1} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE22F17C8 " "Info: Device EP4CE22F17C8 is compatible" {  } {  } 2 0 "Device %1!s! is compatible" 0 0 "" 0 -1}  } {  } 2 0 "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" 0 0 "" 0 -1}
{ "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION" "5 " "Info: Fitter converted 5 user pins into dedicated programming pins" { { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_ASDO_DATA1~ C1 " "Info: Pin ~ALTERA_ASDO_DATA1~ is reserved at location C1" {  } { { "c:/altera/11.0/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/altera/11.0/quartus/bin64/pin_planner.ppl" { ~ALTERA_ASDO_DATA1~ } } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_ASDO_DATA1~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 698 5593 6598 0}  }  } }  } 0 0 "Pin %1!s! is reserved at location %2!s!" 0 0 "" 0 -1} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_FLASH_nCE_nCSO~ D2 " "Info: Pin ~ALTERA_FLASH_nCE_nCSO~ is reserved at location D2" {  } { { "c:/altera/11.0/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/altera/11.0/quartus/bin64/pin_planner.ppl" { ~ALTERA_FLASH_nCE_nCSO~ } } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_FLASH_nCE_nCSO~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 700 5593 6598 0}  }  } }  } 0 0 "Pin %1!s! is reserved at location %2!s!" 0 0 "" 0 -1} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DCLK~ H1 " "Info: Pin ~ALTERA_DCLK~ is reserved at location H1" {  } { { "c:/altera/11.0/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/altera/11.0/quartus/bin64/pin_planner.ppl" { ~ALTERA_DCLK~ } } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_DCLK~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 702 5593 6598 0}  }  } }  } 0 0 "Pin %1!s! is reserved at location %2!s!" 0 0 "" 0 -1} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DATA0~ H2 " "Info: Pin ~ALTERA_DATA0~ is reserved at location H2" {  } { { "c:/altera/11.0/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/altera/11.0/quartus/bin64/pin_planner.ppl" { ~ALTERA_DATA0~ } } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_DATA0~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 704 5593 6598 0}  }  } }  } 0 0 "Pin %1!s! is reserved at location %2!s!" 0 0 "" 0 -1} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_nCEO~ F16 " "Info: Pin ~ALTERA_nCEO~ is reserved at location F16" {  } { { "c:/altera/11.0/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/altera/11.0/quartus/bin64/pin_planner.ppl" { ~ALTERA_nCEO~ } } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_nCEO~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 706 5593 6598 0}  }  } }  } 0 0 "Pin %1!s! is reserved at location %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Fitter converted %1!d! user pins into dedicated programming pins" 0 0 "" 0 -1}
{ "Warning" "WCUT_CUT_ATOM_PINS_WITH_INCOMPLETE_IO_ASSIGNMENTS" "" "Warning: Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" {  } {  } 0 0 "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" 0 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_RAM_METASTABILITY_INFO" "" "Info: Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." {  } {  } 0 0 "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." 0 0 "" 0 -1}
{ "Info" "ITDC_FITTER_TIMING_ENGINE" "TimeQuest " "Info: Timing-driven compilation is using the TimeQuest Timing Analyzer" {  } {  } 0 0 "Timing-driven compilation is using the %1!s! Timing Analyzer" 0 0 "" 0 -1}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "DAC904_HighSpeedDAC.sdc " "Critical Warning: Synopsys Design Constraints File file not found: 'DAC904_HighSpeedDAC.sdc'. A Synopsys Design Constraints File is required by the TimeQuest Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 0 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the TimeQuest Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_FOUND_NO_DERIVING_MSG" "generated clocks " "Info: No user constrained generated clocks found in the design" {  } {  } 0 0 "No user constrained %1!s! found in the design" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_FOUND_NO_DERIVING_MSG" "base clocks " "Info: No user constrained base clocks found in the design" {  } {  } 0 0 "No user constrained %1!s! found in the design" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_CLOCKS_FOUND_NO_CLOCKS" "" "Info: The command derive_clocks did not find any clocks to derive.  No clocks were created or changed." {  } {  } 0 0 "The command derive_clocks did not find any clocks to derive.  No clocks were created or changed." 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "Info: No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 0 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_UNCERTAINTY_FOUND" "" "Info: The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." {  } {  } 0 0 "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." 0 0 "" 0 -1}
{ "Info" "ISTA_TDC_NO_DEFAULT_OPTIMIZATION_GOALS" "" "Info: Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." {  } {  } 0 0 "Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." 0 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|wire_pll1_clk\[1\] (placed in counter C0 of PLL_4) " "Info: Automatically promoted node pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|wire_pll1_clk\[1\] (placed in counter C0 of PLL_4)" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G18 " "Info: Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G18" {  } {  } 0 0 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "" 0 -1}  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 92 -1 0 } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { pll165m:U0_pll165m_inst|altpll:altpll_component|pll165m_altpll:auto_generated|wire_pll1_clk[0] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 132 5593 6598 0}  }  } }  } 0 0 "Automatically promoted node %1!s! %2!s!" 0 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "rstn~input (placed in PIN M1 (CLK3, DIFFCLK_1n)) " "Info: Automatically promoted node rstn~input (placed in PIN M1 (CLK3, DIFFCLK_1n))" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G3 " "Info: Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G3" {  } {  } 0 0 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "" 0 -1}  } { { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 3 0 0 } } { "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "c:/altera/11.0/quartus/bin64/TimingClosureFloorplan.fld" "" "" { rstn~input } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/" { { 0 { 0 ""} 0 688 5593 6598 0}  }  } }  } 0 0 "Automatically promoted node %1!s! %2!s!" 0 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_START_REGPACKING_INFO" "" "Info: Starting register packing" {  } {  } 0 0 "Starting register packing" 0 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_START_REG_LOCATION_PROCESSING" "" "Extra Info: Performing register packing on registers with non-logic cell location assignments" {  } {  } 1 0 "Performing register packing on registers with non-logic cell location assignments" 1 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_FINISH_REG_LOCATION_PROCESSING" "" "Extra Info: Completed register packing on registers with non-logic cell location assignments" {  } {  } 1 0 "Completed register packing on registers with non-logic cell location assignments" 1 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_BEGIN_FAST_REGISTER_INFO" "" "Extra Info: Started Fast Input/Output/OE register processing" {  } {  } 1 0 "Started Fast Input/Output/OE register processing" 1 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_FAST_REGISTER_INFO" "" "Extra Info: Finished Fast Input/Output/OE register processing" {  } {  } 1 0 "Finished Fast Input/Output/OE register processing" 1 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_START_IO_MULT_RAM_PACKING" "" "Extra Info: Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" {  } {  } 1 0 "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" 1 0 "" 0 -1}
{ "Extra Info" "IFSAC_FSAC_FINISH_IO_MULT_RAM_PACKING" "" "Extra Info: Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" {  } {  } 1 0 "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" 1 0 "" 0 -1}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_REGPACKING_INFO" "" "Info: Finished register packing" { { "Extra Info" "IFSAC_NO_REGISTERS_WERE_PACKED" "" "Extra Info: No registers were packed into other blocks" {  } {  } 1 0 "No registers were packed into other blocks" 0 0 "" 0 -1}  } {  } 0 0 "Finished register packing" 0 0 "" 0 -1}
{ "Warning" "WCUT_PLL_CLK_FEEDS_NON_DEDICATED_IO" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 clk\[1\] clk_driver~output " "Warning: PLL \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\" output port clk\[1\] feeds output pin \"clk_driver~output\" via non-dedicated routing -- jitter performance depends on switching rate of other design elements. Use PLL dedicated clock outputs to ensure jitter performance" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 50 -1 0 } } { "altpll.tdf" "" { Text "c:/altera/11.0/quartus/libraries/megafunctions/altpll.tdf" 897 0 0 } } { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } } { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 21 0 0 } } { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 5 0 0 } }  } 0 0 "PLL \"%1!s!\" output port %2!s! feeds output pin \"%3!s!\" via non-dedicated routing -- jitter performance depends on switching rate of other design elements. Use PLL dedicated clock outputs to ensure jitter performance" 0 0 "" 0 -1}
{ "Warning" "WCUT_CUT_UNATTACHED_ASGN" "" "Warning: Ignored locations or region assignments to the following nodes" { { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyAF " "Warning: Node \"keyAF\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyAF" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1} { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyIn\[0\] " "Warning: Node \"keyIn\[0\]\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyIn\[0\]" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1} { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyIn\[1\] " "Warning: Node \"keyIn\[1\]\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyIn\[1\]" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1} { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyIn\[2\] " "Warning: Node \"keyIn\[2\]\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyIn\[2\]" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1} { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyIn\[3\] " "Warning: Node \"keyIn\[3\]\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyIn\[3\]" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1} { "Warning" "WCUT_CUT_UNATTACHED_ASGN_SUB" "keyIn\[4\] " "Warning: Node \"keyIn\[4\]\" is assigned to location or region, but does not exist in design" {  } { { "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" "" { Assignment "c:/altera/11.0/quartus/bin64/Assignment Editor.qase" 1 { { 0 "keyIn\[4\]" } } } }  } 0 0 "Node \"%1!s!\" is assigned to location or region, but does not exist in design" 0 0 "" 0 -1}  } {  } 0 0 "Ignored locations or region assignments to the following nodes" 0 0 "" 0 -1}
{ "Info" "IFITCC_FITTER_PREPARATION_END" "00:00:00 " "Info: Fitter preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 0 "Fitter preparation operations ending: elapsed time is %1!s!" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_START" "" "Info: Fitter placement preparation operations beginning" {  } {  } 0 0 "Fitter placement preparation operations beginning" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_END" "00:00:00 " "Info: Fitter placement preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 0 "Fitter placement preparation operations ending: elapsed time is %1!s!" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_START" "" "Info: Fitter placement operations beginning" {  } {  } 0 0 "Fitter placement operations beginning" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_INFO_VPR_PLACEMENT_FINISH" "" "Info: Fitter placement was successful" {  } {  } 0 0 "Fitter placement was successful" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_END" "00:00:00 " "Info: Fitter placement operations ending: elapsed time is 00:00:00" {  } {  } 0 0 "Fitter placement operations ending: elapsed time is %1!s!" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_START" "" "Info: Fitter routing operations beginning" {  } {  } 0 0 "Fitter routing operations beginning" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_PERCENT_ROUTING_RESOURCE_USAGE" "0 " "Info: Router estimated average interconnect usage is 0% of the available device resources" { { "Info" "IFITAPI_FITAPI_VPR_PEAK_ROUTING_REGION" "2 X21_Y10 X30_Y19 " "Info: Router estimated peak interconnect usage is 2% of the available device resources in the region that extends from location X21_Y10 to location X30_Y19" {  } {  } 0 0 "Router estimated peak interconnect usage is %1!d!%% of the available device resources in the region that extends from location %2!s! to location %3!s!" 0 0 "" 0 -1}  } {  } 0 0 "Router estimated average interconnect usage is %1!d!%% of the available device resources" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_END" "00:00:00 " "Info: Fitter routing operations ending: elapsed time is 00:00:00" {  } {  } 0 0 "Fitter routing operations ending: elapsed time is %1!s!" 0 0 "" 0 -1}
{ "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED" "" "Info: The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." { { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_ROUTABILITY" "" "Info: Optimizations that may affect the design's routability were skipped" {  } {  } 0 0 "Optimizations that may affect the design's routability were skipped" 0 0 "" 0 -1} { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_TIMING" "" "Info: Optimizations that may affect the design's timing were skipped" {  } {  } 0 0 "Optimizations that may affect the design's timing were skipped" 0 0 "" 0 -1}  } {  } 0 0 "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_STARTED" "" "Info: Started post-fitting delay annotation" {  } {  } 0 0 "Started post-fitting delay annotation" 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Info: Delay annotation completed successfully" {  } {  } 0 0 "Delay annotation completed successfully" 0 0 "" 0 -1}
{ "Warning" "WFITCC_FITCC_IGNORED_ASSIGNMENT" "" "Warning: Found invalid Fitter assignments. See the Ignored Assignments panel in the Fitter Compilation Report for more information." {  } {  } 0 0 "Found invalid Fitter assignments. See the Ignored Assignments panel in the Fitter Compilation Report for more information." 0 0 "" 0 -1}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/DAC904_HighSpeedDAC.fit.smsg " "Info: Generated suppressed messages file D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/DAC904_HighSpeedDAC.fit.smsg" {  } {  } 0 0 "Generated suppressed messages file %1!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_ERROR_COUNT" "Fitter 0 s 12 s Quartus II 64-Bit " "Info: Quartus II 64-Bit Fitter was successful. 0 errors, 12 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4696 " "Info: Peak virtual memory: 4696 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "" 0 -1} { "Info" "IQEXE_END_BANNER_TIME" "Tue Jul 25 11:30:36 2023 " "Info: Processing ended: Tue Jul 25 11:30:36 2023" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_TIME" "00:00:03 " "Info: Elapsed time: 00:00:03" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:03 " "Info: Total CPU time (on all processors): 00:00:03" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "" 0 -1}
