/**
  ******************************************************************************
  * @file    dac904_simple.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   DAC904简化版驱动实现
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "dac904_simple.h"

/* Private variables ---------------------------------------------------------*/
static uint8_t dac904_initialized = 0;

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  DAC904简化初始化
  * @param  None
  * @retval None
  */
void DAC904_Simple_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    // 使能GPIOE时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);

    // 配置数据引脚 (PE0-PE13)
    GPIO_InitStructure.GPIO_Pin = DAC904_DATA_PINS_MASK;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(DAC904_DATA_PORT, &GPIO_InitStructure);

    // 配置时钟引脚 (PE14)
    GPIO_InitStructure.GPIO_Pin = DAC904_CLK_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(DAC904_CLOCK_PORT, &GPIO_InitStructure);

    // 初始化引脚状态
    GPIO_ResetBits(DAC904_DATA_PORT, DAC904_DATA_PINS_MASK);  // 数据引脚清零
    GPIO_ResetBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN);        // 时钟引脚清零

    // 设置初始输出为中间值
    DAC904_Simple_WriteData(DAC904_DATA_MID);

    dac904_initialized = 1;
}

/**
  * @brief  DAC904写入数据
  * @param  data: 14位数据值 (0-16383)
  * @retval None
  */
void DAC904_Simple_WriteData(uint16_t data)
{
    // 限制数据范围
    if (data > DAC904_DATA_MAX) {
        data = DAC904_DATA_MAX;
    }

    // 写入并行数据
    uint32_t temp_odr = DAC904_DATA_PORT->ODR;
    temp_odr &= ~DAC904_DATA_PINS_MASK;
    temp_odr |= (data & DAC904_DATA_MAX);
    DAC904_DATA_PORT->ODR = temp_odr;

    // 产生时钟脉冲
    GPIO_SetBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN);
    // 短暂延时确保建立时间
    __NOP(); __NOP(); __NOP(); __NOP();
    GPIO_ResetBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN);
}

/**
  * @brief  DAC904设置电压
  * @param  voltage: 电压值 (0.0-5.0V)
  * @retval None
  */
void DAC904_Simple_SetVoltage(float voltage)
{
    // 限制电压范围
    if (voltage < 0.0f) voltage = 0.0f;
    if (voltage > 5.0f) voltage = 5.0f;

    // 转换为DAC数据
    uint16_t dac_data = (uint16_t)((voltage * DAC904_DATA_MAX) / 5.0f);

    // 写入DAC
    DAC904_Simple_WriteData(dac_data);
}

/**
  * @brief  DAC904测试函数
  * @param  None
  * @retval None
  */
void DAC904_Simple_Test(void)
{
    if (!dac904_initialized) {
        DAC904_Simple_Init();
    }

    // 测试不同电压输出
    DAC904_Simple_SetVoltage(0.0f);   // 0V
    for (volatile int i = 0; i < 1000000; i++);  // 延时

    DAC904_Simple_SetVoltage(1.25f);  // 1.25V
    for (volatile int i = 0; i < 1000000; i++);  // 延时

    DAC904_Simple_SetVoltage(2.5f);   // 2.5V
    for (volatile int i = 0; i < 1000000; i++);  // 延时

    DAC904_Simple_SetVoltage(3.75f);  // 3.75V
    for (volatile int i = 0; i < 1000000; i++);  // 延时

    DAC904_Simple_SetVoltage(5.0f);   // 5V
    for (volatile int i = 0; i < 1000000; i++);  // 延时

    // 回到中间值
    DAC904_Simple_SetVoltage(2.5f);   // 2.5V
}

/************************ (C) COPYRIGHT DAC904移植项目组 *****END OF FILE****/
