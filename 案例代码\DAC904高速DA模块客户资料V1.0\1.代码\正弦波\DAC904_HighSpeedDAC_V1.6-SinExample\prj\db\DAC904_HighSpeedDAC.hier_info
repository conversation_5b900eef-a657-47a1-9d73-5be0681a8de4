|DAC904_WriteTEST
clk => clk.IN1
rstn => rstn.IN2


|DAC904_WriteTEST|pll165m:U0_pll165m_inst
areset => areset.IN1
inclk0 => sub_wire5[0].IN1


|DAC904_WriteTEST|pll165m:U0_pll165m_inst|altpll:altpll_component
inclk[0] => pll165m_altpll:auto_generated.inclk[0]
inclk[1] => pll165m_altpll:auto_generated.inclk[1]
fbin => ~NO_FANOUT~
pllena => ~NO_FANOUT~
clkswitch => ~NO_FANOUT~
areset => pll165m_altpll:auto_generated.areset
pfdena => ~NO_FANOUT~
clkena[0] => ~NO_FANOUT~
clkena[1] => ~NO_FANOUT~
clkena[2] => ~NO_FANOUT~
clkena[3] => ~NO_FANOUT~
clkena[4] => ~NO_FANOUT~
clkena[5] => ~NO_FANOUT~
extclkena[0] => ~NO_FANOUT~
extclkena[1] => ~NO_FANOUT~
extclkena[2] => ~NO_FANOUT~
extclkena[3] => ~NO_FANOUT~
scanclk => ~NO_FANOUT~
scanclkena => ~NO_FANOUT~
scanaclr => ~NO_FANOUT~
scanread => ~NO_FANOUT~
scanwrite => ~NO_FANOUT~
scandata => ~NO_FANOUT~
phasecounterselect[0] => ~NO_FANOUT~
phasecounterselect[1] => ~NO_FANOUT~
phasecounterselect[2] => ~NO_FANOUT~
phasecounterselect[3] => ~NO_FANOUT~
phaseupdown => ~NO_FANOUT~
phasestep => ~NO_FANOUT~
configupdate => ~NO_FANOUT~
fbmimicbidir <> <GND>


|DAC904_WriteTEST|pll165m:U0_pll165m_inst|altpll:altpll_component|pll165m_altpll:auto_generated
areset => pll_lock_sync.ACLR
areset => pll1.ARESET
inclk[0] => pll1.CLK
inclk[1] => pll1.CLK1


|DAC904_WriteTEST|PhaseAccumulator:U1_PhaseAccumulator
CLK => PhaseSum[0].CLK
CLK => PhaseSum[1].CLK
CLK => PhaseSum[2].CLK
CLK => PhaseSum[3].CLK
CLK => PhaseSum[4].CLK
CLK => PhaseSum[5].CLK
CLK => PhaseSum[6].CLK
CLK => PhaseSum[7].CLK
CLK => PhaseSum[8].CLK
CLK => PhaseSum[9].CLK
CLK => PhaseSum[10].CLK
CLK => PhaseSum[11].CLK
CLK => PhaseSum[12].CLK
CLK => PhaseSum[13].CLK
CLK => PhaseSum[14].CLK
CLK => PhaseSum[15].CLK
CLK => PhaseSum[16].CLK
CLK => PhaseSum[17].CLK
CLK => PhaseSum[18].CLK
CLK => PhaseSum[19].CLK
CLK => PhaseSum[20].CLK
CLK => PhaseSum[21].CLK
CLK => PhaseSum[22].CLK
CLK => PhaseSum[23].CLK
CLK => PhaseSum[24].CLK
CLK => PhaseSum[25].CLK
CLK => PhaseSum[26].CLK
CLK => PhaseSum[27].CLK
CLK => PhaseSum[28].CLK
CLK => PhaseSum[29].CLK
CLK => PhaseSum[30].CLK
CLK => PhaseSum[31].CLK
RSTn => PhaseSum[0].ACLR
RSTn => PhaseSum[1].ACLR
RSTn => PhaseSum[2].ACLR
RSTn => PhaseSum[3].ACLR
RSTn => PhaseSum[4].ACLR
RSTn => PhaseSum[5].ACLR
RSTn => PhaseSum[6].ACLR
RSTn => PhaseSum[7].ACLR
RSTn => PhaseSum[8].ACLR
RSTn => PhaseSum[9].ACLR
RSTn => PhaseSum[10].ACLR
RSTn => PhaseSum[11].ACLR
RSTn => PhaseSum[12].ACLR
RSTn => PhaseSum[13].ACLR
RSTn => PhaseSum[14].ACLR
RSTn => PhaseSum[15].ACLR
RSTn => PhaseSum[16].ACLR
RSTn => PhaseSum[17].ACLR
RSTn => PhaseSum[18].ACLR
RSTn => PhaseSum[19].ACLR
RSTn => PhaseSum[20].ACLR
RSTn => PhaseSum[21].ACLR
RSTn => PhaseSum[22].ACLR
RSTn => PhaseSum[23].ACLR
RSTn => PhaseSum[24].ACLR
RSTn => PhaseSum[25].ACLR
RSTn => PhaseSum[26].ACLR
RSTn => PhaseSum[27].ACLR
RSTn => PhaseSum[28].ACLR
RSTn => PhaseSum[29].ACLR
RSTn => PhaseSum[30].ACLR
RSTn => PhaseSum[31].ACLR
PhaseStep[0] => Add0.IN32
PhaseStep[1] => Add0.IN31
PhaseStep[2] => Add0.IN30
PhaseStep[3] => Add0.IN29
PhaseStep[4] => Add0.IN28
PhaseStep[5] => Add0.IN27
PhaseStep[6] => Add0.IN26
PhaseStep[7] => Add0.IN25
PhaseStep[8] => Add0.IN24
PhaseStep[9] => Add0.IN23
PhaseStep[10] => Add0.IN22
PhaseStep[11] => Add0.IN21
PhaseStep[12] => Add0.IN20
PhaseStep[13] => Add0.IN19
PhaseStep[14] => Add0.IN18
PhaseStep[15] => Add0.IN17
PhaseStep[16] => Add0.IN16
PhaseStep[17] => Add0.IN15
PhaseStep[18] => Add0.IN14
PhaseStep[19] => Add0.IN13
PhaseStep[20] => Add0.IN12
PhaseStep[21] => Add0.IN11
PhaseStep[22] => Add0.IN10
PhaseStep[23] => Add0.IN9
PhaseStep[24] => Add0.IN8
PhaseStep[25] => Add0.IN7
PhaseStep[26] => Add0.IN6
PhaseStep[27] => Add0.IN5
PhaseStep[28] => Add0.IN4
PhaseStep[29] => Add0.IN3
PhaseStep[30] => Add0.IN2
PhaseStep[31] => Add0.IN1


|DAC904_WriteTEST|rom_14B_Sin:U2_rom_14B_Sin
address[0] => address[0].IN1
address[1] => address[1].IN1
address[2] => address[2].IN1
address[3] => address[3].IN1
address[4] => address[4].IN1
address[5] => address[5].IN1
address[6] => address[6].IN1
address[7] => address[7].IN1
address[8] => address[8].IN1
address[9] => address[9].IN1
address[10] => address[10].IN1
address[11] => address[11].IN1
address[12] => address[12].IN1
address[13] => address[13].IN1
clock => clock.IN1


|DAC904_WriteTEST|rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component
wren_a => ~NO_FANOUT~
rden_a => ~NO_FANOUT~
wren_b => ~NO_FANOUT~
rden_b => ~NO_FANOUT~
data_a[0] => ~NO_FANOUT~
data_a[1] => ~NO_FANOUT~
data_a[2] => ~NO_FANOUT~
data_a[3] => ~NO_FANOUT~
data_a[4] => ~NO_FANOUT~
data_a[5] => ~NO_FANOUT~
data_a[6] => ~NO_FANOUT~
data_a[7] => ~NO_FANOUT~
data_a[8] => ~NO_FANOUT~
data_a[9] => ~NO_FANOUT~
data_a[10] => ~NO_FANOUT~
data_a[11] => ~NO_FANOUT~
data_a[12] => ~NO_FANOUT~
data_a[13] => ~NO_FANOUT~
data_b[0] => ~NO_FANOUT~
address_a[0] => altsyncram_vhb1:auto_generated.address_a[0]
address_a[1] => altsyncram_vhb1:auto_generated.address_a[1]
address_a[2] => altsyncram_vhb1:auto_generated.address_a[2]
address_a[3] => altsyncram_vhb1:auto_generated.address_a[3]
address_a[4] => altsyncram_vhb1:auto_generated.address_a[4]
address_a[5] => altsyncram_vhb1:auto_generated.address_a[5]
address_a[6] => altsyncram_vhb1:auto_generated.address_a[6]
address_a[7] => altsyncram_vhb1:auto_generated.address_a[7]
address_a[8] => altsyncram_vhb1:auto_generated.address_a[8]
address_a[9] => altsyncram_vhb1:auto_generated.address_a[9]
address_a[10] => altsyncram_vhb1:auto_generated.address_a[10]
address_a[11] => altsyncram_vhb1:auto_generated.address_a[11]
address_a[12] => altsyncram_vhb1:auto_generated.address_a[12]
address_a[13] => altsyncram_vhb1:auto_generated.address_a[13]
address_b[0] => ~NO_FANOUT~
addressstall_a => ~NO_FANOUT~
addressstall_b => ~NO_FANOUT~
clock0 => altsyncram_vhb1:auto_generated.clock0
clock1 => ~NO_FANOUT~
clocken0 => ~NO_FANOUT~
clocken1 => ~NO_FANOUT~
clocken2 => ~NO_FANOUT~
clocken3 => ~NO_FANOUT~
aclr0 => ~NO_FANOUT~
aclr1 => ~NO_FANOUT~
byteena_a[0] => ~NO_FANOUT~
byteena_b[0] => ~NO_FANOUT~


|DAC904_WriteTEST|rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated
address_a[0] => ram_block1a0.PORTAADDR
address_a[0] => ram_block1a1.PORTAADDR
address_a[0] => ram_block1a2.PORTAADDR
address_a[0] => ram_block1a3.PORTAADDR
address_a[0] => ram_block1a4.PORTAADDR
address_a[0] => ram_block1a5.PORTAADDR
address_a[0] => ram_block1a6.PORTAADDR
address_a[0] => ram_block1a7.PORTAADDR
address_a[0] => ram_block1a8.PORTAADDR
address_a[0] => ram_block1a9.PORTAADDR
address_a[0] => ram_block1a10.PORTAADDR
address_a[0] => ram_block1a11.PORTAADDR
address_a[0] => ram_block1a12.PORTAADDR
address_a[0] => ram_block1a13.PORTAADDR
address_a[0] => ram_block1a14.PORTAADDR
address_a[0] => ram_block1a15.PORTAADDR
address_a[0] => ram_block1a16.PORTAADDR
address_a[0] => ram_block1a17.PORTAADDR
address_a[0] => ram_block1a18.PORTAADDR
address_a[0] => ram_block1a19.PORTAADDR
address_a[0] => ram_block1a20.PORTAADDR
address_a[0] => ram_block1a21.PORTAADDR
address_a[0] => ram_block1a22.PORTAADDR
address_a[0] => ram_block1a23.PORTAADDR
address_a[0] => ram_block1a24.PORTAADDR
address_a[0] => ram_block1a25.PORTAADDR
address_a[0] => ram_block1a26.PORTAADDR
address_a[0] => ram_block1a27.PORTAADDR
address_a[1] => ram_block1a0.PORTAADDR1
address_a[1] => ram_block1a1.PORTAADDR1
address_a[1] => ram_block1a2.PORTAADDR1
address_a[1] => ram_block1a3.PORTAADDR1
address_a[1] => ram_block1a4.PORTAADDR1
address_a[1] => ram_block1a5.PORTAADDR1
address_a[1] => ram_block1a6.PORTAADDR1
address_a[1] => ram_block1a7.PORTAADDR1
address_a[1] => ram_block1a8.PORTAADDR1
address_a[1] => ram_block1a9.PORTAADDR1
address_a[1] => ram_block1a10.PORTAADDR1
address_a[1] => ram_block1a11.PORTAADDR1
address_a[1] => ram_block1a12.PORTAADDR1
address_a[1] => ram_block1a13.PORTAADDR1
address_a[1] => ram_block1a14.PORTAADDR1
address_a[1] => ram_block1a15.PORTAADDR1
address_a[1] => ram_block1a16.PORTAADDR1
address_a[1] => ram_block1a17.PORTAADDR1
address_a[1] => ram_block1a18.PORTAADDR1
address_a[1] => ram_block1a19.PORTAADDR1
address_a[1] => ram_block1a20.PORTAADDR1
address_a[1] => ram_block1a21.PORTAADDR1
address_a[1] => ram_block1a22.PORTAADDR1
address_a[1] => ram_block1a23.PORTAADDR1
address_a[1] => ram_block1a24.PORTAADDR1
address_a[1] => ram_block1a25.PORTAADDR1
address_a[1] => ram_block1a26.PORTAADDR1
address_a[1] => ram_block1a27.PORTAADDR1
address_a[2] => ram_block1a0.PORTAADDR2
address_a[2] => ram_block1a1.PORTAADDR2
address_a[2] => ram_block1a2.PORTAADDR2
address_a[2] => ram_block1a3.PORTAADDR2
address_a[2] => ram_block1a4.PORTAADDR2
address_a[2] => ram_block1a5.PORTAADDR2
address_a[2] => ram_block1a6.PORTAADDR2
address_a[2] => ram_block1a7.PORTAADDR2
address_a[2] => ram_block1a8.PORTAADDR2
address_a[2] => ram_block1a9.PORTAADDR2
address_a[2] => ram_block1a10.PORTAADDR2
address_a[2] => ram_block1a11.PORTAADDR2
address_a[2] => ram_block1a12.PORTAADDR2
address_a[2] => ram_block1a13.PORTAADDR2
address_a[2] => ram_block1a14.PORTAADDR2
address_a[2] => ram_block1a15.PORTAADDR2
address_a[2] => ram_block1a16.PORTAADDR2
address_a[2] => ram_block1a17.PORTAADDR2
address_a[2] => ram_block1a18.PORTAADDR2
address_a[2] => ram_block1a19.PORTAADDR2
address_a[2] => ram_block1a20.PORTAADDR2
address_a[2] => ram_block1a21.PORTAADDR2
address_a[2] => ram_block1a22.PORTAADDR2
address_a[2] => ram_block1a23.PORTAADDR2
address_a[2] => ram_block1a24.PORTAADDR2
address_a[2] => ram_block1a25.PORTAADDR2
address_a[2] => ram_block1a26.PORTAADDR2
address_a[2] => ram_block1a27.PORTAADDR2
address_a[3] => ram_block1a0.PORTAADDR3
address_a[3] => ram_block1a1.PORTAADDR3
address_a[3] => ram_block1a2.PORTAADDR3
address_a[3] => ram_block1a3.PORTAADDR3
address_a[3] => ram_block1a4.PORTAADDR3
address_a[3] => ram_block1a5.PORTAADDR3
address_a[3] => ram_block1a6.PORTAADDR3
address_a[3] => ram_block1a7.PORTAADDR3
address_a[3] => ram_block1a8.PORTAADDR3
address_a[3] => ram_block1a9.PORTAADDR3
address_a[3] => ram_block1a10.PORTAADDR3
address_a[3] => ram_block1a11.PORTAADDR3
address_a[3] => ram_block1a12.PORTAADDR3
address_a[3] => ram_block1a13.PORTAADDR3
address_a[3] => ram_block1a14.PORTAADDR3
address_a[3] => ram_block1a15.PORTAADDR3
address_a[3] => ram_block1a16.PORTAADDR3
address_a[3] => ram_block1a17.PORTAADDR3
address_a[3] => ram_block1a18.PORTAADDR3
address_a[3] => ram_block1a19.PORTAADDR3
address_a[3] => ram_block1a20.PORTAADDR3
address_a[3] => ram_block1a21.PORTAADDR3
address_a[3] => ram_block1a22.PORTAADDR3
address_a[3] => ram_block1a23.PORTAADDR3
address_a[3] => ram_block1a24.PORTAADDR3
address_a[3] => ram_block1a25.PORTAADDR3
address_a[3] => ram_block1a26.PORTAADDR3
address_a[3] => ram_block1a27.PORTAADDR3
address_a[4] => ram_block1a0.PORTAADDR4
address_a[4] => ram_block1a1.PORTAADDR4
address_a[4] => ram_block1a2.PORTAADDR4
address_a[4] => ram_block1a3.PORTAADDR4
address_a[4] => ram_block1a4.PORTAADDR4
address_a[4] => ram_block1a5.PORTAADDR4
address_a[4] => ram_block1a6.PORTAADDR4
address_a[4] => ram_block1a7.PORTAADDR4
address_a[4] => ram_block1a8.PORTAADDR4
address_a[4] => ram_block1a9.PORTAADDR4
address_a[4] => ram_block1a10.PORTAADDR4
address_a[4] => ram_block1a11.PORTAADDR4
address_a[4] => ram_block1a12.PORTAADDR4
address_a[4] => ram_block1a13.PORTAADDR4
address_a[4] => ram_block1a14.PORTAADDR4
address_a[4] => ram_block1a15.PORTAADDR4
address_a[4] => ram_block1a16.PORTAADDR4
address_a[4] => ram_block1a17.PORTAADDR4
address_a[4] => ram_block1a18.PORTAADDR4
address_a[4] => ram_block1a19.PORTAADDR4
address_a[4] => ram_block1a20.PORTAADDR4
address_a[4] => ram_block1a21.PORTAADDR4
address_a[4] => ram_block1a22.PORTAADDR4
address_a[4] => ram_block1a23.PORTAADDR4
address_a[4] => ram_block1a24.PORTAADDR4
address_a[4] => ram_block1a25.PORTAADDR4
address_a[4] => ram_block1a26.PORTAADDR4
address_a[4] => ram_block1a27.PORTAADDR4
address_a[5] => ram_block1a0.PORTAADDR5
address_a[5] => ram_block1a1.PORTAADDR5
address_a[5] => ram_block1a2.PORTAADDR5
address_a[5] => ram_block1a3.PORTAADDR5
address_a[5] => ram_block1a4.PORTAADDR5
address_a[5] => ram_block1a5.PORTAADDR5
address_a[5] => ram_block1a6.PORTAADDR5
address_a[5] => ram_block1a7.PORTAADDR5
address_a[5] => ram_block1a8.PORTAADDR5
address_a[5] => ram_block1a9.PORTAADDR5
address_a[5] => ram_block1a10.PORTAADDR5
address_a[5] => ram_block1a11.PORTAADDR5
address_a[5] => ram_block1a12.PORTAADDR5
address_a[5] => ram_block1a13.PORTAADDR5
address_a[5] => ram_block1a14.PORTAADDR5
address_a[5] => ram_block1a15.PORTAADDR5
address_a[5] => ram_block1a16.PORTAADDR5
address_a[5] => ram_block1a17.PORTAADDR5
address_a[5] => ram_block1a18.PORTAADDR5
address_a[5] => ram_block1a19.PORTAADDR5
address_a[5] => ram_block1a20.PORTAADDR5
address_a[5] => ram_block1a21.PORTAADDR5
address_a[5] => ram_block1a22.PORTAADDR5
address_a[5] => ram_block1a23.PORTAADDR5
address_a[5] => ram_block1a24.PORTAADDR5
address_a[5] => ram_block1a25.PORTAADDR5
address_a[5] => ram_block1a26.PORTAADDR5
address_a[5] => ram_block1a27.PORTAADDR5
address_a[6] => ram_block1a0.PORTAADDR6
address_a[6] => ram_block1a1.PORTAADDR6
address_a[6] => ram_block1a2.PORTAADDR6
address_a[6] => ram_block1a3.PORTAADDR6
address_a[6] => ram_block1a4.PORTAADDR6
address_a[6] => ram_block1a5.PORTAADDR6
address_a[6] => ram_block1a6.PORTAADDR6
address_a[6] => ram_block1a7.PORTAADDR6
address_a[6] => ram_block1a8.PORTAADDR6
address_a[6] => ram_block1a9.PORTAADDR6
address_a[6] => ram_block1a10.PORTAADDR6
address_a[6] => ram_block1a11.PORTAADDR6
address_a[6] => ram_block1a12.PORTAADDR6
address_a[6] => ram_block1a13.PORTAADDR6
address_a[6] => ram_block1a14.PORTAADDR6
address_a[6] => ram_block1a15.PORTAADDR6
address_a[6] => ram_block1a16.PORTAADDR6
address_a[6] => ram_block1a17.PORTAADDR6
address_a[6] => ram_block1a18.PORTAADDR6
address_a[6] => ram_block1a19.PORTAADDR6
address_a[6] => ram_block1a20.PORTAADDR6
address_a[6] => ram_block1a21.PORTAADDR6
address_a[6] => ram_block1a22.PORTAADDR6
address_a[6] => ram_block1a23.PORTAADDR6
address_a[6] => ram_block1a24.PORTAADDR6
address_a[6] => ram_block1a25.PORTAADDR6
address_a[6] => ram_block1a26.PORTAADDR6
address_a[6] => ram_block1a27.PORTAADDR6
address_a[7] => ram_block1a0.PORTAADDR7
address_a[7] => ram_block1a1.PORTAADDR7
address_a[7] => ram_block1a2.PORTAADDR7
address_a[7] => ram_block1a3.PORTAADDR7
address_a[7] => ram_block1a4.PORTAADDR7
address_a[7] => ram_block1a5.PORTAADDR7
address_a[7] => ram_block1a6.PORTAADDR7
address_a[7] => ram_block1a7.PORTAADDR7
address_a[7] => ram_block1a8.PORTAADDR7
address_a[7] => ram_block1a9.PORTAADDR7
address_a[7] => ram_block1a10.PORTAADDR7
address_a[7] => ram_block1a11.PORTAADDR7
address_a[7] => ram_block1a12.PORTAADDR7
address_a[7] => ram_block1a13.PORTAADDR7
address_a[7] => ram_block1a14.PORTAADDR7
address_a[7] => ram_block1a15.PORTAADDR7
address_a[7] => ram_block1a16.PORTAADDR7
address_a[7] => ram_block1a17.PORTAADDR7
address_a[7] => ram_block1a18.PORTAADDR7
address_a[7] => ram_block1a19.PORTAADDR7
address_a[7] => ram_block1a20.PORTAADDR7
address_a[7] => ram_block1a21.PORTAADDR7
address_a[7] => ram_block1a22.PORTAADDR7
address_a[7] => ram_block1a23.PORTAADDR7
address_a[7] => ram_block1a24.PORTAADDR7
address_a[7] => ram_block1a25.PORTAADDR7
address_a[7] => ram_block1a26.PORTAADDR7
address_a[7] => ram_block1a27.PORTAADDR7
address_a[8] => ram_block1a0.PORTAADDR8
address_a[8] => ram_block1a1.PORTAADDR8
address_a[8] => ram_block1a2.PORTAADDR8
address_a[8] => ram_block1a3.PORTAADDR8
address_a[8] => ram_block1a4.PORTAADDR8
address_a[8] => ram_block1a5.PORTAADDR8
address_a[8] => ram_block1a6.PORTAADDR8
address_a[8] => ram_block1a7.PORTAADDR8
address_a[8] => ram_block1a8.PORTAADDR8
address_a[8] => ram_block1a9.PORTAADDR8
address_a[8] => ram_block1a10.PORTAADDR8
address_a[8] => ram_block1a11.PORTAADDR8
address_a[8] => ram_block1a12.PORTAADDR8
address_a[8] => ram_block1a13.PORTAADDR8
address_a[8] => ram_block1a14.PORTAADDR8
address_a[8] => ram_block1a15.PORTAADDR8
address_a[8] => ram_block1a16.PORTAADDR8
address_a[8] => ram_block1a17.PORTAADDR8
address_a[8] => ram_block1a18.PORTAADDR8
address_a[8] => ram_block1a19.PORTAADDR8
address_a[8] => ram_block1a20.PORTAADDR8
address_a[8] => ram_block1a21.PORTAADDR8
address_a[8] => ram_block1a22.PORTAADDR8
address_a[8] => ram_block1a23.PORTAADDR8
address_a[8] => ram_block1a24.PORTAADDR8
address_a[8] => ram_block1a25.PORTAADDR8
address_a[8] => ram_block1a26.PORTAADDR8
address_a[8] => ram_block1a27.PORTAADDR8
address_a[9] => ram_block1a0.PORTAADDR9
address_a[9] => ram_block1a1.PORTAADDR9
address_a[9] => ram_block1a2.PORTAADDR9
address_a[9] => ram_block1a3.PORTAADDR9
address_a[9] => ram_block1a4.PORTAADDR9
address_a[9] => ram_block1a5.PORTAADDR9
address_a[9] => ram_block1a6.PORTAADDR9
address_a[9] => ram_block1a7.PORTAADDR9
address_a[9] => ram_block1a8.PORTAADDR9
address_a[9] => ram_block1a9.PORTAADDR9
address_a[9] => ram_block1a10.PORTAADDR9
address_a[9] => ram_block1a11.PORTAADDR9
address_a[9] => ram_block1a12.PORTAADDR9
address_a[9] => ram_block1a13.PORTAADDR9
address_a[9] => ram_block1a14.PORTAADDR9
address_a[9] => ram_block1a15.PORTAADDR9
address_a[9] => ram_block1a16.PORTAADDR9
address_a[9] => ram_block1a17.PORTAADDR9
address_a[9] => ram_block1a18.PORTAADDR9
address_a[9] => ram_block1a19.PORTAADDR9
address_a[9] => ram_block1a20.PORTAADDR9
address_a[9] => ram_block1a21.PORTAADDR9
address_a[9] => ram_block1a22.PORTAADDR9
address_a[9] => ram_block1a23.PORTAADDR9
address_a[9] => ram_block1a24.PORTAADDR9
address_a[9] => ram_block1a25.PORTAADDR9
address_a[9] => ram_block1a26.PORTAADDR9
address_a[9] => ram_block1a27.PORTAADDR9
address_a[10] => ram_block1a0.PORTAADDR10
address_a[10] => ram_block1a1.PORTAADDR10
address_a[10] => ram_block1a2.PORTAADDR10
address_a[10] => ram_block1a3.PORTAADDR10
address_a[10] => ram_block1a4.PORTAADDR10
address_a[10] => ram_block1a5.PORTAADDR10
address_a[10] => ram_block1a6.PORTAADDR10
address_a[10] => ram_block1a7.PORTAADDR10
address_a[10] => ram_block1a8.PORTAADDR10
address_a[10] => ram_block1a9.PORTAADDR10
address_a[10] => ram_block1a10.PORTAADDR10
address_a[10] => ram_block1a11.PORTAADDR10
address_a[10] => ram_block1a12.PORTAADDR10
address_a[10] => ram_block1a13.PORTAADDR10
address_a[10] => ram_block1a14.PORTAADDR10
address_a[10] => ram_block1a15.PORTAADDR10
address_a[10] => ram_block1a16.PORTAADDR10
address_a[10] => ram_block1a17.PORTAADDR10
address_a[10] => ram_block1a18.PORTAADDR10
address_a[10] => ram_block1a19.PORTAADDR10
address_a[10] => ram_block1a20.PORTAADDR10
address_a[10] => ram_block1a21.PORTAADDR10
address_a[10] => ram_block1a22.PORTAADDR10
address_a[10] => ram_block1a23.PORTAADDR10
address_a[10] => ram_block1a24.PORTAADDR10
address_a[10] => ram_block1a25.PORTAADDR10
address_a[10] => ram_block1a26.PORTAADDR10
address_a[10] => ram_block1a27.PORTAADDR10
address_a[11] => ram_block1a0.PORTAADDR11
address_a[11] => ram_block1a1.PORTAADDR11
address_a[11] => ram_block1a2.PORTAADDR11
address_a[11] => ram_block1a3.PORTAADDR11
address_a[11] => ram_block1a4.PORTAADDR11
address_a[11] => ram_block1a5.PORTAADDR11
address_a[11] => ram_block1a6.PORTAADDR11
address_a[11] => ram_block1a7.PORTAADDR11
address_a[11] => ram_block1a8.PORTAADDR11
address_a[11] => ram_block1a9.PORTAADDR11
address_a[11] => ram_block1a10.PORTAADDR11
address_a[11] => ram_block1a11.PORTAADDR11
address_a[11] => ram_block1a12.PORTAADDR11
address_a[11] => ram_block1a13.PORTAADDR11
address_a[11] => ram_block1a14.PORTAADDR11
address_a[11] => ram_block1a15.PORTAADDR11
address_a[11] => ram_block1a16.PORTAADDR11
address_a[11] => ram_block1a17.PORTAADDR11
address_a[11] => ram_block1a18.PORTAADDR11
address_a[11] => ram_block1a19.PORTAADDR11
address_a[11] => ram_block1a20.PORTAADDR11
address_a[11] => ram_block1a21.PORTAADDR11
address_a[11] => ram_block1a22.PORTAADDR11
address_a[11] => ram_block1a23.PORTAADDR11
address_a[11] => ram_block1a24.PORTAADDR11
address_a[11] => ram_block1a25.PORTAADDR11
address_a[11] => ram_block1a26.PORTAADDR11
address_a[11] => ram_block1a27.PORTAADDR11
address_a[12] => ram_block1a0.PORTAADDR12
address_a[12] => ram_block1a1.PORTAADDR12
address_a[12] => ram_block1a2.PORTAADDR12
address_a[12] => ram_block1a3.PORTAADDR12
address_a[12] => ram_block1a4.PORTAADDR12
address_a[12] => ram_block1a5.PORTAADDR12
address_a[12] => ram_block1a6.PORTAADDR12
address_a[12] => ram_block1a7.PORTAADDR12
address_a[12] => ram_block1a8.PORTAADDR12
address_a[12] => ram_block1a9.PORTAADDR12
address_a[12] => ram_block1a10.PORTAADDR12
address_a[12] => ram_block1a11.PORTAADDR12
address_a[12] => ram_block1a12.PORTAADDR12
address_a[12] => ram_block1a13.PORTAADDR12
address_a[12] => ram_block1a14.PORTAADDR12
address_a[12] => ram_block1a15.PORTAADDR12
address_a[12] => ram_block1a16.PORTAADDR12
address_a[12] => ram_block1a17.PORTAADDR12
address_a[12] => ram_block1a18.PORTAADDR12
address_a[12] => ram_block1a19.PORTAADDR12
address_a[12] => ram_block1a20.PORTAADDR12
address_a[12] => ram_block1a21.PORTAADDR12
address_a[12] => ram_block1a22.PORTAADDR12
address_a[12] => ram_block1a23.PORTAADDR12
address_a[12] => ram_block1a24.PORTAADDR12
address_a[12] => ram_block1a25.PORTAADDR12
address_a[12] => ram_block1a26.PORTAADDR12
address_a[12] => ram_block1a27.PORTAADDR12
address_a[13] => address_reg_a[0].DATAIN
address_a[13] => decode_c8a:rden_decode.data[0]
clock0 => ram_block1a0.CLK0
clock0 => ram_block1a1.CLK0
clock0 => ram_block1a2.CLK0
clock0 => ram_block1a3.CLK0
clock0 => ram_block1a4.CLK0
clock0 => ram_block1a5.CLK0
clock0 => ram_block1a6.CLK0
clock0 => ram_block1a7.CLK0
clock0 => ram_block1a8.CLK0
clock0 => ram_block1a9.CLK0
clock0 => ram_block1a10.CLK0
clock0 => ram_block1a11.CLK0
clock0 => ram_block1a12.CLK0
clock0 => ram_block1a13.CLK0
clock0 => ram_block1a14.CLK0
clock0 => ram_block1a15.CLK0
clock0 => ram_block1a16.CLK0
clock0 => ram_block1a17.CLK0
clock0 => ram_block1a18.CLK0
clock0 => ram_block1a19.CLK0
clock0 => ram_block1a20.CLK0
clock0 => ram_block1a21.CLK0
clock0 => ram_block1a22.CLK0
clock0 => ram_block1a23.CLK0
clock0 => ram_block1a24.CLK0
clock0 => ram_block1a25.CLK0
clock0 => ram_block1a26.CLK0
clock0 => ram_block1a27.CLK0
clock0 => address_reg_a[0].CLK
clock0 => out_address_reg_a[0].CLK


|DAC904_WriteTEST|rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|decode_c8a:rden_decode
data[0] => eq_node[1].IN0
data[0] => eq_node[0].IN0


|DAC904_WriteTEST|rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|mux_gob:mux2
data[0] => result_node[0].IN1
data[1] => result_node[1].IN1
data[2] => result_node[2].IN1
data[3] => result_node[3].IN1
data[4] => result_node[4].IN1
data[5] => result_node[5].IN1
data[6] => result_node[6].IN1
data[7] => result_node[7].IN1
data[8] => result_node[8].IN1
data[9] => result_node[9].IN1
data[10] => result_node[10].IN1
data[11] => result_node[11].IN1
data[12] => result_node[12].IN1
data[13] => result_node[13].IN1
data[14] => result_node[0].IN1
data[15] => result_node[1].IN1
data[16] => result_node[2].IN1
data[17] => result_node[3].IN1
data[18] => result_node[4].IN1
data[19] => result_node[5].IN1
data[20] => result_node[6].IN1
data[21] => result_node[7].IN1
data[22] => result_node[8].IN1
data[23] => result_node[9].IN1
data[24] => result_node[10].IN1
data[25] => result_node[11].IN1
data[26] => result_node[12].IN1
data[27] => result_node[13].IN1
sel[0] => result_node[13].IN0
sel[0] => _.IN0
sel[0] => result_node[12].IN0
sel[0] => _.IN0
sel[0] => result_node[11].IN0
sel[0] => _.IN0
sel[0] => result_node[10].IN0
sel[0] => _.IN0
sel[0] => result_node[9].IN0
sel[0] => _.IN0
sel[0] => result_node[8].IN0
sel[0] => _.IN0
sel[0] => result_node[7].IN0
sel[0] => _.IN0
sel[0] => result_node[6].IN0
sel[0] => _.IN0
sel[0] => result_node[5].IN0
sel[0] => _.IN0
sel[0] => result_node[4].IN0
sel[0] => _.IN0
sel[0] => result_node[3].IN0
sel[0] => _.IN0
sel[0] => result_node[2].IN0
sel[0] => _.IN0
sel[0] => result_node[1].IN0
sel[0] => _.IN0
sel[0] => result_node[0].IN0
sel[0] => _.IN0


|DAC904_WriteTEST|DAC904_WriteModule:U3_DAC904_WriteModule
DAC_CLKneg => rOutputData[0].CLK
DAC_CLKneg => rOutputData[1].CLK
DAC_CLKneg => rOutputData[2].CLK
DAC_CLKneg => rOutputData[3].CLK
DAC_CLKneg => rOutputData[4].CLK
DAC_CLKneg => rOutputData[5].CLK
DAC_CLKneg => rOutputData[6].CLK
DAC_CLKneg => rOutputData[7].CLK
DAC_CLKneg => rOutputData[8].CLK
DAC_CLKneg => rOutputData[9].CLK
DAC_CLKneg => rOutputData[10].CLK
DAC_CLKneg => rOutputData[11].CLK
DAC_CLKneg => rOutputData[12].CLK
DAC_CLKneg => rOutputData[13].CLK
RSTn => rOutputData[0].ACLR
RSTn => rOutputData[1].ACLR
RSTn => rOutputData[2].ACLR
RSTn => rOutputData[3].ACLR
RSTn => rOutputData[4].ACLR
RSTn => rOutputData[5].ACLR
RSTn => rOutputData[6].ACLR
RSTn => rOutputData[7].ACLR
RSTn => rOutputData[8].ACLR
RSTn => rOutputData[9].ACLR
RSTn => rOutputData[10].ACLR
RSTn => rOutputData[11].ACLR
RSTn => rOutputData[12].ACLR
RSTn => rOutputData[13].ACLR
inputdata[0] => rOutputData[0].DATAIN
inputdata[1] => rOutputData[1].DATAIN
inputdata[2] => rOutputData[2].DATAIN
inputdata[3] => rOutputData[3].DATAIN
inputdata[4] => rOutputData[4].DATAIN
inputdata[5] => rOutputData[5].DATAIN
inputdata[6] => rOutputData[6].DATAIN
inputdata[7] => rOutputData[7].DATAIN
inputdata[8] => rOutputData[8].DATAIN
inputdata[9] => rOutputData[9].DATAIN
inputdata[10] => rOutputData[10].DATAIN
inputdata[11] => rOutputData[11].DATAIN
inputdata[12] => rOutputData[12].DATAIN
inputdata[13] => rOutputData[13].DATAIN


