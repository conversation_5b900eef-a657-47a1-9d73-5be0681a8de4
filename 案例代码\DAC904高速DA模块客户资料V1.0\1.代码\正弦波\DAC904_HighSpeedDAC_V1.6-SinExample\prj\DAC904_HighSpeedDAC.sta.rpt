TimeQuest Timing Analyzer report for DAC904_HighSpeedDAC
Tue Jul 25 11:30:39 2023
Quartus II 64-Bit Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. TimeQuest Timing Analyzer Summary
  3. Parallel Compilation
  4. Clocks
  5. Slow 1200mV 85C Model Fmax Summary
  6. Slow 1200mV 85C Model Setup Summary
  7. Slow 1200mV 85C Model Hold Summary
  8. Slow 1200mV 85C Model Recovery Summary
  9. Slow 1200mV 85C Model Removal Summary
 10. Slow 1200mV 85C Model Minimum Pulse Width Summary
 11. Slow 1200mV 85C Model Setup: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'
 12. Slow 1200mV 85C Model Hold: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'
 13. Slow 1200mV 85C Model Minimum Pulse Width: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'
 14. Slow 1200mV 85C Model Minimum Pulse Width: 'clk'
 15. Clock to Output Times
 16. Minimum Clock to Output Times
 17. Slow 1200mV 85C Model Metastability Report
 18. Slow 1200mV 0C Model Fmax Summary
 19. Slow 1200mV 0C Model Setup Summary
 20. Slow 1200mV 0C Model Hold Summary
 21. Slow 1200mV 0C Model Recovery Summary
 22. Slow 1200mV 0C Model Removal Summary
 23. Slow 1200mV 0C Model Minimum Pulse Width Summary
 24. Slow 1200mV 0C Model Setup: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'
 25. Slow 1200mV 0C Model Hold: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'
 26. Slow 1200mV 0C Model Minimum Pulse Width: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'
 27. Slow 1200mV 0C Model Minimum Pulse Width: 'clk'
 28. Clock to Output Times
 29. Minimum Clock to Output Times
 30. Slow 1200mV 0C Model Metastability Report
 31. Fast 1200mV 0C Model Setup Summary
 32. Fast 1200mV 0C Model Hold Summary
 33. Fast 1200mV 0C Model Recovery Summary
 34. Fast 1200mV 0C Model Removal Summary
 35. Fast 1200mV 0C Model Minimum Pulse Width Summary
 36. Fast 1200mV 0C Model Setup: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'
 37. Fast 1200mV 0C Model Hold: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'
 38. Fast 1200mV 0C Model Minimum Pulse Width: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'
 39. Fast 1200mV 0C Model Minimum Pulse Width: 'clk'
 40. Clock to Output Times
 41. Minimum Clock to Output Times
 42. Fast 1200mV 0C Model Metastability Report
 43. Multicorner Timing Analysis Summary
 44. Clock to Output Times
 45. Minimum Clock to Output Times
 46. Board Trace Model Assignments
 47. Input Transition Times
 48. Signal Integrity Metrics (Slow 1200mv 0c Model)
 49. Signal Integrity Metrics (Slow 1200mv 85c Model)
 50. Signal Integrity Metrics (Fast 1200mv 0c Model)
 51. Setup Transfers
 52. Hold Transfers
 53. Report TCCS
 54. Report RSKM
 55. Unconstrained Paths
 56. TimeQuest Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2011 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+---------------------------------------------------------------------------------------+
; TimeQuest Timing Analyzer Summary                                                     ;
+--------------------+------------------------------------------------------------------+
; Quartus II Version ; Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version ;
; Revision Name      ; DAC904_HighSpeedDAC                                              ;
; Device Family      ; Cyclone IV E                                                     ;
; Device Name        ; EP4CE15F17C8                                                     ;
; Timing Models      ; Final                                                            ;
; Delay Model        ; Combined                                                         ;
; Rise/Fall Delays   ; Enabled                                                          ;
+--------------------+------------------------------------------------------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 6           ;
; Maximum allowed            ; 6           ;
;                            ;             ;
; Average used               ; 4.00        ;
; Maximum used               ; 6           ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     1-4 processors         ; 100.0%      ;
;     5-6 processors         ; < 0.1%      ;
+----------------------------+-------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                                                                                                                                                                                                ;
+-------------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+---------------------------------------------------------------+-----------------------------------------------------------------+
; Clock Name                                                  ; Type      ; Period ; Frequency  ; Rise  ; Fall   ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master ; Source                                                        ; Targets                                                         ;
+-------------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+---------------------------------------------------------------+-----------------------------------------------------------------+
; clk                                                         ; Base      ; 20.000 ; 50.0 MHz   ; 0.000 ; 10.000 ;            ;           ;             ;       ;        ;           ;            ;          ;        ;                                                               ; { clk }                                                         ;
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Generated ; 6.060  ; 165.02 MHz ; 0.000 ; 3.030  ; 50.00      ; 10        ; 33          ;       ;        ;           ;            ; false    ; clk    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|inclk[0] ; { U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] } ;
+-------------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+---------------------------------------------------------------+-----------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary                                                                ;
+------------+-----------------+-------------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                                  ; Note ;
+------------+-----------------+-------------------------------------------------------------+------+
; 142.25 MHz ; 142.25 MHz      ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;      ;
+------------+-----------------+-------------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+--------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup Summary                                                  ;
+-------------------------------------------------------------+--------+---------------+
; Clock                                                       ; Slack  ; End Point TNS ;
+-------------------------------------------------------------+--------+---------------+
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; -0.485 ; -1.974        ;
+-------------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold Summary                                                  ;
+-------------------------------------------------------------+-------+---------------+
; Clock                                                       ; Slack ; End Point TNS ;
+-------------------------------------------------------------+-------+---------------+
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.463 ; 0.000         ;
+-------------------------------------------------------------+-------+---------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+-------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary                                   ;
+-------------------------------------------------------------+-------+---------------+
; Clock                                                       ; Slack ; End Point TNS ;
+-------------------------------------------------------------+-------+---------------+
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 1.859 ; 0.000         ;
; clk                                                         ; 9.931 ; 0.000         ;
+-------------------------------------------------------------+-------+---------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'                                                                                                                                                                                                                                                                                                                                            ;
+--------+----------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                                                                                                  ; To Node                                                                                                                    ; Launch Clock                                                ; Latch Clock                                                 ; Relationship ; Clock Skew ; Data Delay ;
+--------+----------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; -0.485 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[12]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.482     ; 3.034      ;
; -0.407 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[2]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.463     ; 2.975      ;
; -0.292 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[6]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.465     ; 2.858      ;
; -0.226 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[9]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.483     ; 2.774      ;
; -0.216 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[2]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.470     ; 2.777      ;
; -0.207 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[1]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.486     ; 2.752      ;
; -0.160 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[8]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.484     ; 2.707      ;
; -0.100 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[10]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.471     ; 2.660      ;
; -0.081 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[0]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.488     ; 2.624      ;
; -0.016 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[5]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.470     ; 2.577      ;
; 0.007  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[7]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.470     ; 2.554      ;
; 0.027  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[4]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.460     ; 2.544      ;
; 0.035  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[1]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.465     ; 2.531      ;
; 0.076  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[9]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.496     ; 2.459      ;
; 0.112  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[10]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.468     ; 2.451      ;
; 0.127  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[11]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.469     ; 2.435      ;
; 0.153  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[13]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.488     ; 2.390      ;
; 0.159  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[0]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.492     ; 2.380      ;
; 0.257  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[8]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.495     ; 2.279      ;
; 0.367  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[5]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.460     ; 2.204      ;
; 0.377  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[4]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.457     ; 2.197      ;
; 0.378  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[11]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.495     ; 2.158      ;
; 0.381  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[6]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.462     ; 2.188      ;
; 0.607  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[7]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.465     ; 1.959      ;
; 0.634  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[12]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.464     ; 1.933      ;
; 0.794  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[13]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.465     ; 1.772      ;
; 0.806  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[3]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.465     ; 1.760      ;
; 0.954  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[3]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.464     ; 1.613      ;
; 1.469  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[10]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.444      ;
; 1.470  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[1]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.443      ;
; 1.470  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[11]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.443      ;
; 1.471  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[0]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.442      ;
; 1.471  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[8]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.442      ;
; 1.471  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[13]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.442      ;
; 1.472  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[2]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.441      ;
; 1.472  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[3]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.441      ;
; 1.473  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[5]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.440      ;
; 1.473  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[12]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.440      ;
; 1.474  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[9]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.439      ;
; 1.474  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[6]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.439      ;
; 1.475  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[7]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.438      ;
; 1.476  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[4]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.118     ; 1.437      ;
; 1.532  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.284      ; 4.860      ;
; 1.555  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.285      ; 4.838      ;
; 1.573  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.284      ; 4.819      ;
; 2.070  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.272      ; 4.310      ;
; 2.096  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.272      ; 4.284      ;
; 2.100  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.272      ; 4.280      ;
; 2.129  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.273      ; 4.252      ;
; 2.147  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.835      ;
; 2.243  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.739      ;
; 2.259  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.723      ;
; 2.281  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.268      ; 4.095      ;
; 2.293  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.689      ;
; 2.293  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.689      ;
; 2.297  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.305      ; 4.116      ;
; 2.323  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.659      ;
; 2.356  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.265      ; 4.017      ;
; 2.388  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.594      ;
; 2.389  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.593      ;
; 2.404  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.578      ;
; 2.405  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.577      ;
; 2.427  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.121     ; 3.428      ;
; 2.427  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.121     ; 3.428      ;
; 2.427  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.121     ; 3.428      ;
; 2.427  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.121     ; 3.428      ;
; 2.427  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.121     ; 3.428      ;
; 2.427  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.121     ; 3.428      ;
; 2.427  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.121     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.428  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.120     ; 3.428      ;
; 2.429  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.119     ; 3.428      ;
; 2.429  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.119     ; 3.428      ;
; 2.429  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.119     ; 3.428      ;
; 2.435  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.547      ;
; 2.439  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.543      ;
; 2.439  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.543      ;
; 2.469  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.513      ;
; 2.469  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.513      ;
; 2.526  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.456      ;
; 2.534  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.448      ;
; 2.535  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.447      ;
; 2.550  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.432      ;
; 2.551  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.079     ; 3.431      ;
+--------+----------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'                                                                                                                                                                                                                                                                                                                            ;
+-------+------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                                                                                  ; To Node                                                                                                                    ; Launch Clock                                                ; Latch Clock                                                 ; Relationship ; Clock Skew ; Data Delay ;
+-------+------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; 0.463 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.472      ; 1.189      ;
; 0.466 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 0.758      ;
; 0.480 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.472      ; 1.206      ;
; 0.484 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.472      ; 1.210      ;
; 0.487 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.472      ; 1.213      ;
; 0.509 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 0.801      ;
; 0.513 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.472      ; 1.239      ;
; 0.702 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|address_reg_a[0] ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.079      ; 0.993      ;
; 0.728 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.472      ; 1.454      ;
; 0.737 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.029      ;
; 0.737 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.029      ;
; 0.738 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.030      ;
; 0.738 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.030      ;
; 0.738 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.030      ;
; 0.739 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.031      ;
; 0.740 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.032      ;
; 0.740 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.032      ;
; 0.741 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.033      ;
; 0.741 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.033      ;
; 0.762 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.054      ;
; 0.763 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.055      ;
; 0.763 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.055      ;
; 0.763 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.055      ;
; 0.763 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.055      ;
; 0.763 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.055      ;
; 0.765 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.057      ;
; 0.765 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.057      ;
; 0.766 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.058      ;
; 0.767 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.059      ;
; 0.767 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.059      ;
; 0.767 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.059      ;
; 0.767 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.059      ;
; 0.790 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.515      ;
; 0.802 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.468      ; 1.524      ;
; 0.812 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.537      ;
; 0.818 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.543      ;
; 0.821 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.551      ;
; 0.837 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.562      ;
; 0.838 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.563      ;
; 0.840 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.565      ;
; 0.846 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.468      ; 1.568      ;
; 0.848 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.578      ;
; 0.851 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.581      ;
; 0.851 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.581      ;
; 0.855 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.468      ; 1.577      ;
; 0.855 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.585      ;
; 0.855 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.585      ;
; 0.856 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.581      ;
; 0.857 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.582      ;
; 0.857 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.587      ;
; 0.858 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.588      ;
; 0.858 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.588      ;
; 0.860 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.590      ;
; 0.866 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.596      ;
; 0.868 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.475      ; 1.597      ;
; 0.868 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.598      ;
; 0.871 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.468      ; 1.593      ;
; 0.872 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.602      ;
; 0.874 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.604      ;
; 0.876 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.475      ; 1.605      ;
; 0.876 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.475      ; 1.605      ;
; 0.876 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.606      ;
; 0.878 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.608      ;
; 0.880 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.610      ;
; 0.888 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.613      ;
; 0.888 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.475      ; 1.617      ;
; 0.899 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.629      ;
; 0.900 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.625      ;
; 0.900 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.475      ; 1.629      ;
; 0.901 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.631      ;
; 0.904 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.629      ;
; 0.906 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.471      ; 1.631      ;
; 0.913 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.643      ;
; 0.917 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.647      ;
; 0.921 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.651      ;
; 0.922 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.476      ; 1.652      ;
; 0.929 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.468      ; 1.651      ;
; 0.978 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.475      ; 1.707      ;
; 1.040 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.477      ; 1.771      ;
; 1.051 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.477      ; 1.782      ;
; 1.062 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.477      ; 1.793      ;
; 1.070 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.477      ; 1.801      ;
; 1.077 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.477      ; 1.808      ;
; 1.090 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.081      ; 1.383      ;
; 1.091 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.383      ;
; 1.092 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.384      ;
; 1.092 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.384      ;
; 1.092 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.384      ;
; 1.093 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.385      ;
; 1.093 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.385      ;
; 1.100 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.392      ;
; 1.100 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.392      ;
; 1.100 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.392      ;
; 1.101 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.393      ;
; 1.101 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.393      ;
; 1.101 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.080      ; 1.393      ;
+-------+------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'                                                                                                                                                         ;
+-------+--------------+----------------+------------------+-------------------------------------------------------------+------------+----------------------------------------------------------------------------------------------------------------------------+
; Slack ; Actual Width ; Required Width ; Type             ; Clock                                                       ; Clock Edge ; Target                                                                                                                     ;
+-------+--------------+----------------+------------------+-------------------------------------------------------------+------------+----------------------------------------------------------------------------------------------------------------------------+
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                                           ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                                           ;
; 2.728 ; 2.948        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                                           ;
; 2.730 ; 2.950        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|address_reg_a[0]                 ;
; 2.730 ; 2.950        ; 0.220          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ;
; 2.777 ; 3.012        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19~porta_address_reg0 ;
; 2.777 ; 3.012        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2~porta_address_reg0  ;
; 2.778 ; 3.013        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19                    ;
; 2.778 ; 3.013        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2                     ;
; 2.778 ; 3.013        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ;
; 2.778 ; 3.013        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ;
; 2.779 ; 3.014        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25~porta_address_reg0 ;
; 2.779 ; 3.014        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3                     ;
; 2.779 ; 3.014        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7                     ;
; 2.780 ; 3.015        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ;
; 2.780 ; 3.015        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25                    ;
; 2.781 ; 3.016        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0~porta_address_reg0  ;
; 2.781 ; 3.016        ; 0.235          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ;
+-------+--------------+----------------+------------------+-------------------------------------------------------------+------------+----------------------------------------------------------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'clk'                                                                                                       ;
+--------+--------------+----------------+------------------+-------+------------+-----------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock ; Clock Edge ; Target                                                                ;
+--------+--------------+----------------+------------------+-------+------------+-----------------------------------------------------------------------+
; 9.931  ; 9.931        ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]           ;
; 9.931  ; 9.931        ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.953  ; 9.953        ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; clk~input|o                                                           ;
; 9.994  ; 9.994        ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; clk~input|i                                                           ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; clk~input|i                                                           ;
; 10.005 ; 10.005       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.047 ; 10.047       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; clk~input|o                                                           ;
; 10.068 ; 10.068       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]           ;
; 10.068 ; 10.068       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; clk   ; Rise       ; clk                                                                   ;
+--------+--------------+----------------+------------------+-------+------------+-----------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; Data Port    ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                             ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; clk_driver   ; clk        ; 4.933 ;       ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; IO_data[*]   ; clk        ; 6.976 ; 6.961 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[0]  ; clk        ; 6.158 ; 5.958 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[1]  ; clk        ; 6.498 ; 6.222 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[2]  ; clk        ; 5.969 ; 5.858 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[3]  ; clk        ; 6.976 ; 6.961 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[4]  ; clk        ; 6.800 ; 6.558 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[5]  ; clk        ; 6.129 ; 5.881 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[6]  ; clk        ; 5.483 ; 5.322 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[7]  ; clk        ; 6.154 ; 6.025 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[8]  ; clk        ; 6.260 ; 6.131 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[9]  ; clk        ; 5.878 ; 5.694 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[10] ; clk        ; 6.424 ; 6.325 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[11] ; clk        ; 5.516 ; 5.369 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[12] ; clk        ; 5.612 ; 5.445 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[13] ; clk        ; 5.981 ; 5.832 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; clk_driver   ; clk        ;       ; 4.892 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                        ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; Data Port    ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                             ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; clk_driver   ; clk        ; 4.369 ;       ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; IO_data[*]   ; clk        ; 4.826 ; 4.667 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[0]  ; clk        ; 5.478 ; 5.284 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[1]  ; clk        ; 5.801 ; 5.532 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[2]  ; clk        ; 5.292 ; 5.182 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[3]  ; clk        ; 6.318 ; 6.305 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[4]  ; clk        ; 6.090 ; 5.854 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[5]  ; clk        ; 5.447 ; 5.204 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[6]  ; clk        ; 4.826 ; 4.667 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[7]  ; clk        ; 5.470 ; 5.342 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[8]  ; clk        ; 5.572 ; 5.445 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[9]  ; clk        ; 5.206 ; 5.025 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[10] ; clk        ; 5.729 ; 5.630 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[11] ; clk        ; 4.858 ; 4.713 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[12] ; clk        ; 4.950 ; 4.785 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[13] ; clk        ; 5.304 ; 5.157 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; clk_driver   ; clk        ;       ; 4.331 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+


----------------------------------------------
; Slow 1200mV 85C Model Metastability Report ;
----------------------------------------------
No synchronizer chains to report.


+---------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary                                                                 ;
+------------+-----------------+-------------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                                  ; Note ;
+------------+-----------------+-------------------------------------------------------------+------+
; 151.01 MHz ; 151.01 MHz      ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;      ;
+------------+-----------------+-------------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+--------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup Summary                                                   ;
+-------------------------------------------------------------+--------+---------------+
; Clock                                                       ; Slack  ; End Point TNS ;
+-------------------------------------------------------------+--------+---------------+
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; -0.281 ; -0.678        ;
+-------------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold Summary                                                   ;
+-------------------------------------------------------------+-------+---------------+
; Clock                                                       ; Slack ; End Point TNS ;
+-------------------------------------------------------------+-------+---------------+
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.418 ; 0.000         ;
+-------------------------------------------------------------+-------+---------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+-------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary                                    ;
+-------------------------------------------------------------+-------+---------------+
; Clock                                                       ; Slack ; End Point TNS ;
+-------------------------------------------------------------+-------+---------------+
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 1.859 ; 0.000         ;
; clk                                                         ; 9.943 ; 0.000         ;
+-------------------------------------------------------------+-------+---------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'                                                                                                                                                                                                                                                                                                                                             ;
+--------+----------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                                                                                                  ; To Node                                                                                                                    ; Launch Clock                                                ; Latch Clock                                                 ; Relationship ; Clock Skew ; Data Delay ;
+--------+----------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; -0.281 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[12]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.439     ; 2.874      ;
; -0.217 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[2]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.421     ; 2.828      ;
; -0.109 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[6]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.424     ; 2.717      ;
; -0.037 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[1]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.444     ; 2.625      ;
; -0.035 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[2]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.433     ; 2.634      ;
; -0.034 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[9]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.442     ; 2.624      ;
; 0.024  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[8]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.444     ; 2.564      ;
; 0.070  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[0]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.447     ; 2.515      ;
; 0.088  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[10]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.430     ; 2.514      ;
; 0.164  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[5]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.432     ; 2.436      ;
; 0.181  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[7]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.428     ; 2.423      ;
; 0.203  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[4]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.420     ; 2.409      ;
; 0.207  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[1]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.424     ; 2.401      ;
; 0.250  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[9]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.456     ; 2.326      ;
; 0.286  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[10]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.426     ; 2.320      ;
; 0.291  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[11]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.432     ; 2.309      ;
; 0.313  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[13]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.447     ; 2.272      ;
; 0.317  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[0]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.450     ; 2.265      ;
; 0.407  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[8]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.452     ; 2.173      ;
; 0.510  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[11]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.455     ; 2.067      ;
; 0.519  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[6]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.422     ; 2.091      ;
; 0.526  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[5]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.420     ; 2.086      ;
; 0.526  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[4]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.418     ; 2.088      ;
; 0.740  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[7]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.428     ; 1.864      ;
; 0.779  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[12]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.424     ; 1.829      ;
; 0.923  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[13]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.427     ; 1.682      ;
; 0.932  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3                     ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[3]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.428     ; 1.672      ;
; 1.076  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17                    ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[3]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.428     ; 1.528      ;
; 1.602  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[2]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.308      ;
; 1.602  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[8]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.308      ;
; 1.604  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[1]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.306      ;
; 1.604  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[10]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.306      ;
; 1.605  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[11]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.305      ;
; 1.605  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[7]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.305      ;
; 1.605  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[13]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.305      ;
; 1.606  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[0]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.304      ;
; 1.606  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[4]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.304      ;
; 1.607  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[5]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.303      ;
; 1.607  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[3]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.303      ;
; 1.608  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[6]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.302      ;
; 1.608  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[12]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.302      ;
; 1.609  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[9]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.122     ; 1.301      ;
; 1.715  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.248      ; 4.632      ;
; 1.740  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.252      ; 4.611      ;
; 1.760  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.251      ; 4.590      ;
; 2.222  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.241      ; 4.118      ;
; 2.250  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.241      ; 4.090      ;
; 2.252  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.241      ; 4.088      ;
; 2.278  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.239      ; 4.060      ;
; 2.412  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.267      ; 3.954      ;
; 2.484  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.233      ; 3.848      ;
; 2.555  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.231      ; 3.775      ;
; 2.612  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.070     ; 3.380      ;
; 2.687  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.070     ; 3.305      ;
; 2.699  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.070     ; 3.293      ;
; 2.737  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.070     ; 3.255      ;
; 2.738  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.070     ; 3.254      ;
; 2.738  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.270      ; 3.631      ;
; 2.745  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.235      ; 3.589      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.758  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.107     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7                     ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.759  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.106     ; 3.119      ;
; 2.760  ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16~porta_address_reg0 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.105     ; 3.119      ;
; 2.777  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.070     ; 3.215      ;
; 2.778  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.237      ; 3.558      ;
; 2.800  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.231      ; 3.530      ;
; 2.812  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.070     ; 3.180      ;
; 2.813  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.070     ; 3.179      ;
; 2.817  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.279      ; 3.561      ;
; 2.818  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.233      ; 3.514      ;
; 2.819  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.273      ; 3.553      ;
; 2.821  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.275      ; 3.553      ;
; 2.822  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.270      ; 3.547      ;
; 2.824  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.070     ; 3.168      ;
; 2.825  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.070     ; 3.167      ;
; 2.832  ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.267      ; 3.534      ;
+--------+----------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'                                                                                                                                                                                                                                                                                                                             ;
+-------+------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                                                                                  ; To Node                                                                                                                    ; Launch Clock                                                ; Latch Clock                                                 ; Relationship ; Clock Skew ; Data Delay ;
+-------+------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; 0.418 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.684      ;
; 0.436 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.417      ; 1.083      ;
; 0.452 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.417      ; 1.099      ;
; 0.456 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.417      ; 1.103      ;
; 0.458 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.417      ; 1.105      ;
; 0.470 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.736      ;
; 0.482 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.417      ; 1.129      ;
; 0.648 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|address_reg_a[0] ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.914      ;
; 0.668 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.417      ; 1.315      ;
; 0.685 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.951      ;
; 0.685 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.951      ;
; 0.686 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.952      ;
; 0.687 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.953      ;
; 0.687 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.953      ;
; 0.687 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.953      ;
; 0.688 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.954      ;
; 0.688 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.954      ;
; 0.688 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.954      ;
; 0.690 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.956      ;
; 0.690 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.956      ;
; 0.691 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.957      ;
; 0.691 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.957      ;
; 0.691 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.957      ;
; 0.707 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.973      ;
; 0.707 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.973      ;
; 0.708 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.974      ;
; 0.708 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.974      ;
; 0.708 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.974      ;
; 0.709 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.975      ;
; 0.711 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.977      ;
; 0.712 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.978      ;
; 0.713 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.979      ;
; 0.713 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.979      ;
; 0.714 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.980      ;
; 0.714 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.980      ;
; 0.714 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 0.980      ;
; 0.729 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.416      ; 1.375      ;
; 0.742 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.411      ; 1.383      ;
; 0.748 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.416      ; 1.394      ;
; 0.756 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.416      ; 1.402      ;
; 0.761 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.411      ;
; 0.772 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.416      ; 1.418      ;
; 0.774 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.416      ; 1.420      ;
; 0.777 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.413      ; 1.420      ;
; 0.782 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.432      ;
; 0.786 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.411      ; 1.427      ;
; 0.786 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.436      ;
; 0.788 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.438      ;
; 0.788 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.438      ;
; 0.790 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.440      ;
; 0.790 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.440      ;
; 0.791 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.411      ; 1.432      ;
; 0.791 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.419      ; 1.440      ;
; 0.792 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.413      ; 1.435      ;
; 0.793 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.443      ;
; 0.793 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.443      ;
; 0.797 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.413      ; 1.440      ;
; 0.801 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.451      ;
; 0.801 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.451      ;
; 0.803 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.419      ; 1.452      ;
; 0.805 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.411      ; 1.446      ;
; 0.805 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.455      ;
; 0.808 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.458      ;
; 0.808 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.458      ;
; 0.810 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.460      ;
; 0.810 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.460      ;
; 0.812 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.419      ; 1.461      ;
; 0.814 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.464      ;
; 0.821 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.471      ;
; 0.825 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.413      ; 1.468      ;
; 0.828 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.416      ; 1.474      ;
; 0.830 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.416      ; 1.476      ;
; 0.831 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.481      ;
; 0.831 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.419      ; 1.480      ;
; 0.832 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.482      ;
; 0.835 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.413      ; 1.478      ;
; 0.845 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.495      ;
; 0.845 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.495      ;
; 0.849 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.499      ;
; 0.853 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.419      ; 1.502      ;
; 0.861 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.411      ; 1.502      ;
; 0.901 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.551      ;
; 0.953 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.603      ;
; 0.960 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.610      ;
; 0.968 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.618      ;
; 0.974 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.624      ;
; 0.987 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.420      ; 1.637      ;
; 1.005 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.072      ; 1.272      ;
; 1.006 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.272      ;
; 1.006 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.272      ;
; 1.007 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.273      ;
; 1.007 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.273      ;
; 1.007 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.273      ;
; 1.007 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.273      ;
; 1.007 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.273      ;
; 1.007 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.273      ;
; 1.008 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.274      ;
; 1.008 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.274      ;
; 1.008 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.274      ;
; 1.008 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.071      ; 1.274      ;
+-------+------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'                                                                                                                                                          ;
+-------+--------------+----------------+------------------+-------------------------------------------------------------+------------+----------------------------------------------------------------------------------------------------------------------------+
; Slack ; Actual Width ; Required Width ; Type             ; Clock                                                       ; Clock Edge ; Target                                                                                                                     ;
+-------+--------------+----------------+------------------+-------------------------------------------------------------+------------+----------------------------------------------------------------------------------------------------------------------------+
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27                    ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27~porta_address_reg0 ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9                     ;
; 1.859 ; 6.060        ; 4.201          ; Min Period       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                                           ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                                           ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                                           ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|address_reg_a[0]                 ;
; 2.728 ; 2.944        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ;
; 2.773 ; 3.003        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16~porta_address_reg0 ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[0]                                                                    ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[10]                                                                   ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[11]                                                                   ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[12]                                                                   ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[13]                                                                   ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[1]                                                                    ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[2]                                                                    ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[3]                                                                    ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[4]                                                                    ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[5]                                                                    ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[6]                                                                    ;
; 2.774 ; 2.958        ; 0.184          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[7]                                                                    ;
+-------+--------------+----------------+------------------+-------------------------------------------------------------+------------+----------------------------------------------------------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'clk'                                                                                                        ;
+--------+--------------+----------------+------------------+-------+------------+-----------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock ; Clock Edge ; Target                                                                ;
+--------+--------------+----------------+------------------+-------+------------+-----------------------------------------------------------------------+
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]           ;
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.976  ; 9.976        ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; clk~input|o                                                           ;
; 9.991  ; 9.991        ; 0.000          ; High Pulse Width ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; clk~input|i                                                           ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; clk~input|i                                                           ;
; 10.008 ; 10.008       ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.024 ; 10.024       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; clk~input|o                                                           ;
; 10.056 ; 10.056       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]           ;
; 10.056 ; 10.056       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; clk   ; Rise       ; clk                                                                   ;
+--------+--------------+----------------+------------------+-------+------------+-----------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; Data Port    ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                             ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; clk_driver   ; clk        ; 4.500 ;       ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; IO_data[*]   ; clk        ; 6.420 ; 6.253 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[0]  ; clk        ; 5.757 ; 5.401 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[1]  ; clk        ; 6.109 ; 5.658 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[2]  ; clk        ; 5.563 ; 5.336 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[3]  ; clk        ; 6.420 ; 6.253 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[4]  ; clk        ; 6.359 ; 5.978 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[5]  ; clk        ; 5.731 ; 5.359 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[6]  ; clk        ; 5.097 ; 4.862 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[7]  ; clk        ; 5.696 ; 5.517 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[8]  ; clk        ; 5.797 ; 5.614 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[9]  ; clk        ; 5.479 ; 5.195 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[10] ; clk        ; 6.003 ; 5.745 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[11] ; clk        ; 5.124 ; 4.897 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[12] ; clk        ; 5.208 ; 4.969 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[13] ; clk        ; 5.573 ; 5.319 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; clk_driver   ; clk        ;       ; 4.392 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                        ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; Data Port    ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                             ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; clk_driver   ; clk        ; 3.981 ;       ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; IO_data[*]   ; clk        ; 4.494 ; 4.265 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[0]  ; clk        ; 5.133 ; 4.789 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[1]  ; clk        ; 5.466 ; 5.029 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[2]  ; clk        ; 4.941 ; 4.720 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[3]  ; clk        ; 5.815 ; 5.655 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[4]  ; clk        ; 5.705 ; 5.337 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[5]  ; clk        ; 5.103 ; 4.742 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[6]  ; clk        ; 4.494 ; 4.265 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[7]  ; clk        ; 5.069 ; 4.894 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[8]  ; clk        ; 5.167 ; 4.988 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[9]  ; clk        ; 4.862 ; 4.586 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[10] ; clk        ; 5.363 ; 5.112 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[11] ; clk        ; 4.520 ; 4.299 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[12] ; clk        ; 4.601 ; 4.367 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[13] ; clk        ; 4.950 ; 4.704 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; clk_driver   ; clk        ;       ; 3.877 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+


---------------------------------------------
; Slow 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+-------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup Summary                                                  ;
+-------------------------------------------------------------+-------+---------------+
; Clock                                                       ; Slack ; End Point TNS ;
+-------------------------------------------------------------+-------+---------------+
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 1.337 ; 0.000         ;
+-------------------------------------------------------------+-------+---------------+


+-------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold Summary                                                   ;
+-------------------------------------------------------------+-------+---------------+
; Clock                                                       ; Slack ; End Point TNS ;
+-------------------------------------------------------------+-------+---------------+
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.168 ; 0.000         ;
+-------------------------------------------------------------+-------+---------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+-------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary                                    ;
+-------------------------------------------------------------+-------+---------------+
; Clock                                                       ; Slack ; End Point TNS ;
+-------------------------------------------------------------+-------+---------------+
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 2.778 ; 0.000         ;
; clk                                                         ; 9.589 ; 0.000         ;
+-------------------------------------------------------------+-------+---------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'                                                                                                                                                                                                                                                                                                                                ;
+-------+----------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                                                                                      ; To Node                                                                                                                    ; Launch Clock                                                ; Latch Clock                                                 ; Relationship ; Clock Skew ; Data Delay ;
+-------+----------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; 1.337 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[12]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.189     ; 1.491      ;
; 1.479 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[2]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.176     ; 1.362      ;
; 1.535 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6         ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[6]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.178     ; 1.304      ;
; 1.558 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[9]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.191     ; 1.268      ;
; 1.566 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[1]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.188     ; 1.263      ;
; 1.575 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2         ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[2]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.186     ; 1.256      ;
; 1.583 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8         ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[8]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.192     ; 1.242      ;
; 1.603 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[5]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.185     ; 1.229      ;
; 1.627 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[10]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.184     ; 1.206      ;
; 1.627 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0         ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[0]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.190     ; 1.200      ;
; 1.643 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4         ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[4]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.175     ; 1.199      ;
; 1.659 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[10]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.180     ; 1.178      ;
; 1.670 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[7]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.183     ; 1.164      ;
; 1.675 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9         ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[9]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.199     ; 1.143      ;
; 1.680 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1         ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[1]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.178     ; 1.159      ;
; 1.734 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[11]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.185     ; 1.098      ;
; 1.739 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[0]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.193     ; 1.085      ;
; 1.743 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[13]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.190     ; 1.084      ;
; 1.799 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[8]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.195     ; 1.023      ;
; 1.823 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5         ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[5]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.175     ; 1.019      ;
; 1.859 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[11]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.198     ; 0.960      ;
; 1.859 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[4]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.173     ; 0.985      ;
; 1.862 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[6]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.177     ; 0.978      ;
; 1.961 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7         ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[7]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.183     ; 0.873      ;
; 1.979 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[12]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.179     ; 0.859      ;
; 2.053 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[13]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.181     ; 0.783      ;
; 2.059 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3         ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[3]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.182     ; 0.776      ;
; 2.133 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17        ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[3]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.182     ; 0.702      ;
; 2.370 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[1]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.630      ;
; 2.370 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[10]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.630      ;
; 2.371 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[11]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.629      ;
; 2.372 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[8]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.628      ;
; 2.372 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[13]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.628      ;
; 2.373 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[0]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.627      ;
; 2.373 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[2]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.627      ;
; 2.374 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[3]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.626      ;
; 2.375 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[9]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.625      ;
; 2.376 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[4]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.624      ;
; 2.376 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[6]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.624      ;
; 2.377 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[12]                                                                   ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.623      ;
; 2.377 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[7]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.623      ;
; 2.378 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0] ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[5]                                                                    ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 3.030        ; -0.017     ; 0.622      ;
; 3.745 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.120      ; 2.444      ;
; 3.771 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.123      ; 2.421      ;
; 3.776 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.122      ; 2.415      ;
; 4.050 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.116      ; 2.135      ;
; 4.064 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.117      ; 2.122      ;
; 4.077 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.116      ; 2.108      ;
; 4.095 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.115      ; 2.089      ;
; 4.152 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.128      ; 2.045      ;
; 4.305 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.109      ; 1.873      ;
; 4.308 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.704      ;
; 4.309 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.131      ; 1.891      ;
; 4.334 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.107      ; 1.842      ;
; 4.349 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.139      ; 1.859      ;
; 4.353 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.659      ;
; 4.355 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.111      ; 1.825      ;
; 4.371 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.114      ; 1.812      ;
; 4.372 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.640      ;
; 4.376 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.636      ;
; 4.377 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.635      ;
; 4.383 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.629      ;
; 4.386 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.133      ; 1.816      ;
; 4.388 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.136      ; 1.817      ;
; 4.391 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.131      ; 1.809      ;
; 4.395 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.109      ; 1.783      ;
; 4.395 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.107      ; 1.781      ;
; 4.399 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.128      ; 1.798      ;
; 4.418 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.111      ; 1.762      ;
; 4.420 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.114      ; 1.763      ;
; 4.421 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.591      ;
; 4.421 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.591      ;
; 4.440 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.572      ;
; 4.441 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.571      ;
; 4.441 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.571      ;
; 4.444 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.568      ;
; 4.445 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.567      ;
; 4.450 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.562      ;
; 4.451 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.561      ;
; 4.457 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.109      ; 1.721      ;
; 4.465 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.116      ; 1.720      ;
; 4.473 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.118      ; 1.714      ;
; 4.489 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.523      ;
; 4.489 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.523      ;
; 4.490 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.522      ;
; 4.490 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.116      ; 1.695      ;
; 4.490 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.120      ; 1.699      ;
; 4.505 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.507      ;
; 4.506 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.114      ; 1.677      ;
; 4.508 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.504      ;
; 4.509 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                              ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.503      ;
; 4.509 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.503      ;
; 4.509 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.503      ;
; 4.512 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.500      ;
; 4.513 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.499      ;
; 4.518 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.494      ;
; 4.518 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.109      ; 1.660      ;
; 4.519 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.493      ;
; 4.519 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                               ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; -0.035     ; 1.493      ;
; 4.520 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                              ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 6.060        ; 0.131      ; 1.680      ;
+-------+----------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'                                                                                                                                                                                                                                                                                                                             ;
+-------+------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                                                                                  ; To Node                                                                                                                    ; Launch Clock                                                ; Latch Clock                                                 ; Relationship ; Clock Skew ; Data Delay ;
+-------+------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+
; 0.168 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.218      ; 0.490      ;
; 0.173 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.218      ; 0.495      ;
; 0.175 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.218      ; 0.497      ;
; 0.178 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.218      ; 0.500      ;
; 0.186 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.218      ; 0.508      ;
; 0.194 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.314      ;
; 0.205 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.324      ;
; 0.270 ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|address_reg_a[0] ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|out_address_reg_a[0]             ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.389      ;
; 0.281 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.218      ; 0.603      ;
; 0.293 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.413      ;
; 0.294 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.414      ;
; 0.295 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.414      ;
; 0.295 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.415      ;
; 0.296 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.416      ;
; 0.306 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.425      ;
; 0.306 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.425      ;
; 0.306 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.425      ;
; 0.306 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.425      ;
; 0.306 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.425      ;
; 0.307 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.426      ;
; 0.307 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.426      ;
; 0.307 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.426      ;
; 0.308 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.427      ;
; 0.308 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.427      ;
; 0.308 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.427      ;
; 0.309 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.428      ;
; 0.309 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.035      ; 0.428      ;
; 0.313 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.217      ; 0.634      ;
; 0.322 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.217      ; 0.643      ;
; 0.326 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.212      ; 0.642      ;
; 0.326 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.217      ; 0.647      ;
; 0.329 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.654      ;
; 0.330 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.217      ; 0.651      ;
; 0.330 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.655      ;
; 0.332 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.222      ; 0.658      ;
; 0.334 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.222      ; 0.660      ;
; 0.335 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.217      ; 0.656      ;
; 0.337 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.222      ; 0.663      ;
; 0.339 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.664      ;
; 0.339 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.664      ;
; 0.341 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.222      ; 0.667      ;
; 0.341 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.222      ; 0.667      ;
; 0.341 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.222      ; 0.667      ;
; 0.341 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.220      ; 0.665      ;
; 0.342 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.214      ; 0.660      ;
; 0.344 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.669      ;
; 0.346 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.212      ; 0.662      ;
; 0.347 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.212      ; 0.663      ;
; 0.347 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.214      ; 0.665      ;
; 0.347 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.220      ; 0.671      ;
; 0.348 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.673      ;
; 0.348 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.673      ;
; 0.353 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.217      ; 0.674      ;
; 0.353 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.678      ;
; 0.354 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.212      ; 0.670      ;
; 0.354 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.214      ; 0.672      ;
; 0.354 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.679      ;
; 0.355 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.680      ;
; 0.358 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.217      ; 0.679      ;
; 0.359 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.684      ;
; 0.359 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.222      ; 0.685      ;
; 0.361 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.686      ;
; 0.362 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.222      ; 0.688      ;
; 0.363 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.688      ;
; 0.364 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.220      ; 0.688      ;
; 0.365 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.220      ; 0.689      ;
; 0.366 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.214      ; 0.684      ;
; 0.366 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.691      ;
; 0.369 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.214      ; 0.687      ;
; 0.371 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.696      ;
; 0.378 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.220      ; 0.702      ;
; 0.384 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.212      ; 0.700      ;
; 0.391 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.716      ;
; 0.424 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.749      ;
; 0.429 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.754      ;
; 0.433 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.758      ;
; 0.437 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.762      ;
; 0.440 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.765      ;
; 0.441 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.561      ;
; 0.442 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.562      ;
; 0.442 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.562      ;
; 0.442 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.562      ;
; 0.442 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.562      ;
; 0.443 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.563      ;
; 0.443 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.563      ;
; 0.448 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                          ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.221      ; 0.773      ;
; 0.452 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.572      ;
; 0.453 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.573      ;
; 0.453 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                          ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.573      ;
; 0.453 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.573      ;
; 0.453 ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                           ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                                          ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 0.000        ; 0.036      ; 0.573      ;
+-------+------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------+-------------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]'                                                                                                                                                          ;
+-------+--------------+----------------+------------------+-------------------------------------------------------------+------------+----------------------------------------------------------------------------------------------------------------------------+
; Slack ; Actual Width ; Required Width ; Type             ; Clock                                                       ; Clock Edge ; Target                                                                                                                     ;
+-------+--------------+----------------+------------------+-------------------------------------------------------------+------------+----------------------------------------------------------------------------------------------------------------------------+
; 2.778 ; 3.008        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10~porta_address_reg0 ;
; 2.778 ; 3.008        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20~porta_address_reg0 ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a10                    ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13~porta_address_reg0 ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15~porta_address_reg0 ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16~porta_address_reg0 ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19~porta_address_reg0 ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1~porta_address_reg0  ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a20                    ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22~porta_address_reg0 ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23~porta_address_reg0 ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26~porta_address_reg0 ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4~porta_address_reg0  ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5~porta_address_reg0  ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6~porta_address_reg0  ;
; 2.779 ; 3.009        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9~porta_address_reg0  ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0~porta_address_reg0  ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a1                     ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11~porta_address_reg0 ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12~porta_address_reg0 ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a13                    ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14~porta_address_reg0 ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a15                    ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a16                    ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17~porta_address_reg0 ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18~porta_address_reg0 ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a19                    ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21~porta_address_reg0 ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a22                    ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a23                    ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24~porta_address_reg0 ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25~porta_address_reg0 ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a26                    ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27~porta_address_reg0 ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2~porta_address_reg0  ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3~porta_address_reg0  ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a4                     ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a5                     ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a6                     ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8~porta_address_reg0  ;
; 2.780 ; 3.010        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a9                     ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a0                     ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a11                    ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a12                    ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a14                    ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a17                    ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a18                    ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a2                     ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a21                    ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a24                    ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a25                    ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a27                    ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a3                     ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7~porta_address_reg0  ;
; 2.781 ; 3.011        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a8                     ;
; 2.782 ; 3.012        ; 0.230          ; Low Pulse Width  ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|ram_block1a7                     ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[0]                                                                    ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[10]                                                                   ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[11]                                                                   ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[12]                                                                   ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[13]                                                                   ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[1]                                                                    ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[2]                                                                    ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[3]                                                                    ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[4]                                                                    ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[5]                                                                    ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[6]                                                                    ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[7]                                                                    ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[8]                                                                    ;
; 2.802 ; 3.018        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Fall       ; DAC904_WriteModule:U3_DAC904_WriteModule|rOutputData[9]                                                                    ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[10]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[11]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[12]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[13]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[14]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[15]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[16]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[17]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[18]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[19]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[20]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[21]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[22]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[23]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[24]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[25]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[26]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[27]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[28]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[29]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[30]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[31]                                                                          ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[3]                                                                           ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[4]                                                                           ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[5]                                                                           ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[6]                                                                           ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[7]                                                                           ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[8]                                                                           ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; PhaseAccumulator:U1_PhaseAccumulator|PhaseSum[9]                                                                           ;
; 2.814 ; 3.030        ; 0.216          ; High Pulse Width ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; Rise       ; rom_14B_Sin:U2_rom_14B_Sin|altsyncram:altsyncram_component|altsyncram_vhb1:auto_generated|address_reg_a[0]                 ;
+-------+--------------+----------------+------------------+-------------------------------------------------------------+------------+----------------------------------------------------------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'clk'                                                                                                        ;
+--------+--------------+----------------+------------------+-------+------------+-----------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock ; Clock Edge ; Target                                                                ;
+--------+--------------+----------------+------------------+-------+------------+-----------------------------------------------------------------------+
; 9.589  ; 9.589        ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]           ;
; 9.589  ; 9.589        ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.621  ; 9.621        ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; clk~input|o                                                           ;
; 9.632  ; 9.632        ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; clk~input|i                                                           ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; clk   ; Rise       ; clk~input|i                                                           ;
; 10.367 ; 10.367       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.379 ; 10.379       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; clk~input|o                                                           ;
; 10.408 ; 10.408       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]           ;
; 10.408 ; 10.408       ; 0.000          ; High Pulse Width ; clk   ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; clk   ; Rise       ; clk                                                                   ;
+--------+--------------+----------------+------------------+-------+------------+-----------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; Data Port    ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                             ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; clk_driver   ; clk        ; 2.525 ;       ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; IO_data[*]   ; clk        ; 3.440 ; 3.630 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[0]  ; clk        ; 2.845 ; 2.930 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[1]  ; clk        ; 2.904 ; 3.036 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[2]  ; clk        ; 2.719 ; 2.839 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[3]  ; clk        ; 3.440 ; 3.630 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[4]  ; clk        ; 3.070 ; 3.238 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[5]  ; clk        ; 2.759 ; 2.861 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[6]  ; clk        ; 2.497 ; 2.572 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[7]  ; clk        ; 2.856 ; 3.002 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[8]  ; clk        ; 2.916 ; 3.068 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[9]  ; clk        ; 2.685 ; 2.778 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[10] ; clk        ; 2.917 ; 3.072 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[11] ; clk        ; 2.509 ; 2.590 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[12] ; clk        ; 2.541 ; 2.627 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[13] ; clk        ; 2.721 ; 2.834 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; clk_driver   ; clk        ;       ; 2.638 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                        ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; Data Port    ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                             ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; clk_driver   ; clk        ; 2.258 ;       ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; IO_data[*]   ; clk        ; 2.187 ; 2.258 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[0]  ; clk        ; 2.529 ; 2.610 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[1]  ; clk        ; 2.577 ; 2.703 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[2]  ; clk        ; 2.399 ; 2.515 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[3]  ; clk        ; 3.129 ; 3.316 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[4]  ; clk        ; 2.737 ; 2.897 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[5]  ; clk        ; 2.438 ; 2.536 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[6]  ; clk        ; 2.187 ; 2.258 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[7]  ; clk        ; 2.531 ; 2.671 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[8]  ; clk        ; 2.589 ; 2.735 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[9]  ; clk        ; 2.368 ; 2.457 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[10] ; clk        ; 2.590 ; 2.738 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[11] ; clk        ; 2.198 ; 2.275 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[12] ; clk        ; 2.229 ; 2.311 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[13] ; clk        ; 2.402 ; 2.509 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; clk_driver   ; clk        ;       ; 2.370 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+


---------------------------------------------
; Fast 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                                                                      ;
+--------------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Clock                                                        ; Setup  ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+--------------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Worst-case Slack                                             ; -0.485 ; 0.168 ; N/A      ; N/A     ; 1.859               ;
;  U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; -0.485 ; 0.168 ; N/A      ; N/A     ; 1.859               ;
;  clk                                                         ; N/A    ; N/A   ; N/A      ; N/A     ; 9.589               ;
; Design-wide TNS                                              ; -1.974 ; 0.0   ; 0.0      ; 0.0     ; 0.0                 ;
;  U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; -1.974 ; 0.000 ; N/A      ; N/A     ; 0.000               ;
;  clk                                                         ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
+--------------------------------------------------------------+--------+-------+----------+---------+---------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; Data Port    ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                             ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; clk_driver   ; clk        ; 4.933 ;       ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; IO_data[*]   ; clk        ; 6.976 ; 6.961 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[0]  ; clk        ; 6.158 ; 5.958 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[1]  ; clk        ; 6.498 ; 6.222 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[2]  ; clk        ; 5.969 ; 5.858 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[3]  ; clk        ; 6.976 ; 6.961 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[4]  ; clk        ; 6.800 ; 6.558 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[5]  ; clk        ; 6.129 ; 5.881 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[6]  ; clk        ; 5.483 ; 5.322 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[7]  ; clk        ; 6.154 ; 6.025 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[8]  ; clk        ; 6.260 ; 6.131 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[9]  ; clk        ; 5.878 ; 5.694 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[10] ; clk        ; 6.424 ; 6.325 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[11] ; clk        ; 5.516 ; 5.369 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[12] ; clk        ; 5.612 ; 5.445 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[13] ; clk        ; 5.981 ; 5.832 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; clk_driver   ; clk        ;       ; 4.892 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                        ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; Data Port    ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                             ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+
; clk_driver   ; clk        ; 2.258 ;       ; Rise       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; IO_data[*]   ; clk        ; 2.187 ; 2.258 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[0]  ; clk        ; 2.529 ; 2.610 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[1]  ; clk        ; 2.577 ; 2.703 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[2]  ; clk        ; 2.399 ; 2.515 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[3]  ; clk        ; 3.129 ; 3.316 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[4]  ; clk        ; 2.737 ; 2.897 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[5]  ; clk        ; 2.438 ; 2.536 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[6]  ; clk        ; 2.187 ; 2.258 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[7]  ; clk        ; 2.531 ; 2.671 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[8]  ; clk        ; 2.589 ; 2.735 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[9]  ; clk        ; 2.368 ; 2.457 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[10] ; clk        ; 2.590 ; 2.738 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[11] ; clk        ; 2.198 ; 2.275 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[12] ; clk        ; 2.229 ; 2.311 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
;  IO_data[13] ; clk        ; 2.402 ; 2.509 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
; clk_driver   ; clk        ;       ; 2.370 ; Fall       ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ;
+--------------+------------+-------+-------+------------+-------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                    ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin           ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; clk_driver    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[0]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[1]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[2]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[3]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[4]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[5]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[6]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[7]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[8]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[9]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[10]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[11]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[12]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; IO_data[13]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; rstn                    ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; clk                     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; clk_driver    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.33 V              ; -0.00402 V          ; 0.17 V                               ; 0.066 V                              ; 3.12e-09 s                  ; 2.97e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.33 V             ; -0.00402 V         ; 0.17 V                              ; 0.066 V                             ; 3.12e-09 s                 ; 2.97e-09 s                 ; Yes                       ; Yes                       ;
; IO_data[0]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.13e-09 V                   ; 2.35 V              ; -0.0129 V           ; 0.191 V                              ; 0.049 V                              ; 7.21e-10 s                  ; 7.09e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.13e-09 V                  ; 2.35 V             ; -0.0129 V          ; 0.191 V                             ; 0.049 V                             ; 7.21e-10 s                 ; 7.09e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[1]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[2]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[3]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.33 V              ; -0.00402 V          ; 0.17 V                               ; 0.066 V                              ; 3.12e-09 s                  ; 2.97e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.33 V             ; -0.00402 V         ; 0.17 V                              ; 0.066 V                             ; 3.12e-09 s                 ; 2.97e-09 s                 ; Yes                       ; Yes                       ;
; IO_data[4]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[5]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[6]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[7]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[8]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[9]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[10]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[11]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[12]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[13]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.86e-09 V                   ; 2.37 V              ; -0.0148 V           ; 0.107 V                              ; 0.037 V                              ; 4.37e-10 s                  ; 4.02e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.86e-09 V                  ; 2.37 V             ; -0.0148 V          ; 0.107 V                             ; 0.037 V                             ; 4.37e-10 s                 ; 4.02e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.51e-09 V                   ; 2.37 V              ; -0.0161 V           ; 0.162 V                              ; 0.02 V                               ; 4.95e-10 s                  ; 4.49e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.51e-09 V                  ; 2.37 V             ; -0.0161 V          ; 0.162 V                             ; 0.02 V                              ; 4.95e-10 s                 ; 4.49e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.83e-09 V                   ; 2.35 V              ; -0.00181 V          ; 0.151 V                              ; 0.007 V                              ; 6.94e-10 s                  ; 8.74e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.83e-09 V                  ; 2.35 V             ; -0.00181 V         ; 0.151 V                             ; 0.007 V                             ; 6.94e-10 s                 ; 8.74e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; clk_driver    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.33 V              ; -0.00197 V          ; 0.075 V                              ; 0.051 V                              ; 3.78e-09 s                  ; 3.6e-09 s                   ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.33 V             ; -0.00197 V         ; 0.075 V                             ; 0.051 V                             ; 3.78e-09 s                 ; 3.6e-09 s                  ; Yes                       ; Yes                       ;
; IO_data[0]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.95e-07 V                   ; 2.34 V              ; -0.00547 V          ; 0.075 V                              ; 0.023 V                              ; 9.12e-10 s                  ; 8.83e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.95e-07 V                  ; 2.34 V             ; -0.00547 V         ; 0.075 V                             ; 0.023 V                             ; 9.12e-10 s                 ; 8.83e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[1]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[2]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[3]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.33 V              ; -0.00197 V          ; 0.075 V                              ; 0.051 V                              ; 3.78e-09 s                  ; 3.6e-09 s                   ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.33 V             ; -0.00197 V         ; 0.075 V                             ; 0.051 V                             ; 3.78e-09 s                 ; 3.6e-09 s                  ; Yes                       ; Yes                       ;
; IO_data[4]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[5]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[6]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[7]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[8]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[9]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[10]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[11]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[12]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[13]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.89e-07 V                   ; 2.35 V              ; -0.000666 V         ; 0.058 V                              ; 0.002 V                              ; 4.86e-10 s                  ; 4.98e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.89e-07 V                  ; 2.35 V             ; -0.000666 V        ; 0.058 V                             ; 0.002 V                             ; 4.86e-10 s                 ; 4.98e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.53e-07 V                   ; 2.34 V              ; -0.008 V            ; 0.104 V                              ; 0.015 V                              ; 6.53e-10 s                  ; 5.55e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.53e-07 V                  ; 2.34 V             ; -0.008 V           ; 0.104 V                             ; 0.015 V                             ; 6.53e-10 s                 ; 5.55e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.51e-07 V                   ; 2.34 V              ; -0.0032 V           ; 0.057 V                              ; 0.016 V                              ; 8.65e-10 s                  ; 1.08e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.51e-07 V                  ; 2.34 V             ; -0.0032 V          ; 0.057 V                             ; 0.016 V                             ; 8.65e-10 s                 ; 1.08e-09 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; clk_driver    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.64 V              ; -0.0111 V           ; 0.21 V                               ; 0.135 V                              ; 2.38e-09 s                  ; 2.28e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.64 V             ; -0.0111 V          ; 0.21 V                              ; 0.135 V                             ; 2.38e-09 s                 ; 2.28e-09 s                 ; No                        ; Yes                       ;
; IO_data[0]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.78e-08 V                   ; 2.71 V              ; -0.0338 V           ; 0.25 V                               ; 0.066 V                              ; 4.98e-10 s                  ; 5.2e-10 s                   ; No                         ; Yes                        ; 2.62 V                      ; 2.78e-08 V                  ; 2.71 V             ; -0.0338 V          ; 0.25 V                              ; 0.066 V                             ; 4.98e-10 s                 ; 5.2e-10 s                  ; No                        ; Yes                       ;
; IO_data[1]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[2]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[3]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.64 V              ; -0.0111 V           ; 0.21 V                               ; 0.135 V                              ; 2.38e-09 s                  ; 2.28e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.64 V             ; -0.0111 V          ; 0.21 V                              ; 0.135 V                             ; 2.38e-09 s                 ; 2.28e-09 s                 ; No                        ; Yes                       ;
; IO_data[4]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[5]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[6]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[7]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[8]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[9]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[10]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[11]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[12]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; IO_data[13]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.11e-08 V                   ; 2.72 V              ; -0.0549 V           ; 0.19 V                               ; 0.12 V                               ; 2.69e-10 s                  ; 2.76e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.11e-08 V                  ; 2.72 V             ; -0.0549 V          ; 0.19 V                              ; 0.12 V                              ; 2.69e-10 s                 ; 2.76e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.19e-08 V                   ; 2.71 V              ; -0.0718 V           ; 0.277 V                              ; 0.167 V                              ; 3.12e-10 s                  ; 3.02e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.19e-08 V                  ; 2.71 V             ; -0.0718 V          ; 0.277 V                             ; 0.167 V                             ; 3.12e-10 s                 ; 3.02e-10 s                 ; No                        ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 3.52e-08 V                   ; 2.7 V               ; -0.0198 V           ; 0.2 V                                ; 0.049 V                              ; 4.85e-10 s                  ; 6.73e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 3.52e-08 V                  ; 2.7 V              ; -0.0198 V          ; 0.2 V                               ; 0.049 V                             ; 4.85e-10 s                 ; 6.73e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Setup Transfers                                                                                                                                                       ;
+-------------------------------------------------------------+-------------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                                  ; To Clock                                                    ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------------+-------------------------------------------------------------+----------+----------+----------+----------+
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 857      ; 0        ; 42       ; 0        ;
+-------------------------------------------------------------+-------------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Hold Transfers                                                                                                                                                        ;
+-------------------------------------------------------------+-------------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                                  ; To Clock                                                    ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------------+-------------------------------------------------------------+----------+----------+----------+----------+
; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] ; 857      ; 0        ; 42       ; 0        ;
+-------------------------------------------------------------+-------------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths                            ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 0     ; 0    ;
; Unconstrained Input Ports       ; 1     ; 1    ;
; Unconstrained Input Port Paths  ; 43    ; 43   ;
; Unconstrained Output Ports      ; 15    ; 15   ;
; Unconstrained Output Port Paths ; 15    ; 15   ;
+---------------------------------+-------+------+


+------------------------------------+
; TimeQuest Timing Analyzer Messages ;
+------------------------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit TimeQuest Timing Analyzer
    Info: Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version
    Info: Processing started: Tue Jul 25 11:30:37 2023
Info: Command: quartus_sta DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC
Info: qsta_default_script.tcl version: #1
Info: Parallel compilation is enabled and will use 6 of the 6 processors detected
Info: Low junction temperature is 0 degrees C
Info: High junction temperature is 85 degrees C
Critical Warning: Synopsys Design Constraints File file not found: 'DAC904_HighSpeedDAC.sdc'. A Synopsys Design Constraints File is required by the TimeQuest Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design.
Info: No user constrained generated clocks found in the design. Calling "derive_pll_clocks -create_base_clocks"
Info: Deriving PLL Clocks
    Info: create_clock -period 20.000 -waveform {0.000 10.000} -name clk clk
    Info: create_generated_clock -source {U0_pll165m_inst|altpll_component|auto_generated|pll1|inclk[0]} -divide_by 10 -multiply_by 33 -duty_cycle 50.00 -name {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]} {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}
Info: No user constrained base clocks found in the design. Calling "derive_clocks -period 1.0"
Info: The command derive_clocks did not find any clocks to derive.  No clocks were created or changed.
Info: No user constrained clock uncertainty found in the design. Calling "derive_clock_uncertainty"
Info: Deriving Clock Uncertainty
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
Info: Analyzing Slow 1200mV 85C Model
Critical Warning: Timing requirements not met
Info: Worst-case setup slack is -0.485
    Info:     Slack End Point TNS Clock 
    Info: ========= ============= =====================
    Info:    -0.485        -1.974 U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] 
Info: Worst-case hold slack is 0.463
    Info:     Slack End Point TNS Clock 
    Info: ========= ============= =====================
    Info:     0.463         0.000 U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] 
Info: No Recovery paths to report
Info: No Removal paths to report
Info: Worst-case minimum pulse width slack is 1.859
    Info:     Slack End Point TNS Clock 
    Info: ========= ============= =====================
    Info:     1.859         0.000 U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] 
    Info:     9.931         0.000 clk 
Info: Analyzing Slow 1200mV 0C Model
Info: Started post-fitting delay annotation
Info: Delay annotation completed successfully
Info: Deriving Clock Uncertainty
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
Critical Warning: Timing requirements not met
Info: Worst-case setup slack is -0.281
    Info:     Slack End Point TNS Clock 
    Info: ========= ============= =====================
    Info:    -0.281        -0.678 U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] 
Info: Worst-case hold slack is 0.418
    Info:     Slack End Point TNS Clock 
    Info: ========= ============= =====================
    Info:     0.418         0.000 U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] 
Info: No Recovery paths to report
Info: No Removal paths to report
Info: Worst-case minimum pulse width slack is 1.859
    Info:     Slack End Point TNS Clock 
    Info: ========= ============= =====================
    Info:     1.859         0.000 U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] 
    Info:     9.943         0.000 clk 
Info: Analyzing Fast 1200mV 0C Model
Info: Started post-fitting delay annotation
Info: Delay annotation completed successfully
Info: Deriving Clock Uncertainty
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -setup 0.020
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
    Info: set_clock_uncertainty -rise_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -rise_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
    Info: set_clock_uncertainty -fall_from [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -fall_to [get_clocks {U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1]}] -hold 0.020
Info: Worst-case setup slack is 1.337
    Info:     Slack End Point TNS Clock 
    Info: ========= ============= =====================
    Info:     1.337         0.000 U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] 
Info: Worst-case hold slack is 0.168
    Info:     Slack End Point TNS Clock 
    Info: ========= ============= =====================
    Info:     0.168         0.000 U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] 
Info: No Recovery paths to report
Info: No Removal paths to report
Info: Worst-case minimum pulse width slack is 2.778
    Info:     Slack End Point TNS Clock 
    Info: ========= ============= =====================
    Info:     2.778         0.000 U0_pll165m_inst|altpll_component|auto_generated|pll1|clk[1] 
    Info:     9.589         0.000 clk 
Info: Design is not fully constrained for setup requirements
Info: Design is not fully constrained for hold requirements
Info: Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 3 warnings
    Info: Peak virtual memory: 4537 megabytes
    Info: Processing ended: Tue Jul 25 11:30:39 2023
    Info: Elapsed time: 00:00:02
    Info: Total CPU time (on all processors): 00:00:02


