/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> 第二问 正弦信号发生器
  * @version V1.0
  * @date    2024
  * @brief   STM32F4控制DAC8552产生正弦信号
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
// #include "../Modules/Core/usart.h"  // 注释掉串口，只用示波器观察
#include "bsp.h"

// 波形输出模块已清除，为DAC904模块预留
// #include "../Modules/Generation/ad9834_highperf.h"  // 已清除AD9834模块
// #include "../Modules/Generation/dac8552.h"          // 已清除DAC8552模块
// #include "../Modules/Generation/dds_wavegen.h"      // 已清除内置DDS模块
// 注意：AD9851模块保留在第二问项目中作为备用方案

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// AD9834配置变量 (替代原有DDS系统)
// 注释掉未使用的变量，避免编译警告
// static uint32_t current_frequency = 3000000;  // 当前频率 (默认3MHz)
// static uint16_t current_wave_type = SINE_WAVE; // 当前波形 (默认正弦波)

// AD9834技术优势：
// - 专业DDS芯片，75MHz系统时钟
// - 28位频率分辨率 (0.028Hz精度)
// - 12位相位分辨率 (0.088°精度)
// - 1Hz-5MHz频率范围，远超内置DAC方案
// 所有表都使用内存对齐优化，支持硬件加速访问

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void DDS_HighPerf_Init(void);
// 移除不需要的函数声明，专注于稳定输出

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    // USART1_Init(115200);  // 注释掉串口初始化
    BSP_Init();

    /* ==================== 系统就绪，等待DAC904模块集成 ==================== */
    // 所有波形输出模块已清除，GPIO引脚资源已释放
    // 系统已为DAC904模块集成做好准备

    // LED慢闪表示系统就绪，等待DAC904模块
    for (int i = 0; i < 3; i++) {
        GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
        Delay_ms(500);  // 慢闪表示等待状态
    }

    /* ==================== 系统清理完成，等待DAC904集成 ==================== */
    // 所有波形输出模块已清除：AD9834、DAC8552、内置DDS
    // GPIO引脚资源已释放，为DAC904模块预留
    // AD9851模块保留在第二问项目中作为备用方案

    /* 主循环 - 系统空闲模式 */
    uint32_t led_counter = 0;

    while (1)
    {
        led_counter++;

        // ==================== 系统状态指示 ====================
        // LED慢闪表示系统正常运行，等待DAC904模块集成
        if (led_counter % 1000000 == 0) {  // 慢闪
            if (GPIO_ReadOutputDataBit(GPIOE, GPIO_Pin_6)) {
                GPIO_ResetBits(GPIOE, GPIO_Pin_6);
            } else {
                GPIO_SetBits(GPIOE, GPIO_Pin_6);
            }
        }

        // ==================== 低功耗模式 ====================
        // 系统进入低功耗状态，等待DAC904模块集成
        __WFI();  // 等待中断，CPU进入睡眠
    }
}

// ==================== 系统清理完成说明 ====================
//
// 波形输出模块清理状态：
// ✓ AD9834模块：已完全移除
// ✓ DAC8552模块：已完全移除
// ✓ 内置DDS模块：已完全移除
// ✓ AD9851模块：保留在第二问项目中作为备用
//
// 释放的GPIO引脚资源：
// PA3, PA4, PA5, PA6 (原AD9834 SPI接口)
// PB0, PB1 (原AD9834控制引脚)
// SPI1, SPI2接口 (原DAC通信接口)
// 多个定时器和DMA通道 (原内置DDS)
//
// 为DAC904模块预留的资源：
// - 完整的SPI接口 (SPI1/SPI2可选)
// - 丰富的GPIO引脚 (PA, PB, PC端口)
// - 定时器资源 (TIM2-TIM14可选)
// - DMA通道 (多个通道可选)
// - ADC接口 (如需反馈控制)
//
// 系统当前状态：基础系统正常运行，等待DAC904模块集成








/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


