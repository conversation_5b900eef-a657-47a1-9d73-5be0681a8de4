/**
  ******************************************************************************
  * @file    dac904_simple.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   DAC904简化版驱动头文件
  ******************************************************************************
  */

#ifndef __DAC904_SIMPLE_H
#define __DAC904_SIMPLE_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "bsp.h"

/* Exported constants --------------------------------------------------------*/
#define DAC904_DATA_MAX             16383       ///< 14位最大值
#define DAC904_DATA_MID             8192        ///< 14位中间值
#define DAC904_DATA_MIN             0           ///< 14位最小值

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  DAC904简化初始化
  * @param  None
  * @retval None
  */
void DAC904_Simple_Init(void);

/**
  * @brief  DAC904写入数据
  * @param  data: 14位数据值 (0-16383)
  * @retval None
  */
void DAC904_Simple_WriteData(uint16_t data);

/**
  * @brief  DAC904设置电压
  * @param  voltage: 电压值 (0.0-5.0V)
  * @retval None
  */
void DAC904_Simple_SetVoltage(float voltage);

/**
  * @brief  DAC904测试函数
  * @param  None
  * @retval None
  */
void DAC904_Simple_Test(void);

#ifdef __cplusplus
}
#endif

#endif /* __DAC904_SIMPLE_H */

/************************ (C) COPYRIGHT DAC904移植项目组 *****END OF FILE****/
