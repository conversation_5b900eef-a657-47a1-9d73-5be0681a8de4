/**
  ******************************************************************************
  * @file    dac904.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   DAC904高速DAC模块驱动头文件
  ******************************************************************************
  * @attention
  *
  * DAC904特性：
  * - 14位分辨率
  * - 最高165MHz采样率
  * - 并行数据接口
  * - 单电源5V供电
  * - 差分电流输出
  *
  ******************************************************************************
  */

#ifndef __DAC904_H
#define __DAC904_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "bsp.h"
#include "dac904_types.h"

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  DAC904状态枚举
  */
typedef enum {
    DAC904_OK = 0,                  ///< 操作成功
    DAC904_ERROR,                   ///< 一般错误
    DAC904_PARAM_ERROR,             ///< 参数错误
    DAC904_TIMEOUT_ERROR,           ///< 超时错误
    DAC904_BUSY_ERROR               ///< 设备忙错误
} DAC904_Status_t;

/**
  * @brief  DAC904工作模式枚举
  */
typedef enum {
    DAC904_MODE_MANUAL = 0,         ///< 手动模式
    DAC904_MODE_TIMER,              ///< 定时器模式
    DAC904_MODE_DMA                 ///< DMA模式
} DAC904_Mode_t;

/**
  * @brief  DAC904配置结构体
  */
typedef struct {
    DAC904_Mode_t mode;             ///< 工作模式
    uint32_t clock_frequency;       ///< 时钟频率 (Hz)
    uint32_t gpio_speed;            ///< GPIO速度配置
    bool enable_clock_output;       ///< 是否使能时钟输出
} DAC904_Config_t;

/**
  * @brief  DAC904句柄结构体
  */
typedef struct {
    DAC904_Config_t config;         ///< 配置参数
    TIM_TypeDef* timer;             ///< 定时器实例
    bool is_initialized;            ///< 初始化状态
    bool is_running;                ///< 运行状态
    uint32_t error_count;           ///< 错误计数
} DAC904_Handle_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup DAC904_Clock_Frequencies 时钟频率预定义
  * @{
  */
#define DAC904_CLOCK_22K3HZ         22300       ///< 22.3kHz (兼容现有项目)
#define DAC904_CLOCK_100KHZ         100000      ///< 100kHz
#define DAC904_CLOCK_1MHZ           1000000     ///< 1MHz
#define DAC904_CLOCK_10MHZ          10000000    ///< 10MHz
#define DAC904_CLOCK_MAX            165000000   ///< 最大165MHz
/**
  * @}
  */

/** @defgroup DAC904_GPIO_Speed GPIO速度配置
  * @{
  */
#define DAC904_GPIO_SPEED_LOW       GPIO_Speed_25MHz    ///< 低速模式
#define DAC904_GPIO_SPEED_MEDIUM    GPIO_Speed_50MHz    ///< 中速模式  
#define DAC904_GPIO_SPEED_HIGH      GPIO_Speed_100MHz   ///< 高速模式
/**
  * @}
  */

/** @defgroup DAC904_Timer_Config 定时器配置
  * @{
  */
#define DAC904_TIMER                TIM2        ///< 使用TIM2 (32位定时器)
#define DAC904_TIMER_CLK            RCC_APB1Periph_TIM2
#define DAC904_TIMER_IRQ            TIM2_IRQn
/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup DAC904_Macros 操作宏
  * @{
  */

/**
  * @brief  设置DAC904时钟信号为高电平
  */
#define DAC904_CLK_HIGH()           GPIO_SetBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN)

/**
  * @brief  设置DAC904时钟信号为低电平
  */
#define DAC904_CLK_LOW()            GPIO_ResetBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN)

/**
  * @brief  切换DAC904时钟信号
  */
#define DAC904_CLK_TOGGLE()         GPIO_ToggleBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN)

/**
  * @brief  快速设置DAC904并行数据
  * @param  data: 14位数据值 (0-16383)
  */
#define DAC904_SET_DATA_FAST(data)  do { \
    uint32_t temp_odr = DAC904_DATA_PORT->ODR; \
    temp_odr &= ~DAC904_DATA_PINS_MASK; \
    temp_odr |= ((data) & DAC904_DATA_MAX); \
    DAC904_DATA_PORT->ODR = temp_odr; \
} while(0)

/**
  * @brief  检查数据值是否有效
  * @param  data: 数据值
  * @retval true: 有效, false: 无效
  */
#define DAC904_IS_VALID_DATA(data)  ((data) <= DAC904_DATA_MAX)

/**
  * @brief  将电压值转换为DAC数据
  * @param  voltage: 电压值 (0.0V - 5.0V)
  * @retval DAC数据值
  */
#define DAC904_VOLTAGE_TO_DATA(voltage) \
    ((uint16_t)(((voltage) * DAC904_DATA_MAX) / 5.0f))

/**
  * @brief  将DAC数据转换为电压值
  * @param  data: DAC数据值
  * @retval 电压值
  */
#define DAC904_DATA_TO_VOLTAGE(data) \
    (((float)(data) * 5.0f) / DAC904_DATA_MAX)

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/
extern DAC904_Handle_t g_dac904_handle;

/* Exported functions --------------------------------------------------------*/

/** @defgroup DAC904_Functions DAC904驱动函数
  * @{
  */

/**
  * @brief  DAC904初始化
  * @param  config: 配置参数指针
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_Init(DAC904_Config_t* config);

/**
  * @brief  DAC904反初始化
  * @param  None
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_DeInit(void);

/**
  * @brief  写入数据到DAC904
  * @param  data: 14位数据值 (0-16383)
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_WriteData(uint16_t data);

/**
  * @brief  设置DAC904输出电压
  * @param  voltage: 输出电压值 (0.0V - 5.0V)
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_SetVoltage(float voltage);

/**
  * @brief  启动DAC904定时器模式
  * @param  frequency: 更新频率 (Hz)
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_StartTimer(uint32_t frequency);

/**
  * @brief  停止DAC904定时器模式
  * @param  None
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_StopTimer(void);

/**
  * @brief  获取DAC904状态
  * @param  None
  * @retval DAC904_Handle_t* 句柄指针
  */
DAC904_Handle_t* DAC904_GetHandle(void);

/**
  * @brief  DAC904定时器中断回调函数
  * @param  None
  * @retval None
  * @note   此函数需要在定时器中断中调用
  */
void DAC904_TimerCallback(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __DAC904_H */

/************************ (C) COPYRIGHT DAC904移植项目组 *****END OF FILE****/
