/**
  ******************************************************************************
  * @file    dac904_types.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   DAC904通用类型定义
  ******************************************************************************
  */

#ifndef __DAC904_TYPES_H
#define __DAC904_TYPES_H

#ifdef __cplusplus
extern "C" {
#endif

/* Type definitions ----------------------------------------------------------*/
#ifndef __cplusplus
#ifndef _BOOL_DEFINED
#define _BOOL_DEFINED
typedef enum { false = 0, true = !false } bool;
#endif
#endif

#ifndef NULL
#define NULL ((void*)0)
#endif

#ifdef __cplusplus
}
#endif

#endif /* __DAC904_TYPES_H */

/************************ (C) COPYRIGHT DAC904移植项目组 *****END OF FILE****/
