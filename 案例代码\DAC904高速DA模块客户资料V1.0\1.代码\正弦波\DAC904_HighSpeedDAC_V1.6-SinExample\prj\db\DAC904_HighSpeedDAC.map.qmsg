{ "Info" "IQEXE_SEPARATOR" "" "Info: *******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus II 64-Bit " "Info: Running Quartus II 64-Bit Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version " "Info: Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_START_BANNER_TIME" "Tue Jul 25 11:30:31 2023 " "Info: Processing started: Tue Jul 25 11:30:31 2023" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "" 0 -1}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC " "Info: Command: quartus_map --read_settings_files=on --write_settings_files=off DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC" {  } {  } 0 0 "Command: %1!s!" 0 0 "" 0 -1}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "6 6 " "Info: Parallel compilation is enabled and will use 6 of the 6 processors detected" {  } {  } 0 0 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "" 0 -1}
{ "Warning" "WVRFX_VERI_IGNORED_ANONYMOUS_PORT" "PhaseAccumulator PhaseAccumulator.V(17) " "Warning (10238): Verilog Module Declaration warning at PhaseAccumulator.V(17): ignored anonymous port(s) indicated by duplicate or dangling comma(s) in the port list for module \"PhaseAccumulator\"" {  } { { "../rtl/PhaseAccumulator.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PhaseAccumulator.V" 17 0 0 } }  } 0 10238 "Verilog Module Declaration warning at %2!s!: ignored anonymous port(s) indicated by duplicate or dangling comma(s) in the port list for module \"%1!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/phaseaccumulator.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/phaseaccumulator.v" { { "Info" "ISGN_ENTITY_NAME" "1 PhaseAccumulator " "Info: Found entity 1: PhaseAccumulator" {  } { { "../rtl/PhaseAccumulator.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PhaseAccumulator.V" 11 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Warning" "WVRFX_L2_VERI_INGORE_DANGLING_COMMA" "DAC904_WriteTEST.V(59) " "Warning (10275): Verilog HDL Module Instantiation warning at DAC904_WriteTEST.V(59): ignored dangling comma in List of Port Connections" {  } { { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 59 0 0 } }  } 0 10275 "Verilog HDL Module Instantiation warning at %1!s!: ignored dangling comma in List of Port Connections" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/dac904_writetest.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/dac904_writetest.v" { { "Info" "ISGN_ENTITY_NAME" "1 DAC904_WriteTEST " "Info: Found entity 1: DAC904_WriteTEST" {  } { { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 1 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/dac904_writemodule.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/dac904_writemodule.v" { { "Info" "ISGN_ENTITY_NAME" "1 DAC904_WriteModule " "Info: Found entity 1: DAC904_WriteModule" {  } { { "../rtl/DAC904_WriteModule.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteModule.V" 1 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/rom/rom_14b_sin/rom_14b_sin.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/rom/rom_14b_sin/rom_14b_sin.v" { { "Info" "ISGN_ENTITY_NAME" "1 rom_14B_Sin " "Info: Found entity 1: rom_14B_Sin" {  } { { "../rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" 39 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/pll/pll165m/pll165m.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file /ddisk/fpga/quartus/kv/dac904_highspeeddac/dac904_highspeeddac_v1.6-sinexample/rtl/pll/pll165m/pll165m.v" { { "Info" "ISGN_ENTITY_NAME" "1 pll165m " "Info: Found entity 1: pll165m" {  } { { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 39 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Warning" "WVRFX_L2_VERI_CREATED_IMPLICIT_NET" "pll_65M DAC904_WriteTEST.V(18) " "Warning (10236): Verilog HDL Implicit Net warning at DAC904_WriteTEST.V(18): created implicit net for \"pll_65M\"" {  } { { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 18 0 0 } }  } 0 10236 "Verilog HDL Implicit Net warning at %2!s!: created implicit net for \"%1!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_TOP" "DAC904_WriteTEST " "Info: Elaborating entity \"DAC904_WriteTEST\" for the top level hierarchy" {  } {  } 0 0 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "pll165m pll165m:U0_pll165m_inst " "Info: Elaborating entity \"pll165m\" for hierarchy \"pll165m:U0_pll165m_inst\"" {  } { { "../rtl/DAC904_WriteTEST.V" "U0_pll165m_inst" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 21 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altpll pll165m:U0_pll165m_inst\|altpll:altpll_component " "Info: Elaborating entity \"altpll\" for hierarchy \"pll165m:U0_pll165m_inst\|altpll:altpll_component\"" {  } { { "../rtl/PLL/pll165m/pll165m.v" "altpll_component" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_ELABORATION_HEADER" "pll165m:U0_pll165m_inst\|altpll:altpll_component " "Info: Elaborated megafunction instantiation \"pll165m:U0_pll165m_inst\|altpll:altpll_component\"" {  } { { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } }  } 0 0 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "pll165m:U0_pll165m_inst\|altpll:altpll_component " "Info: Instantiated megafunction \"pll165m:U0_pll165m_inst\|altpll:altpll_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "bandwidth_type AUTO " "Info: Parameter \"bandwidth_type\" = \"AUTO\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_divide_by 25 " "Info: Parameter \"clk0_divide_by\" = \"25\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_duty_cycle 50 " "Info: Parameter \"clk0_duty_cycle\" = \"50\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_multiply_by 33 " "Info: Parameter \"clk0_multiply_by\" = \"33\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_phase_shift 0 " "Info: Parameter \"clk0_phase_shift\" = \"0\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_divide_by 10 " "Info: Parameter \"clk1_divide_by\" = \"10\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_duty_cycle 50 " "Info: Parameter \"clk1_duty_cycle\" = \"50\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_multiply_by 33 " "Info: Parameter \"clk1_multiply_by\" = \"33\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_phase_shift 0 " "Info: Parameter \"clk1_phase_shift\" = \"0\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "compensate_clock CLK0 " "Info: Parameter \"compensate_clock\" = \"CLK0\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "inclk0_input_frequency 20000 " "Info: Parameter \"inclk0_input_frequency\" = \"20000\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Info: Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint CBX_MODULE_PREFIX=pll165m " "Info: Parameter \"lpm_hint\" = \"CBX_MODULE_PREFIX=pll165m\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altpll " "Info: Parameter \"lpm_type\" = \"altpll\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode NORMAL " "Info: Parameter \"operation_mode\" = \"NORMAL\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "pll_type AUTO " "Info: Parameter \"pll_type\" = \"AUTO\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_activeclock PORT_UNUSED " "Info: Parameter \"port_activeclock\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_areset PORT_USED " "Info: Parameter \"port_areset\" = \"PORT_USED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad0 PORT_UNUSED " "Info: Parameter \"port_clkbad0\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad1 PORT_UNUSED " "Info: Parameter \"port_clkbad1\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkloss PORT_UNUSED " "Info: Parameter \"port_clkloss\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkswitch PORT_UNUSED " "Info: Parameter \"port_clkswitch\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_configupdate PORT_UNUSED " "Info: Parameter \"port_configupdate\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_fbin PORT_UNUSED " "Info: Parameter \"port_fbin\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk0 PORT_USED " "Info: Parameter \"port_inclk0\" = \"PORT_USED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk1 PORT_UNUSED " "Info: Parameter \"port_inclk1\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_locked PORT_USED " "Info: Parameter \"port_locked\" = \"PORT_USED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pfdena PORT_UNUSED " "Info: Parameter \"port_pfdena\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasecounterselect PORT_UNUSED " "Info: Parameter \"port_phasecounterselect\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasedone PORT_UNUSED " "Info: Parameter \"port_phasedone\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasestep PORT_UNUSED " "Info: Parameter \"port_phasestep\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phaseupdown PORT_UNUSED " "Info: Parameter \"port_phaseupdown\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pllena PORT_UNUSED " "Info: Parameter \"port_pllena\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanaclr PORT_UNUSED " "Info: Parameter \"port_scanaclr\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclk PORT_UNUSED " "Info: Parameter \"port_scanclk\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclkena PORT_UNUSED " "Info: Parameter \"port_scanclkena\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandata PORT_UNUSED " "Info: Parameter \"port_scandata\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandataout PORT_UNUSED " "Info: Parameter \"port_scandataout\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandone PORT_UNUSED " "Info: Parameter \"port_scandone\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanread PORT_UNUSED " "Info: Parameter \"port_scanread\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanwrite PORT_UNUSED " "Info: Parameter \"port_scanwrite\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk0 PORT_USED " "Info: Parameter \"port_clk0\" = \"PORT_USED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk1 PORT_USED " "Info: Parameter \"port_clk1\" = \"PORT_USED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk2 PORT_UNUSED " "Info: Parameter \"port_clk2\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk3 PORT_UNUSED " "Info: Parameter \"port_clk3\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk4 PORT_UNUSED " "Info: Parameter \"port_clk4\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk5 PORT_UNUSED " "Info: Parameter \"port_clk5\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena0 PORT_UNUSED " "Info: Parameter \"port_clkena0\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena1 PORT_UNUSED " "Info: Parameter \"port_clkena1\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena2 PORT_UNUSED " "Info: Parameter \"port_clkena2\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena3 PORT_UNUSED " "Info: Parameter \"port_clkena3\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena4 PORT_UNUSED " "Info: Parameter \"port_clkena4\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena5 PORT_UNUSED " "Info: Parameter \"port_clkena5\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk0 PORT_UNUSED " "Info: Parameter \"port_extclk0\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk1 PORT_UNUSED " "Info: Parameter \"port_extclk1\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk2 PORT_UNUSED " "Info: Parameter \"port_extclk2\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk3 PORT_UNUSED " "Info: Parameter \"port_extclk3\" = \"PORT_UNUSED\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "self_reset_on_loss_lock OFF " "Info: Parameter \"self_reset_on_loss_lock\" = \"OFF\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_clock 5 " "Info: Parameter \"width_clock\" = \"5\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1}  } { { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } }  } 0 0 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/pll165m_altpll.v 1 1 " "Info: Found 1 design units, including 1 entities, in source file db/pll165m_altpll.v" { { "Info" "ISGN_ENTITY_NAME" "1 pll165m_altpll " "Info: Found entity 1: pll165m_altpll" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 30 -1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "pll165m_altpll pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated " "Info: Elaborating entity \"pll165m_altpll\" for hierarchy \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\"" {  } { { "altpll.tdf" "auto_generated" { Text "c:/altera/11.0/quartus/libraries/megafunctions/altpll.tdf" 897 3 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "PhaseAccumulator PhaseAccumulator:U1_PhaseAccumulator " "Info: Elaborating entity \"PhaseAccumulator\" for hierarchy \"PhaseAccumulator:U1_PhaseAccumulator\"" {  } { { "../rtl/DAC904_WriteTEST.V" "U1_PhaseAccumulator" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 59 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "rom_14B_Sin rom_14B_Sin:U2_rom_14B_Sin " "Info: Elaborating entity \"rom_14B_Sin\" for hierarchy \"rom_14B_Sin:U2_rom_14B_Sin\"" {  } { { "../rtl/DAC904_WriteTEST.V" "U2_rom_14B_Sin" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 74 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component " "Info: Elaborating entity \"altsyncram\" for hierarchy \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\"" {  } { { "../rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" "altsyncram_component" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" 81 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_ELABORATION_HEADER" "rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component " "Info: Elaborated megafunction instantiation \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\"" {  } { { "../rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" 81 0 0 } }  } 0 0 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component " "Info: Instantiated megafunction \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Info: Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Info: Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Info: Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file ../../ROM/ROM_14B_SIN/ROM_14B_SIN.mif " "Info: Parameter \"init_file\" = \"../../ROM/ROM_14B_SIN/ROM_14B_SIN.mif\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Info: Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Info: Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Info: Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 16384 " "Info: Parameter \"numwords_a\" = \"16384\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Info: Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Info: Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a CLOCK0 " "Info: Parameter \"outdata_reg_a\" = \"CLOCK0\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 14 " "Info: Parameter \"widthad_a\" = \"14\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Info: Parameter \"width_a\" = \"14\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Info: Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 0 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "" 0 -1}  } { { "../rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/ROM/ROM_14B_SIN/rom_14B_Sin.v" 81 0 0 } }  } 0 0 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_vhb1.tdf 1 1 " "Info: Found 1 design units, including 1 entities, in source file db/altsyncram_vhb1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_vhb1 " "Info: Found entity 1: altsyncram_vhb1" {  } { { "db/altsyncram_vhb1.tdf" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/altsyncram_vhb1.tdf" 31 1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_vhb1 rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated " "Info: Elaborating entity \"altsyncram_vhb1\" for hierarchy \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "c:/altera/11.0/quartus/libraries/megafunctions/altsyncram.tdf" 790 4 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/decode_c8a.tdf 1 1 " "Info: Found 1 design units, including 1 entities, in source file db/decode_c8a.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 decode_c8a " "Info: Found entity 1: decode_c8a" {  } { { "db/decode_c8a.tdf" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/decode_c8a.tdf" 22 1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "decode_c8a rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated\|decode_c8a:rden_decode " "Info: Elaborating entity \"decode_c8a\" for hierarchy \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated\|decode_c8a:rden_decode\"" {  } { { "db/altsyncram_vhb1.tdf" "rden_decode" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/altsyncram_vhb1.tdf" 40 2 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/mux_gob.tdf 1 1 " "Info: Found 1 design units, including 1 entities, in source file db/mux_gob.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 mux_gob " "Info: Found entity 1: mux_gob" {  } { { "db/mux_gob.tdf" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/mux_gob.tdf" 22 1 0 } }  } 0 0 "Found entity %1!d!: %2!s!" 0 0 "" 0 -1}  } {  } 0 0 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "mux_gob rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated\|mux_gob:mux2 " "Info: Elaborating entity \"mux_gob\" for hierarchy \"rom_14B_Sin:U2_rom_14B_Sin\|altsyncram:altsyncram_component\|altsyncram_vhb1:auto_generated\|mux_gob:mux2\"" {  } { { "db/altsyncram_vhb1.tdf" "mux2" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/altsyncram_vhb1.tdf" 41 2 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "DAC904_WriteModule DAC904_WriteModule:U3_DAC904_WriteModule " "Info: Elaborating entity \"DAC904_WriteModule\" for hierarchy \"DAC904_WriteModule:U3_DAC904_WriteModule\"" {  } { { "../rtl/DAC904_WriteTEST.V" "U3_DAC904_WriteModule" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 93 0 0 } }  } 0 0 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "" 0 -1}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Info: Timing-Driven Synthesis is running" {  } {  } 0 0 "Timing-Driven Synthesis is running" 0 0 "" 0 -1}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Info: Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 " "Info: Adding node \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\"" {  } {  } 0 0 "Adding node \"%1!s!\"" 0 0 "" 0 -1}  } {  } 0 0 "Generating hard_block partition \"%1!s!\"" 0 0 "" 0 -1}
{ "Warning" "WCUT_PLL_COMPENSATE_CLOCK_NOT_CONNECTED" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 compensate_clock clock0 CLK\[0\] " "Warning: PLL \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\" has parameter compensate_clock set to clock0 but port CLK\[0\] is not connected" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 50 -1 0 } } { "altpll.tdf" "" { Text "c:/altera/11.0/quartus/libraries/megafunctions/altpll.tdf" 897 0 0 } } { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } } { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 21 0 0 } }  } 0 0 "PLL \"%1!s!\" has parameter %2!s! set to %3!s! but port %4!s! is not connected" 0 0 "" 0 -1}
{ "Warning" "WCUT_PLL_MULT_DIV_SPECIFIED_CLOCK_NOT_CONNECTED" "pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1 CLK\[0\] clk0_multiply_by clk0_divide_by " "Warning: PLL \"pll165m:U0_pll165m_inst\|altpll:altpll_component\|pll165m_altpll:auto_generated\|pll1\" has parameters clk0_multiply_by and clk0_divide_by specified but port CLK\[0\] is not connected" {  } { { "db/pll165m_altpll.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/prj/db/pll165m_altpll.v" 50 -1 0 } } { "altpll.tdf" "" { Text "c:/altera/11.0/quartus/libraries/megafunctions/altpll.tdf" 897 0 0 } } { "../rtl/PLL/pll165m/pll165m.v" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/PLL/pll165m/pll165m.v" 107 0 0 } } { "../rtl/DAC904_WriteTEST.V" "" { Text "D:/Ddisk/FPGA/quartus/KV/DAC904_HighSpeedDAC/DAC904_HighSpeedDAC_V1.6-SinExample/rtl/DAC904_WriteTEST.V" 21 0 0 } }  } 0 0 "PLL \"%1!s!\" has parameters %3!s! and %4!s! specified but port %2!s! is not connected" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_TM_SUMMARY" "92 " "Info: Implemented 92 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "2 " "Info: Implemented 2 input pins" {  } {  } 0 0 "Implemented %1!d! input pins" 0 0 "" 0 -1} { "Info" "ICUT_CUT_TM_OPINS" "15 " "Info: Implemented 15 output pins" {  } {  } 0 0 "Implemented %1!d! output pins" 0 0 "" 0 -1} { "Info" "ICUT_CUT_TM_LCELLS" "46 " "Info: Implemented 46 logic cells" {  } {  } 0 0 "Implemented %1!d! logic cells" 0 0 "" 0 -1} { "Info" "ICUT_CUT_TM_RAMS" "28 " "Info: Implemented 28 RAM segments" {  } {  } 0 0 "Implemented %1!d! RAM segments" 0 0 "" 0 -1} { "Info" "ICUT_CUT_TM_PLLS" "1 " "Info: Implemented 1 PLLs" {  } {  } 0 0 "Implemented %1!d! PLLs" 0 0 "" 0 -1}  } {  } 0 0 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "" 0 -1}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 5 s Quartus II 64-Bit " "Info: Quartus II 64-Bit Analysis & Synthesis was successful. 0 errors, 5 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4498 " "Info: Peak virtual memory: 4498 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "" 0 -1} { "Info" "IQEXE_END_BANNER_TIME" "Tue Jul 25 11:30:33 2023 " "Info: Processing ended: Tue Jul 25 11:30:33 2023" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_TIME" "00:00:02 " "Info: Elapsed time: 00:00:02" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Info: Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "" 0 -1}
