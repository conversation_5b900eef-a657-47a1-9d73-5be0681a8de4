{ "Info" "IQEXE_SEPARATOR" "" "Info: *******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "TimeQuest Timing Analyzer Quartus II 64-Bit " "Info: Running Quartus II 64-Bit TimeQuest Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version " "Info: Version 11.0 Build 208 07/03/2011 Service Pack 1 SJ Full Version" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_START_BANNER_TIME" "Tue Jul 25 11:30:37 2023 " "Info: Processing started: Tue Jul 25 11:30:37 2023" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "" 0 -1}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "" 0 -1}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC " "Info: Command: quartus_sta DAC904_HighSpeedDAC -c DAC904_HighSpeedDAC" {  } {  } 0 0 "Command: %1!s!" 0 0 "" 0 -1}
{ "Info" "0" "" "Info: qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "" 0 -1}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "6 6 " "Info: Parallel compilation is enabled and will use 6 of the 6 processors detected" {  } {  } 0 0 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Info: Low junction temperature is 0 degrees C" {  } {  } 0 0 "%1!s! is %2!s!" 0 0 "" 0 -1}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "Info: High junction temperature is 85 degrees C" {  } {  } 0 0 "%1!s! is %2!s!" 0 0 "" 0 -1}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "DAC904_HighSpeedDAC.sdc " "Critical Warning: Synopsys Design Constraints File file not found: 'DAC904_HighSpeedDAC.sdc'. A Synopsys Design Constraints File is required by the TimeQuest Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 0 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the TimeQuest Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "generated clocks \"derive_pll_clocks -create_base_clocks\" " "Info: No user constrained generated clocks found in the design. Calling \"derive_pll_clocks -create_base_clocks\"" {  } {  } 0 0 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL Clocks " "Info: Deriving PLL Clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_clock -period 20.000 -waveform \{0.000 10.000\} -name clk clk " "Info: create_clock -period 20.000 -waveform \{0.000 10.000\} -name clk clk" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 10 -multiply_by 33 -duty_cycle 50.00 -name \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\} \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\} " "Info: create_generated_clock -source \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 10 -multiply_by 33 -duty_cycle 50.00 -name \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\} \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "base clocks \"derive_clocks -period 1.0\" " "Info: No user constrained base clocks found in the design. Calling \"derive_clocks -period 1.0\"" {  } {  } 0 0 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_CLOCKS_FOUND_NO_CLOCKS" "" "Info: The command derive_clocks did not find any clocks to derive.  No clocks were created or changed." {  } {  } 0 0 "The command derive_clocks did not find any clocks to derive.  No clocks were created or changed." 0 0 "" 0 -1}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "Info: No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 0 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty " "Info: Deriving Clock Uncertainty" { { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}
{ "Info" "0" "" "Info: Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "" 0 -1}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Critical Warning: Timing requirements not met" {  } {  } 1 0 "Timing requirements not met" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -0.485 " "Info: Worst-case setup slack is -0.485" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.485        -1.974 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:    -0.485        -1.974 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.463 " "Info: Worst-case hold slack is 0.463" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.463         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     0.463         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "Info: No Recovery paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "Info: No Removal paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.859 " "Info: Worst-case minimum pulse width slack is 1.859" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.859         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     1.859         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.931         0.000 clk  " "Info:     9.931         0.000 clk " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "0" "" "Info: Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_STARTED" "" "Info: Started post-fitting delay annotation" {  } {  } 0 0 "Started post-fitting delay annotation" 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Info: Delay annotation completed successfully" {  } {  } 0 0 "Delay annotation completed successfully" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty " "Info: Deriving Clock Uncertainty" { { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Critical Warning: Timing requirements not met" {  } {  } 1 0 "Timing requirements not met" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -0.281 " "Info: Worst-case setup slack is -0.281" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.281        -0.678 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:    -0.281        -0.678 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.418 " "Info: Worst-case hold slack is 0.418" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.418         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     0.418         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "Info: No Recovery paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "Info: No Removal paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.859 " "Info: Worst-case minimum pulse width slack is 1.859" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.859         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     1.859         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.943         0.000 clk  " "Info:     9.943         0.000 clk " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "0" "" "Info: Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_STARTED" "" "Info: Started post-fitting delay annotation" {  } {  } 0 0 "Started post-fitting delay annotation" 0 0 "" 0 -1}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Info: Delay annotation completed successfully" {  } {  } 0 0 "Delay annotation completed successfully" 0 0 "" 0 -1}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty " "Info: Deriving Clock Uncertainty" { { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -setup 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -rise_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -rise_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020 " "Info: set_clock_uncertainty -fall_from \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -fall_to \[get_clocks \{U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]\}\] -hold 0.020" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 1.337 " "Info: Worst-case setup slack is 1.337" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.337         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     1.337         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.168 " "Info: Worst-case hold slack is 0.168" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.168         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     0.168         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "Info: No Recovery paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "Info: No Removal paths to report" {  } {  } 0 0 "No %1!s! paths to report" 0 0 "" 0 -1}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 2.778 " "Info: Worst-case minimum pulse width slack is 2.778" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack End Point TNS Clock  " "Info:     Slack End Point TNS Clock " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= ============= ===================== " "Info: ========= ============= =====================" {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.778         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\]  " "Info:     2.778         0.000 U0_pll165m_inst\|altpll_component\|auto_generated\|pll1\|clk\[1\] " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.589         0.000 clk  " "Info:     9.589         0.000 clk " {  } {  } 0 0 "%1!s!" 0 0 "" 0 -1}  } {  } 0 0 "Worst-case %1!s! slack is %2!s!" 0 0 "" 0 -1}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Info: Design is not fully constrained for setup requirements" {  } {  } 0 0 "Design is not fully constrained for %1!s! requirements" 0 0 "" 0 -1}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Info: Design is not fully constrained for hold requirements" {  } {  } 0 0 "Design is not fully constrained for %1!s! requirements" 0 0 "" 0 -1}
{ "Info" "IQEXE_ERROR_COUNT" "TimeQuest Timing Analyzer 0 s 3 s Quartus II 64-Bit " "Info: Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 3 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4537 " "Info: Peak virtual memory: 4537 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "" 0 -1} { "Info" "IQEXE_END_BANNER_TIME" "Tue Jul 25 11:30:39 2023 " "Info: Processing ended: Tue Jul 25 11:30:39 2023" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_TIME" "00:00:02 " "Info: Elapsed time: 00:00:02" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "" 0 -1} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:02 " "Info: Total CPU time (on all processors): 00:00:02" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "" 0 -1}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "" 0 -1}
